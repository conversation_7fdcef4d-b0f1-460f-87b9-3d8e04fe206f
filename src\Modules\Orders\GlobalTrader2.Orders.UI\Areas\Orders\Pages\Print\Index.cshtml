@page
@using GlobalTrader2.Dto.Templates
@using GlobalTrader2.Orders.UserCases.Orders.GetPrintEmailLog.PrintEmailLog.Queries
@using GlobalTrader2.Orders.UserCases.Orders.BOM.Queries
@using GlobalTrader2.Orders.UserCases.Orders.BOM.HUBRFQForMail.Queries.Dtos
@using GlobalTrader2.Core.Interfaces
@using GlobalTrader2.SharedUI.Helper
@using GlobalTrader2.SharedUI.Interfaces
@using GlobalTrader2.SharedUI.Services
@using Microsoft.Extensions.Localization
@model GlobalTrader2.Orders.UI.Areas.Orders.Pages.Print.IndexModel
@inject IMediator _mediator;
@inject IMapper _mapper;
@inject IWebResourceManager WebResourceManager;
@inject IStringLocalizer<GlobalTrader2.SharedUI.Printing> _printingLocalizer;
@inject SettingManager _settingManager
@inject SessionManager _sessionManager
@inject IRazorViewToStringService _razorViewToStringService

@{
    Layout = "_FormLayout";
    var separator = "¦¦";
}
<link rel="stylesheet" href="~/css/print-log.css" asp-append-version="true" />
@if (Model.PrintObject == ListForPrint.PrintLog)
{
    var dtos = await _mediator.Send(new GetAllPrintEmailLogQuery()
    {
        SectionName = Model.Section,
        DocumentNo = Model.GenericID.Value
    });
    var model = _mapper.Map<IList<PrintDocumentLogTemplate>>(dtos.Data);
    ViewData["Title"] = model.Count > 0 ? _printingLocalizer["DOCNO"] : "Print/Email Log";

    @await Html.PartialAsync("/Areas/Common/Pages/Shared/Templates/PrintEmailLog.cshtml", model)
}
else if ((Model.PrintObject == ListForPrint.PrintHUBRFQ || Model.PrintObject == ListForPrint.EmailHUBRFQ) && Model.GenericID.HasValue)
{
    var hubrfqTitle = _printingLocalizer["HUBRFQ"];

    var bom = await _mediator.Send(new GetBOMDetailsQuery
    {
        Id = Model.GenericID.Value,
        LoginId = _sessionManager.LoginID ?? 0
    });

    var customerRequirements = await _mediator.Send(new GetAllHUBRFQForMailQuery
    {
        BOMNo = Model.GenericID.Value,
        ClientID = _sessionManager.ClientID ?? 0
    });

    if (bom.Success && bom.Data != null && customerRequirements.Success)
    {
        var templateModel = new HubRrqPrintTemplate
        {
            Code = $"{bom.Data.Code}( {_sessionManager.ClientName ?? string.Empty} )",
            Name = bom.Data.Name ?? string.Empty,
            Company = bom.Data.Company ?? string.Empty,
            Contact = bom.Data.ContactName ?? string.Empty,
            Currency = bom.Data.CurrencyCode ?? string.Empty,
            QuoteRequired = bom.Data.QuoteRequired ?? DateTime.Now,
            Notes = Functions.ReplaceLineBreaks(bom.Data.Notes ?? string.Empty),
            IsPoHub = _sessionManager.IsPOHub,
            CustomerRequirements = customerRequirements.Data?.Select(dto => new HubRrqForMailTemplate
            {
                CustomerRequirementNumber = dto.CustomerRequirementNumber,
                CustomerRequirementId = dto.CustomerRequirementId,
                Quantity = dto.Quantity,
                CustomerPart = dto.CustomerPart,
                Part = dto.Part,
                DateCode = dto.DateCode,
                ManufacturerCode = dto.ManufacturerCode,
                PackageName = dto.PackageName,
                ProductName = dto.ProductName,
                CompanyName = dto.CompanyName,
                ClientName = dto.ClientName,
                DatePromised = dto.DatePromised,
                ConvertedTargetValue = dto.ConvertedTargetValue,
                CurrencyNo = dto.CurrencyNo,
                BOMCurrencyCode = dto.BOMCurrencyCode,
                SalesmanName = dto.SalesmanName,
                Instructions = dto.Instructions,
                MSL = dto.MSL,
                FactorySealed = dto.FactorySealed,
                AlternativesAccepted = dto.AlternativesAccepted,
                RepeatBusiness = dto.RepeatBusiness,
                ReqTypeText = dto.ReqTypeText,
                ReqForTraceabilityText = dto.ReqForTraceabilityText,
                LyticaManufacturerRef = dto.LyticaManufacturerRef,
                LyticaAveragePrice = dto.LyticaAveragePrice,
                LyticaTargetPrice = dto.LyticaTargetPrice,
                LyticaMarketLeading = dto.LyticaMarketLeading
            }).ToList() ?? new List<HubRrqForMailTemplate>()
        };

        ViewData["Title"] = hubrfqTitle;

        @await Html.PartialAsync("/Areas/Common/Pages/Shared/Templates/_HUBRFQPrint.cshtml", templateModel)
    }
}

@section Scripts {
    <environment include="Development">
        <script type="module" src="/js/modules/orders/print/print.js" asp-append-version="true"></script>
    </environment>
    <environment exclude="Development">
        <script type="module" src="/dist/js/orders-print.bundle.js" asp-append-version="true"></script>
    </environment>
    <script>
        const printLogSeparator = '@Html.Raw(separator)';
    </script>
}

