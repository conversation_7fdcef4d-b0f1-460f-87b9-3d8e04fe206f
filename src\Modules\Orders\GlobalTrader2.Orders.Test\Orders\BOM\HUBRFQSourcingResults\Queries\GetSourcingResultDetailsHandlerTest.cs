using AutoFixture;
using GlobalTrader2.Core;
using GlobalTrader2.Core.Domain.Entities;
using GlobalTrader2.Core.StoreName;
using GlobalTrader2.Dto.Sourcing;
using GlobalTrader2.Orders.UserCases.Orders.BOM.HUBRFQSourcingResults.Query;
using Microsoft.Data.SqlClient;
using Moq;
using System.Data;

namespace GlobalTrader2.Orders.UserCases.Test.Orders.BOM.HUBRFQSourcingResults.Queries
{
    public class GetSourcingResultDetailsHandlerTest
    {
        private readonly GetSourcingResultDetailsHandler _handler;
        private readonly Mock<IBaseRepository<SourcingResultDetail>> _baseRepository;
        private readonly Fixture _fixture;

        public GetSourcingResultDetailsHandlerTest()
        {
            _baseRepository = new Mock<IBaseRepository<SourcingResultDetail>>();
            _fixture = new Fixture();
            _handler = new GetSourcingResultDetailsHandler(_baseRepository.Object);
        }

        [Fact]
        public async Task Handle_WithValidSourcingResultId_ReturnsSuccessResponse()
        {
            // Arrange
            var query = new GetSourcingResultDetailsQuery { SourcingResultId = 123 };
            var mockSourcingResultDetail = new SourcingResultDetail
            {
                SourcingResultId = 123,
                CustomerRequirementNo = 456,
                SourcingTable = "PQ",
                FullPart = "ABC123-FULL",
                Part = "ABC123",
                ManufacturerNo = 789,
                ManufacturerName = "Test Manufacturer",
                DateCode = "2023A",
                ProductNo = 101,
                ProductDescription = "Test Product",
                ProductInactive = false,
                PackageNo = 202,
                PackageDescription = "SOIC-8",
                Quantity = 1000,
                Price = 15.99,
                CurrencyNo = 1,
                CurrencyCode = "USD",
                SupplierNo = 303,
                SupplierName = "Test Supplier",
                SupplierNotes = "Supplier notes",
                ROHS = 1,
                ROHSStatus = "Compliant",
                FactorySealed = "Yes",
                OfferStatusNo = 1,
                Notes = "Test notes",
                SPQ = "100",
                LeadTime = "12 weeks",
                MSL = "Level 3",
                MSLLevelNo = 3,
                SupplierMOQ = "500",
                SupplierTotalQSA = 10000,
                SupplierLTB = "8 weeks",
                regionNo = 1,
                partWatchMatch = true,
                UPLiftPrice = 5.5,
                EstimatedShippingCost = 25.0,
                DeliveryDate = DateTime.Now.AddDays(30),
                SupplierWarranty = 12,
                NonPreferredCompany = false,
                CountryOfOriginNo = 1,
                IHSCountryOfOriginNo = 2,
                IHSCountryOfOriginName = "USA",
                TypeOfSupplierNo = 1,
                ReasonForSupplierNo = 2,
                RiskOfSupplierNo = 1,
                CountryNo = 1,
                CountryName = "United States",
                SellPriceLessReason = "Volume discount",
                IsTestingRecommended = true,
                SupplierPrice = 12.50
            };

            _baseRepository.Setup(x => x.SqlQueryRawAsync(It.IsAny<string>(), It.IsAny<SqlParameter[]>()))
                          .ReturnsAsync(new List<SourcingResultDetail> { mockSourcingResultDetail });

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            Assert.True(result.Success);
            Assert.NotNull(result.Data);
            Assert.Equal(123, result.Data.SourcingResultId);
            Assert.Equal(456, result.Data.CustomerRequirementNo);
            Assert.Equal("PQ", result.Data.SourcingTable);
            Assert.Equal("ABC123-FULL", result.Data.FullPart);
            Assert.Equal("ABC123", result.Data.Part);
            Assert.Equal(789, result.Data.ManufacturerNo);
            Assert.Equal("Test Manufacturer", result.Data.ManufacturerName);
            Assert.Equal("2023A", result.Data.DateCode);
            Assert.Equal(101, result.Data.ProductNo);
            Assert.Equal("Test Product", result.Data.ProductDescription);
            Assert.False(result.Data.ProductInactive);
            Assert.Equal(202, result.Data.PackageNo);
            Assert.Equal("SOIC-8", result.Data.PackageDescription);
            Assert.Equal(1000, result.Data.Quantity);
            Assert.Equal(15.99, result.Data.Price);
            Assert.Equal(1, result.Data.CurrencyNo);
            Assert.Equal("USD", result.Data.CurrencyCode);
            Assert.Equal(303, result.Data.SupplierNo);
            Assert.Equal("Test Supplier", result.Data.SupplierName);
            Assert.Equal("Supplier notes", result.Data.SupplierNotes);
            Assert.Equal((byte)1, result.Data.ROHS);
            Assert.Equal("Compliant", result.Data.ROHSStatus);
            Assert.Equal("Yes", result.Data.FactorySealed);
            Assert.Equal(1, result.Data.OfferStatusNo);
            Assert.Equal("Test notes", result.Data.Notes);
            Assert.Equal("100", result.Data.SPQ);
            Assert.Equal("12 weeks", result.Data.LeadTime);
            Assert.Equal("Level 3", result.Data.MSL);
            Assert.Equal(3, result.Data.MSLLevelNo);
            Assert.Equal("500", result.Data.SupplierMOQ);
            Assert.Equal(10000, result.Data.SupplierTotalQSA);
            Assert.Equal("8 weeks", result.Data.SupplierLTB);
            Assert.Equal(1, result.Data.regionNo);
            Assert.True(result.Data.PartWatchMatch);
            Assert.Equal(5.5, result.Data.UPLiftPrice);
            Assert.Equal(25.0, result.Data.EstimatedShippingCost);
            Assert.Equal(mockSourcingResultDetail.DeliveryDate, result.Data.DeliveryDate);
            Assert.Equal(12, result.Data.SupplierWarranty);
            Assert.False(result.Data.NonPreferredCompany);
            Assert.Equal(1, result.Data.CountryOfOriginNo);
            Assert.Equal(2, result.Data.IHSCountryOfOriginNo);
            Assert.Equal("USA", result.Data.IHSCountryOfOriginName);
            Assert.Equal(1, result.Data.TypeOfSupplierNo);
            Assert.Equal(2, result.Data.ReasonForSupplierNo);
            Assert.Equal(1, result.Data.RiskOfSupplierNo);
            Assert.Equal(1, result.Data.CountryNo);
            Assert.Equal("United States", result.Data.CountryName);
            Assert.Equal("Volume discount", result.Data.SellPriceLessReason);
            Assert.True(result.Data.IsTestingRecommended);
            Assert.Equal(12.50, result.Data.SupplierPrice);
        }

        [Fact]
        public async Task Handle_WithNonExistentSourcingResultId_ReturnsUnsuccessfulResponse()
        {
            // Arrange
            var query = new GetSourcingResultDetailsQuery { SourcingResultId = 999 };

            _baseRepository.Setup(x => x.SqlQueryRawAsync(It.IsAny<string>(), It.IsAny<SqlParameter[]>()))
                          .ReturnsAsync(new List<SourcingResultDetail>());

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            Assert.False(result.Success);
            Assert.Null(result.Data);
        }

        [Fact]
        public async Task Handle_WithNullResult_ReturnsUnsuccessfulResponse()
        {
            // Arrange
            var query = new GetSourcingResultDetailsQuery { SourcingResultId = 123 };

            _baseRepository.Setup(x => x.SqlQueryRawAsync(It.IsAny<string>(), It.IsAny<SqlParameter[]>()))
                          .ReturnsAsync((List<SourcingResultDetail>)null!);

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            Assert.False(result.Success);
            Assert.Null(result.Data);
        }

        [Fact]
        public async Task Handle_WithNullableFieldsAsNull_HandlesGracefully()
        {
            // Arrange
            var query = new GetSourcingResultDetailsQuery { SourcingResultId = 123 };
            var mockSourcingResultDetail = new SourcingResultDetail
            {
                SourcingResultId = 123,
                CustomerRequirementNo = 456,
                // Set all nullable fields to null
                SourcingTable = null,
                FullPart = null,
                Part = null,
                ManufacturerNo = null,
                ManufacturerName = null,
                DateCode = null,
                ProductNo = null,
                ProductDescription = null,
                ProductInactive = null,
                PackageNo = null,
                PackageDescription = null,
                Quantity = null,
                Price = null,
                CurrencyNo = null,
                CurrencyCode = null,
                SupplierNo = null,
                SupplierName = null,
                SupplierNotes = null,
                ROHS = null,
                ROHSStatus = null,
                FactorySealed = null,
                OfferStatusNo = null,
                Notes = null,
                SPQ = null,
                LeadTime = null,
                MSL = null,
                MSLLevelNo = null,
                SupplierMOQ = null,
                SupplierTotalQSA = null,
                SupplierLTB = null,
                regionNo = null,
                partWatchMatch = null,
                UPLiftPrice = null,
                EstimatedShippingCost = null,
                DeliveryDate = null,
                SupplierWarranty = null,
                NonPreferredCompany = null,
                CountryOfOriginNo = null,
                IHSCountryOfOriginNo = null,
                IHSCountryOfOriginName = null,
                TypeOfSupplierNo = null,
                ReasonForSupplierNo = null,
                RiskOfSupplierNo = null,
                CountryNo = null,
                CountryName = null,
                SellPriceLessReason = null,
                IsTestingRecommended = null,
                SupplierPrice = null
            };

            _baseRepository.Setup(x => x.SqlQueryRawAsync(It.IsAny<string>(), It.IsAny<SqlParameter[]>()))
                          .ReturnsAsync(new List<SourcingResultDetail> { mockSourcingResultDetail });

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            Assert.True(result.Success);
            Assert.NotNull(result.Data);
            Assert.Equal(123, result.Data.SourcingResultId);
            Assert.Equal(456, result.Data.CustomerRequirementNo);
            
            // Verify nullable fields are handled correctly
            Assert.Null(result.Data.SourcingTable);
            Assert.Null(result.Data.FullPart);
            Assert.Null(result.Data.Part);
            Assert.Null(result.Data.ManufacturerNo);
            Assert.Null(result.Data.ManufacturerName);
            Assert.Null(result.Data.DateCode);
            Assert.Null(result.Data.ProductNo);
            Assert.Null(result.Data.ProductDescription);
            Assert.Null(result.Data.ProductInactive);
            Assert.Null(result.Data.PackageNo);
            Assert.Null(result.Data.PackageDescription);
            
            // Verify default values for non-nullable fields
            Assert.Equal(0, result.Data.Quantity); // null becomes 0
            Assert.Equal(0, result.Data.Price); // null becomes 0
            Assert.False(result.Data.PartWatchMatch); // null becomes false
        }

        [Fact]
        public async Task Handle_VerifiesCorrectSqlParametersArePassed()
        {
            // Arrange
            var query = new GetSourcingResultDetailsQuery { SourcingResultId = 456 };
            var mockSourcingResultDetail = _fixture.Create<SourcingResultDetail>();

            _baseRepository.Setup(x => x.SqlQueryRawAsync(It.IsAny<string>(), It.IsAny<SqlParameter[]>()))
                          .ReturnsAsync(new List<SourcingResultDetail> { mockSourcingResultDetail });

            // Act
            await _handler.Handle(query, CancellationToken.None);

            // Assert
            _baseRepository.Verify(x => x.SqlQueryRawAsync(
                It.Is<string>(sql => sql.Contains(StoredProcedures.Select_SourcingResult) && sql.Contains("@SourcingResultId")),
                It.Is<SqlParameter[]>(parameters => 
                    parameters.Length == 1 &&
                    parameters[0].ParameterName == "@SourcingResultId" &&
                    parameters[0].SqlDbType == SqlDbType.Int &&
                    (int)parameters[0].Value == 456)
            ), Times.Once);
        }

        [Fact]
        public async Task Handle_WithMinimalRequiredData_MapsCorrectly()
        {
            // Arrange
            var query = new GetSourcingResultDetailsQuery { SourcingResultId = 789 };
            var mockSourcingResultDetail = new SourcingResultDetail
            {
                SourcingResultId = 789,
                CustomerRequirementNo = 101,
                // Only set essential fields
                Quantity = 500,
                Price = 10.5,
                partWatchMatch = false
            };

            _baseRepository.Setup(x => x.SqlQueryRawAsync(It.IsAny<string>(), It.IsAny<SqlParameter[]>()))
                          .ReturnsAsync(new List<SourcingResultDetail> { mockSourcingResultDetail });

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            Assert.True(result.Success);
            Assert.NotNull(result.Data);
            Assert.Equal(789, result.Data.SourcingResultId);
            Assert.Equal(101, result.Data.CustomerRequirementNo);
            Assert.Equal(500, result.Data.Quantity);
            Assert.Equal(10.5, result.Data.Price);
            Assert.False(result.Data.PartWatchMatch);
        }

        [Fact]
        public async Task Handle_WithMaximumValues_HandlesCorrectly()
        {
            // Arrange
            var query = new GetSourcingResultDetailsQuery { SourcingResultId = 999 };
            var mockSourcingResultDetail = new SourcingResultDetail
            {
                SourcingResultId = 999,
                CustomerRequirementNo = int.MaxValue,
                Quantity = int.MaxValue,
                Price = double.MaxValue,
                UPLiftPrice = double.MaxValue,
                EstimatedShippingCost = double.MaxValue,
                SupplierPrice = double.MaxValue,
                SupplierTotalQSA = int.MaxValue,
                partWatchMatch = true
            };

            _baseRepository.Setup(x => x.SqlQueryRawAsync(It.IsAny<string>(), It.IsAny<SqlParameter[]>()))
                          .ReturnsAsync(new List<SourcingResultDetail> { mockSourcingResultDetail });

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            Assert.True(result.Success);
            Assert.NotNull(result.Data);
            Assert.Equal(999, result.Data.SourcingResultId);
            Assert.Equal(int.MaxValue, result.Data.CustomerRequirementNo);
            Assert.Equal(int.MaxValue, result.Data.Quantity);
            Assert.Equal(double.MaxValue, result.Data.Price);
            Assert.Equal(double.MaxValue, result.Data.UPLiftPrice);
            Assert.Equal(double.MaxValue, result.Data.EstimatedShippingCost);
            Assert.Equal(double.MaxValue, result.Data.SupplierPrice);
            Assert.Equal(int.MaxValue, result.Data.SupplierTotalQSA);
            Assert.True(result.Data.PartWatchMatch);
        }

        [Fact]
        public async Task Handle_WithSpecialCharactersInStrings_HandlesCorrectly()
        {
            // Arrange
            var query = new GetSourcingResultDetailsQuery { SourcingResultId = 111 };
            var mockSourcingResultDetail = new SourcingResultDetail
            {
                SourcingResultId = 111,
                CustomerRequirementNo = 222,
                Part = "Test-Part_123 & Special@Chars",
                SupplierName = "Supplier & Co. <Test>",
                Notes = "Line 1\nLine 2\rLine 3\r\nLine 4",
                SupplierNotes = "Notes with \"quotes\" and 'apostrophes'",
                SellPriceLessReason = "Price < Market && Volume > 1000",
                partWatchMatch = true,
                Quantity = 100,
                Price = 5.99
            };

            _baseRepository.Setup(x => x.SqlQueryRawAsync(It.IsAny<string>(), It.IsAny<SqlParameter[]>()))
                          .ReturnsAsync(new List<SourcingResultDetail> { mockSourcingResultDetail });

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            Assert.True(result.Success);
            Assert.NotNull(result.Data);
            Assert.Equal("Test-Part_123 & Special@Chars", result.Data.Part);
            Assert.Equal("Supplier & Co. <Test>", result.Data.SupplierName);
            Assert.Equal("Line 1\nLine 2\rLine 3\r\nLine 4", result.Data.Notes);
            Assert.Equal("Notes with \"quotes\" and 'apostrophes'", result.Data.SupplierNotes);
            Assert.Equal("Price < Market && Volume > 1000", result.Data.SellPriceLessReason);
        }

        [Fact]
        public async Task Handle_VerifiesStoredProcedureNameIsCorrect()
        {
            // Arrange
            var query = new GetSourcingResultDetailsQuery { SourcingResultId = 123 };
            var mockSourcingResultDetail = _fixture.Create<SourcingResultDetail>();

            _baseRepository.Setup(x => x.SqlQueryRawAsync(It.IsAny<string>(), It.IsAny<SqlParameter[]>()))
                          .ReturnsAsync(new List<SourcingResultDetail> { mockSourcingResultDetail });

            // Act
            await _handler.Handle(query, CancellationToken.None);

            // Assert
            _baseRepository.Verify(x => x.SqlQueryRawAsync(
                It.Is<string>(sql => sql.StartsWith(StoredProcedures.Select_SourcingResult)),
                It.IsAny<SqlParameter[]>()
            ), Times.Once);
        }
    }
}
