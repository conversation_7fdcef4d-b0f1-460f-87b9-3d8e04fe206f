﻿export class PostLineManager {
    constructor(onLinePostedSuccessCallback = () => { }) {
        this._selectedRow = null;
        this.$dialog = null;
        this._creditData = null;
        this._postAllInfo = null;
        this.onSuccessCallback = onLinePostedSuccessCallback;
    }

    async handleSubmit() {
        $("#post-line-dialog").dialog("setLoading", true)
        $("#yes-btn").prop("disabled", true)
        const action = this.$dialog.data("action")
        const soLineId = (this._selectedRow == null) ? null : this._selectedRow.lineId;
        let response = null;
        if (action == "post" || action == "unpost") response = await this.postUnpost(action, soLineId)
        else if (action == "delete") response = await this.deleteLine(soLineId)
        else response = await this.postUnpostAll(action)

        $("#post-line-dialog").dialog("setLoading", false)
        $("#yes-btn").prop("disabled", false)
        if (!response.success) {
            return showToast("danger", "An error occured.")
        }
        $("#post-line-dialog").dialog("close");
        showToast("success", window.localizedStrings.saveChangedMessage)
        this.$dialog.trigger('saveSuccess', soLineId)
        if (this.onSuccessCallback) this.onSuccessCallback();
    }

    async deleteLine(soLineId) {
        const response = await GlobalTrader.ApiClient.deleteAsync(`/orders/sales-order/so-lines/${soLineId}/delete-line`)
        return response;
    }

    async postUnpost(action, soLineId) {
        const posted = (action == "post" || action == "postAll")
        const request = {
            SalesOrderLineId: soLineId,
            Posted: posted
        };
        const response = await GlobalTrader.ApiClient.putAsync(`/orders/sales-order/so-lines/post-unpost`, request);
        return response
    }

    async postUnpostAll(action) {
        const lineIds = action == "postAll" ? this._postAllInfo.postIDs : this._postAllInfo.unpostIDs
        for (const lineId of lineIds) {
            const response = await this.postUnpost(action, lineId)
            if (!response.success) {
                return response
            }
        }
        const result = { success: true }
        return result
    }

    _getButtonConfig() {
        return {
            'lines-post-btn': {
                action: 'post',
                title: localized.postTitle,
                message: `${localized.postMessage}<strong>${localized.line}</strong>?`,
                validateCredit: true,
                getCreditTotal: () => {
                    return this._selectedRow.totalRaw + this._selectedRow.taxRaw + ((this._creditData.anyPosted) ? 0 : this._creditData.totalVal)
                }
            },
            'lines-unpost-btn': {
                action: 'unpost',
                title: localized.unpostTitle,
                message: `${localized.unpostMessage}<strong>${localized.line}</strong>?`,
                validateCredit: false
            },
            'lines-post-all-btn': {
                action: 'postAll',
                title: localized.postAllTitle,
                message: `${localized.postAllMessage}<strong>${localized.lines}</strong>?`,
                validateCredit: true,
                getCreditTotal: () => this._postAllInfo.postLinesTotal + ((this._creditData.anyPosted) ? 0 : this._creditData.totalVal)
            },
            'lines-unpost-all-btn': {
                action: 'unpostAll',
                title: localized.unpostAllTitle,
                message: `${localized.unpostAllMessage}<strong>${localized.lines}</strong>?`,
                validateCredit: false
            },
            'lines-delete-btn': {
                action: 'delete',
                title: localized.deleteTitle,
                message: `${localized.deleteMessage}<strong>${localized.line}</strong>?`,
                validateCredit: false
            }
        }
    }

    _handleButtonClick(config) {
        this.$dialog.dialog("option", "title", config.title);
        $("#description").html(config.message);
        
        if (config.validateCredit && config.getCreditTotal) {
            const creditTotal = config.getCreditTotal();
            if (!this.validateCreditLimit(creditTotal)) {
                return;
            }
        }
        this.$dialog.data("action", config.action);
        $("#post-line-dialog").dialog("open");
    }

    setupDialog() {
        this.$dialog = $("#post-line-dialog").dialog({
            height: 'auto',
            width: 'auto',
            maxHeight: $(window).height(),
            buttons: [
                {
                    id: "yes-btn",
                    text: window.localizedStrings.yes,
                    click: async () => {
                        this.handleSubmit()
                    }
                },
                {
                    text: window.localizedStrings.no,
                    click: () => {
                        $("#post-line-dialog").dialog("close")
                    }
                }
            ],
            open: () => {
                const action = this.$dialog.data("action");
                $('#customer-name').html(`${this._creditData.authData.customerName}`)
                if (action == "postAll" || action == "unpostAll") {
                    $("#wrapper *").hide()
                    $("#wrapper").removeClass("d-flex flex-column gap-3")
                }
                else {
                    $("#wrapper *").show()
                    $("#wrapper").addClass("d-flex flex-column gap-3")

                    const label = (this._selectedRow.serviceNo > 0) ? localized.service : localized.part
                    const value = (this._selectedRow.serviceNo > 0) ? this._selectedRow.serviceNo : this._selectedRow.part

                    $('#part-service-label').html(`${label}`)
                    $('#part-service').html(`${value}`)
                    $('#quantity').html(`${this._selectedRow.quantity}`)
                    $('#price').html(`${this._selectedRow.price}`)
                }
                $('.ui-dialog-titlebar-close').remove();
            }
        })
        $(".ui-dialog-buttonpane .ui-dialog-buttonset button:contains('Yes')").addClass("btn btn-primary fw-normal").html('<img src="/img/icons/check.svg" alt="Yes"> Yes');
        $(".ui-dialog-buttonpane .ui-dialog-buttonset button:contains('No')").addClass("btn btn-danger fw-normal").html('<img src="/img/icons/xmark.svg" alt="No"> No');

        const buttonConfig = this._getButtonConfig();
        Object.keys(buttonConfig).forEach(buttonId => {
            $(`#${buttonId}`).button().on('click', () => {
                this._handleButtonClick(buttonConfig[buttonId]);
            });
        });
    }

    _getLineState(lineData) {
        return {
            isPosted: lineData.isPosted,
            isAllocated: lineData.isAllocated && (lineData.serviceNo <= 0),
            isShipped: lineData.isShipped,
            isPartShipped: (lineData.serviceNo <= 0) && !lineData.isShipped && lineData.shipped > 0,
            isPartAllocated: !lineData.isAllocated && lineData.allocated > 0,
            isInactive: lineData.inactive,
            isClosed: lineData.closed,
            canBePosted: lineData.canBePosted
        };
    }

    checkPostLineConditions(lineData, isPosting) {
        if (!lineData) return false;

        const state = this._getLineState(lineData);
        const isPosted = (state.isPosted == !isPosting);
        const partAllocatedShipped = isPosting ? true : (!state.isPartShipped && !state.isPartAllocated);
        const canBePosted = isPosting ? state.canBePosted : true;

        return isPosted && !state.isAllocated && !state.isShipped && !state.isInactive && !state.isClosed && canBePosted && partAllocatedShipped;
    }

    checkDeleteLineConditions(lineData) {
        if (!lineData) return false;

        const state = this._getLineState(lineData);

        return !state.isPosted && !state.isAllocated && !state.isShipped && !state.isClosed && !state.isPartShipped;
    }

    validateCreditLimit(totalPrice) {
        const balanceWithOpen = this._creditData.authData.balanceWithOpenSOValue
        const creditLimit = this._creditData.authData.creditLimitValue;
        const invoiceNotExported = this._creditData.authData.invoiceNotExportValue;
        const creditBalance = creditLimit - (balanceWithOpen + invoiceNotExported)
        if (totalPrice <= creditBalance) {
            return true;
        }
        else {
            this.$dialog.trigger("creditWarning", localized.message)
            return false;
        }
    }

    lineCanBePostedUnposted(lineData, isPosting, SOCondition, authCheck) {
        const lineCondition = this.checkPostLineConditions(lineData, isPosting);
        const isAllocated = (lineData.isAllocated && (lineData.serviceNo <= 0));
        let result = (lineCondition && SOCondition);

        if (lineData.isIPO) {
            if (lineData.isIPOCreatedForCurrentLine) {
                result = false;
            }
            if (isPosting) {
                result = !lineData.isPosted ? authCheck : false;
            }
            else if (lineData.isPosted && !isAllocated) {
                result = authCheck;
            }
        }
        return result;
    }

    updateSelectedRow(selectedRow) {
        this._selectedRow = selectedRow;
    }
}

