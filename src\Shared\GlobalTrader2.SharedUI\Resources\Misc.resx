<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="AddNew" xml:space="preserve">
    <value>Add New</value>
  </data>
  <data name="All" xml:space="preserve">
    <value>All</value>
  </data>
  <data name="Allocations" xml:space="preserve">
    <value>Allocations</value>
  </data>
  <data name="Alternate" xml:space="preserve">
    <value>Alternate</value>
  </data>
  <data name="Amended" xml:space="preserve">
    <value>Amended</value>
  </data>
  <data name="AppTitle" xml:space="preserve">
    <value>Rebound Global:Trader</value>
  </data>
  <data name="Authorised" xml:space="preserve">
    <value>Authorised</value>
  </data>
  <data name="AutoSearchPart" xml:space="preserve">
    <value>Find a part number</value>
  </data>
  <data name="BeginsWith" xml:space="preserve">
    <value>beginning with</value>
  </data>
  <data name="Browse" xml:space="preserve">
    <value>Browse</value>
  </data>
  <data name="BrowserError" xml:space="preserve">
    <value>Browser Error</value>
  </data>
  <data name="BrowserWarning" xml:space="preserve">
    <value>Browser Warning</value>
  </data>
  <data name="BuiltFor" xml:space="preserve">
    <value>Built for</value>
  </data>
  <data name="BulkPrint" xml:space="preserve">
    <value>Bulk Print</value>
  </data>
  <data name="CertificateOfConformance" xml:space="preserve">
    <value>Certificate Of Conformity</value>
  </data>
  <data name="ChangedField" xml:space="preserve">
    <value>Changed {0}</value>
  </data>
  <data name="ChangedFieldToValue" xml:space="preserve">
    <value>Changed {0} to {1}</value>
  </data>
  <data name="ChangePassword" xml:space="preserve">
    <value>Change Password</value>
  </data>
  <data name="Checked" xml:space="preserve">
    <value>Checked</value>
  </data>
  <data name="Clear" xml:space="preserve">
    <value>Clear</value>
  </data>
  <data name="ClearAll" xml:space="preserve">
    <value>Clear All</value>
  </data>
  <data name="ClickToSnooze" xml:space="preserve">
    <value>Click to be reminded again in</value>
  </data>
  <data name="Closed" xml:space="preserve">
    <value>Closed</value>
  </data>
  <data name="CollapseAll" xml:space="preserve">
    <value>Collapse All</value>
  </data>
  <data name="Company" xml:space="preserve">
    <value>Company</value>
  </data>
  <data name="CompanyShort" xml:space="preserve">
    <value>Company</value>
  </data>
  <data name="Complete" xml:space="preserve">
    <value>Complete</value>
  </data>
  <data name="Confirmed" xml:space="preserve">
    <value>Confirmed</value>
  </data>
  <data name="Contacts" xml:space="preserve">
    <value>Contacts</value>
  </data>
  <data name="ContactsForCompany" xml:space="preserve">
    <value>Contacts for {0}</value>
  </data>
  <data name="ContactShort" xml:space="preserve">
    <value>Contact</value>
  </data>
  <data name="Contains" xml:space="preserve">
    <value>containing</value>
  </data>
  <data name="Credit" xml:space="preserve">
    <value>Credit Note</value>
  </data>
  <data name="CreditNo" xml:space="preserve">
    <value>Credit No</value>
  </data>
  <data name="CreditNote" xml:space="preserve">
    <value>Credit Note</value>
  </data>
  <data name="CreditNotes" xml:space="preserve">
    <value>Credit Notes</value>
  </data>
  <data name="CreditNoteShort" xml:space="preserve">
    <value>Credit</value>
  </data>
  <data name="CreditNotesShort" xml:space="preserve">
    <value>Credits</value>
  </data>
  <data name="Credits" xml:space="preserve">
    <value>Credit Notes</value>
  </data>
  <data name="CRMA" xml:space="preserve">
    <value>Customer RMA</value>
  </data>
  <data name="Current" xml:space="preserve">
    <value>Current</value>
  </data>
  <data name="CusReq" xml:space="preserve">
    <value>Customer Requirement</value>
  </data>
  <data name="CustomerRequirement" xml:space="preserve">
    <value>Requirement</value>
  </data>
  <data name="CustomerRequirementNo" xml:space="preserve">
    <value>Customer Requirement No</value>
  </data>
  <data name="CustomerRequirements" xml:space="preserve">
    <value>Requirements</value>
  </data>
  <data name="CustomerRequirementShort" xml:space="preserve">
    <value>Req</value>
  </data>
  <data name="CustomerRequirementsShort" xml:space="preserve">
    <value>Reqs</value>
  </data>
  <data name="CustomerRMA" xml:space="preserve">
    <value>Customer RMA</value>
  </data>
  <data name="CustomerRMANo" xml:space="preserve">
    <value>Customer RMA No</value>
  </data>
  <data name="CustomerRMAs" xml:space="preserve">
    <value>Customer RMAs</value>
  </data>
  <data name="CustomerRMAShort" xml:space="preserve">
    <value>CRMA</value>
  </data>
  <data name="CustomerRMAsShort" xml:space="preserve">
    <value>CRMAs</value>
  </data>
  <data name="CustomerShort" xml:space="preserve">
    <value>Customer</value>
  </data>
  <data name="Days" xml:space="preserve">
    <value>Days</value>
  </data>
  <data name="Days_1" xml:space="preserve">
    <value>1 day</value>
  </data>
  <data name="Days_2" xml:space="preserve">
    <value>2 days</value>
  </data>
  <data name="Days_3" xml:space="preserve">
    <value>3 days</value>
  </data>
  <data name="Days_4" xml:space="preserve">
    <value>4 days</value>
  </data>
  <data name="Debit" xml:space="preserve">
    <value>Debit Note</value>
  </data>
  <data name="DebitNo" xml:space="preserve">
    <value>Debit No</value>
  </data>
  <data name="DebitNote" xml:space="preserve">
    <value>Debit Note</value>
  </data>
  <data name="DebitNotes" xml:space="preserve">
    <value>Debit Notes</value>
  </data>
  <data name="DebitNoteShort" xml:space="preserve">
    <value>Debit</value>
  </data>
  <data name="DebitNotesShort" xml:space="preserve">
    <value>Debits</value>
  </data>
  <data name="Debits" xml:space="preserve">
    <value>Debit Notes</value>
  </data>
  <data name="Default" xml:space="preserve">
    <value>Default</value>
  </data>
  <data name="Despatched" xml:space="preserve">
    <value>Part Despatched</value>
  </data>
  <data name="Division" xml:space="preserve">
    <value>Division</value>
  </data>
  <data name="DLUP" xml:space="preserve">
    <value>Last updated</value>
  </data>
  <data name="Ellipses" xml:space="preserve">
    <value>...</value>
  </data>
  <data name="EndsWith" xml:space="preserve">
    <value>ending with</value>
  </data>
  <data name="EqualTo" xml:space="preserve">
    <value>=</value>
  </data>
  <data name="Error" xml:space="preserve">
    <value>Error</value>
  </data>
  <data name="ExistingDebitNote" xml:space="preserve">
    <value>Existing Debit Note</value>
  </data>
  <data name="ExistingGI" xml:space="preserve">
    <value>Existing Goods In Note</value>
  </data>
  <data name="ExistingInvoice" xml:space="preserve">
    <value>Existing Invoice</value>
  </data>
  <data name="ExpandAll" xml:space="preserve">
    <value>Expand All</value>
  </data>
  <data name="Expires" xml:space="preserve">
    <value>expires</value>
  </data>
  <data name="Explain_Settings_Company" xml:space="preserve">
    <value>Settings for {0}</value>
  </data>
  <data name="Explain_Settings_Global" xml:space="preserve">
    <value>Settings for all companies</value>
  </data>
  <data name="Explain_Settings_Personal" xml:space="preserve">
    <value>Preferences and settings personal to you</value>
  </data>
  <data name="Explain_Settings_Security" xml:space="preserve">
    <value>Security users, groups and permissions</value>
  </data>
  <data name="Female" xml:space="preserve">
    <value>Female</value>
  </data>
  <data name="Filter" xml:space="preserve">
    <value>Filter</value>
  </data>
  <data name="FilterResults" xml:space="preserve">
    <value>Filter Results</value>
  </data>
  <data name="For" xml:space="preserve">
    <value>For</value>
  </data>
  <data name="FromCustomerRMA" xml:space="preserve">
    <value>From Customer RMA</value>
  </data>
  <data name="FromCustomerRMALine" xml:space="preserve">
    <value>From Customer RMA Line</value>
  </data>
  <data name="FromInvoice" xml:space="preserve">
    <value>From Invoice</value>
  </data>
  <data name="FromInvoiceLine" xml:space="preserve">
    <value>From Invoice Line</value>
  </data>
  <data name="FromMasterPartList" xml:space="preserve">
    <value>From Master Part List</value>
  </data>
  <data name="FromPurchaseOrderLine" xml:space="preserve">
    <value>From Purchase Order Line</value>
  </data>
  <data name="FromPurchaseOrders" xml:space="preserve">
    <value>From Purchase Orders</value>
  </data>
  <data name="FromPurchaseRequisitions" xml:space="preserve">
    <value>From Purchase Requisitions</value>
  </data>
  <data name="FromQuotes" xml:space="preserve">
    <value>From Quotes</value>
  </data>
  <data name="FromRequirements" xml:space="preserve">
    <value>From Requirements</value>
  </data>
  <data name="FromSalesOrders" xml:space="preserve">
    <value>From Sales Orders</value>
  </data>
  <data name="FromService" xml:space="preserve">
    <value>From Service</value>
  </data>
  <data name="FromStock" xml:space="preserve">
    <value>From Stock</value>
  </data>
  <data name="FromSupplierRMALine" xml:space="preserve">
    <value>From Supplier RMA Line</value>
  </data>
  <data name="GI" xml:space="preserve">
    <value>Goods In Note</value>
  </data>
  <data name="GIDocketSODatePromisedLines" xml:space="preserve">
    <value>Allocated by {0} SO line(s)</value>
  </data>
  <data name="GoodsIn" xml:space="preserve">
    <value>Goods In</value>
  </data>
  <data name="GoodsInNo" xml:space="preserve">
    <value>Goods In No</value>
  </data>
  <data name="GoodsInShort" xml:space="preserve">
    <value>GI</value>
  </data>
  <data name="GP" xml:space="preserve">
    <value>GP</value>
  </data>
  <data name="GreaterThan" xml:space="preserve">
    <value>&gt;</value>
  </data>
  <data name="GreaterThanOrEqualTo" xml:space="preserve">
    <value>&gt;=</value>
  </data>
  <data name="Historical" xml:space="preserve">
    <value>Historical</value>
  </data>
  <data name="HomepageSelectUser" xml:space="preserve">
    <value>Select user</value>
  </data>
  <data name="HomepageViewForMyself" xml:space="preserve">
    <value>View for myself?</value>
  </data>
  <data name="IncludeCredits" xml:space="preserve">
    <value>Include Credits?</value>
  </data>
  <data name="Hours" xml:space="preserve">
    <value>Hours</value>
  </data>
  <data name="Hours_1" xml:space="preserve">
    <value>1 hour</value>
  </data>
  <data name="Hours_2" xml:space="preserve">
    <value>2 hours</value>
  </data>
  <data name="Hours_4" xml:space="preserve">
    <value>4 hours</value>
  </data>
  <data name="Hours_8" xml:space="preserve">
    <value>8 hours</value>
  </data>
  <data name="Inactive" xml:space="preserve">
    <value>Inactive</value>
  </data>
  <data name="Information" xml:space="preserve">
    <value>Information</value>
  </data>
  <data name="InTransit" xml:space="preserve">
    <value>In Transit</value>
  </data>
  <data name="Invoice" xml:space="preserve">
    <value>Invoice</value>
  </data>
  <data name="InvoiceLineAllocationsForCRMA" xml:space="preserve">
    <value>Invoice Line Allocations for this CRMA</value>
  </data>
  <data name="InvoiceLinesForCRMA" xml:space="preserve">
    <value>Invoice Lines for CRMA</value>
  </data>
  <data name="InvoiceLinesServices" xml:space="preserve">
    <value>Sales Order Service Lines</value>
  </data>
  <data name="InvoiceNo" xml:space="preserve">
    <value>Invoice No</value>
  </data>
  <data name="Invoices" xml:space="preserve">
    <value>Invoices</value>
  </data>
  <data name="InvoiceShort" xml:space="preserve">
    <value>Invoice</value>
  </data>
  <data name="InvoicesShort" xml:space="preserve">
    <value>Invoices</value>
  </data>
  <data name="JumpTo" xml:space="preserve">
    <value>Jump to</value>
  </data>
  <data name="LandedCostCalculator" xml:space="preserve">
    <value>Landed Cost Calculator</value>
  </data>
  <data name="LessThan" xml:space="preserve">
    <value>&lt;</value>
  </data>
  <data name="LessThanOrEqualTo" xml:space="preserve">
    <value>&lt;=</value>
  </data>
  <data name="LinePagingInactive" xml:space="preserve">
    <value>*** Inactive ***</value>
  </data>
  <data name="LM" xml:space="preserve">
    <value>Last Month</value>
  </data>
  <data name="Loading" xml:space="preserve">
    <value>Loading</value>
  </data>
  <data name="Login" xml:space="preserve">
    <value>Login</value>
  </data>
  <data name="Lot" xml:space="preserve">
    <value>Lot</value>
  </data>
  <data name="LotNo" xml:space="preserve">
    <value>Lot No</value>
  </data>
  <data name="Lots" xml:space="preserve">
    <value>Lot</value>
  </data>
  <data name="LotShort" xml:space="preserve">
    <value>Lots</value>
  </data>
  <data name="LotsShort" xml:space="preserve">
    <value>Lots</value>
  </data>
  <data name="LY" xml:space="preserve">
    <value>Last Year</value>
  </data>
  <data name="MacAddress" xml:space="preserve">
    <value>MAC Address</value>
  </data>
  <data name="Male" xml:space="preserve">
    <value>Male</value>
  </data>
  <data name="ManufacturerShort" xml:space="preserve">
    <value>Manufacturer</value>
  </data>
  <data name="Minutes_10" xml:space="preserve">
    <value>10 minutes</value>
  </data>
  <data name="Minutes_15" xml:space="preserve">
    <value>15 minutes</value>
  </data>
  <data name="Minutes_30" xml:space="preserve">
    <value>30 minutes</value>
  </data>
  <data name="Minutes_5" xml:space="preserve">
    <value>5 minutes</value>
  </data>
  <data name="MoreOpenPurchaseOrders" xml:space="preserve">
    <value>More Purchase Orders</value>
  </data>
  <data name="MoreOpenQuotes" xml:space="preserve">
    <value>More Quotes</value>
  </data>
  <data name="MoreOpenRequirements" xml:space="preserve">
    <value>More Requirements</value>
  </data>
  <data name="MoreOpenSalesOrders" xml:space="preserve">
    <value>More Sales Orders</value>
  </data>
  <data name="MorePurchaseOrdersDueIn" xml:space="preserve">
    <value>More Purchase Orders Due In</value>
  </data>
  <data name="MoreReceivedOrders" xml:space="preserve">
    <value>More Received Purchase Orders</value>
  </data>
  <data name="MoreSalesOrdersReadyToShip" xml:space="preserve">
    <value>More Sales Orders Ready to Ship</value>
  </data>
  <data name="MTD" xml:space="preserve">
    <value>MTD</value>
  </data>
  <data name="My" xml:space="preserve">
    <value>My</value>
  </data>
  <data name="NewCreditNote" xml:space="preserve">
    <value>New Credit Note</value>
  </data>
  <data name="NewCustomerRequirement" xml:space="preserve">
    <value>New Customer Requirement</value>
  </data>
  <data name="NewCustomerRMA" xml:space="preserve">
    <value>New Customer RMA</value>
  </data>
  <data name="NewDebitNote" xml:space="preserve">
    <value>New Debit Note</value>
  </data>
  <data name="NewGI" xml:space="preserve">
    <value>New Goods In Note</value>
  </data>
  <data name="NewGoodsIn" xml:space="preserve">
    <value>New Goods In</value>
  </data>
  <data name="NewInvoice" xml:space="preserve">
    <value>New Invoice</value>
  </data>
  <data name="NewLineItem" xml:space="preserve">
    <value>New Line Item</value>
  </data>
  <data name="NewPurchaseOrder" xml:space="preserve">
    <value>New Purchase Order</value>
  </data>
  <data name="NewPurchaseRequisition" xml:space="preserve">
    <value>New Purchase Requisition</value>
  </data>
  <data name="NewQuote" xml:space="preserve">
    <value>New Quote</value>
  </data>
  <data name="NewSalesOrder" xml:space="preserve">
    <value>New Sales Order</value>
  </data>
  <data name="NewSupplierRMA" xml:space="preserve">
    <value>New Supplier RMA</value>
  </data>
  <data name="NewSupplierRMAShipment" xml:space="preserve">
    <value>New Supplier RMA Shipment</value>
  </data>
  <data name="NM" xml:space="preserve">
    <value>Next Month</value>
  </data>
  <data name="No" xml:space="preserve">
    <value>No</value>
  </data>
  <data name="Number" xml:space="preserve">
    <value>Number</value>
  </data>
  <data name="NumberShort" xml:space="preserve">
    <value>No.</value>
  </data>
  <data name="NumberShorter" xml:space="preserve">
    <value>#</value>
  </data>
  <data name="NY" xml:space="preserve">
    <value>Next Year</value>
  </data>
  <data name="Of" xml:space="preserve">
    <value>of</value>
  </data>
  <data name="Open" xml:space="preserve">
    <value>Open</value>
  </data>
  <data name="Ordered" xml:space="preserve">
    <value>Ordered</value>
  </data>
  <data name="Overdue" xml:space="preserve">
    <value>Overdue</value>
  </data>
  <data name="PackingSlip" xml:space="preserve">
    <value>Packing Slip</value>
  </data>
  <data name="Page" xml:space="preserve">
    <value>Page</value>
  </data>
  <data name="Paid" xml:space="preserve">
    <value>Paid</value>
  </data>
  <data name="PartNo" xml:space="preserve">
    <value>Part No</value>
  </data>
  <data name="Pct" xml:space="preserve">
    <value>%</value>
  </data>
  <data name="PerPage" xml:space="preserve">
    <value>Per Page</value>
  </data>
  <data name="Placed" xml:space="preserve">
    <value>Placed</value>
  </data>
  <data name="PO" xml:space="preserve">
    <value>Purchase Order</value>
  </data>
  <data name="POLinesForDebitNote" xml:space="preserve">
    <value>Purchase Order Lines for this Debit Note</value>
  </data>
  <data name="POLinesForSRMA" xml:space="preserve">
    <value>Purchase Order Lines for use by this Supplier RMA</value>
  </data>
  <data name="PrintAd" xml:space="preserve">
    <value>Created with {0}</value>
  </data>
  <data name="PriorityHigh" xml:space="preserve">
    <value>High</value>
  </data>
  <data name="PriorityNormal" xml:space="preserve">
    <value>Normal</value>
  </data>
  <data name="ProFormaInvoice" xml:space="preserve">
    <value>Pro Forma Invoice</value>
  </data>
  <data name="ProspectShort" xml:space="preserve">
    <value>Prospect</value>
  </data>
  <data name="PurchaseOrder" xml:space="preserve">
    <value>Purchase Order</value>
  </data>
  <data name="PurchaseOrderNo" xml:space="preserve">
    <value>Purchase Order No</value>
  </data>
  <data name="PurchaseOrders" xml:space="preserve">
    <value>Purchase Orders</value>
  </data>
  <data name="PurchaseOrderShort" xml:space="preserve">
    <value>PO</value>
  </data>
  <data name="PurchaseOrdersShort" xml:space="preserve">
    <value>POs</value>
  </data>
  <data name="PurchaseRequisition" xml:space="preserve">
    <value>Purchase Requisition</value>
  </data>
  <data name="PurchaseRequisitions" xml:space="preserve">
    <value>Purchase Requisitions</value>
  </data>
  <data name="PurchaseRequisitionsShort" xml:space="preserve">
    <value>Pur Reqs</value>
  </data>
  <data name="Qu" xml:space="preserve">
    <value>Quote</value>
  </data>
  <data name="QuantityOrderedAndShipped" xml:space="preserve">
    <value>({0} ordered, {1} shipped)</value>
  </data>
  <data name="Quarantined" xml:space="preserve">
    <value>Quarantined</value>
  </data>
  <data name="QuickSearchWaterMark" xml:space="preserve">
    <value>&lt; quick search &gt;</value>
  </data>
  <data name="Quote" xml:space="preserve">
    <value>Quote</value>
  </data>
  <data name="QuoteNo" xml:space="preserve">
    <value>Quote No</value>
  </data>
  <data name="Quotes" xml:space="preserve">
    <value>Quotes</value>
  </data>
  <data name="QuoteShort" xml:space="preserve">
    <value>Quote</value>
  </data>
  <data name="QuotesShort" xml:space="preserve">
    <value>Quotes</value>
  </data>
  <data name="Ready" xml:space="preserve">
    <value>Ready</value>
  </data>
  <data name="ReceiptAndLinkedStock" xml:space="preserve">
    <value>Receipt and linked stock</value>
  </data>
  <data name="ReceiptLinkedStockAndShipments" xml:space="preserve">
    <value>Receipt, linked stock and shipments</value>
  </data>
  <data name="ReceiptOnly" xml:space="preserve">
    <value>Receipt only</value>
  </data>
  <data name="ReceiveCustomerRMA" xml:space="preserve">
    <value>Receive Customer RMA</value>
  </data>
  <data name="ReceiveCustomerRMAShort" xml:space="preserve">
    <value>Receive CRMA</value>
  </data>
  <data name="ReceiveCustomerRMAsShort" xml:space="preserve">
    <value>Receive CRMAs</value>
  </data>
  <data name="Received" xml:space="preserve">
    <value>Received</value>
  </data>
  <data name="ReceivedCRMAs" xml:space="preserve">
    <value>Received Customer RMAs</value>
  </data>
  <data name="ReceivedCustomerRMAShort" xml:space="preserve">
    <value>Received CRMAs</value>
  </data>
  <data name="ReceivedPurchaseOrders" xml:space="preserve">
    <value>Received Purchase Orders</value>
  </data>
  <data name="ReceivedPurchaseOrderShort" xml:space="preserve">
    <value>Received POs</value>
  </data>
  <data name="ReceivePurchaseOrder" xml:space="preserve">
    <value>Receive Purchase Order</value>
  </data>
  <data name="ReceivePurchaseOrderShort" xml:space="preserve">
    <value>Receive PO</value>
  </data>
  <data name="ReceivePurchaseOrdersShort" xml:space="preserve">
    <value>Receive POs</value>
  </data>
  <data name="ReceivingDocket" xml:space="preserve">
    <value>Receiving Docket</value>
  </data>
  <data name="Recent" xml:space="preserve">
    <value>Recent</value>
  </data>
  <data name="RecentPartsSourced" xml:space="preserve">
    <value>Recent Parts Sourced</value>
  </data>
  <data name="RequiredField" xml:space="preserve">
    <value>denotes a required field</value>
  </data>
  <data name="RequirementSourcingResults" xml:space="preserve">
    <value>Requirement Sourcing Results</value>
  </data>
  <data name="Reselect" xml:space="preserve">
    <value>Reselect</value>
  </data>
  <data name="ResultsLimit" xml:space="preserve">
    <value>Results Limit</value>
  </data>
  <data name="ResultsWithOptionalS" xml:space="preserve">
    <value>result(s)</value>
  </data>
  <data name="ROHSCompliant" xml:space="preserve">
    <value>RoHS Compliant</value>
  </data>
  <data name="ROHSExempt" xml:space="preserve">
    <value>RoHS Exempt</value>
  </data>
  <data name="ROHSNonCompliant" xml:space="preserve">
    <value>RoHS Non-compliant</value>
  </data>
  <data name="ROHSNotApplicable" xml:space="preserve">
    <value>RoHS Not Applicable</value>
  </data>
  <data name="ROHSUnknown" xml:space="preserve">
    <value>RoHS Unknown</value>
  </data>
  <data name="SalesOrder" xml:space="preserve">
    <value>Sales Order</value>
  </data>
  <data name="SalesOrderNo" xml:space="preserve">
    <value>Sales Order No</value>
  </data>
  <data name="SalesOrders" xml:space="preserve">
    <value>Sales Orders</value>
  </data>
  <data name="SalesOrderShort" xml:space="preserve">
    <value>SO</value>
  </data>
  <data name="SalesOrdersShort" xml:space="preserve">
    <value>SOs</value>
  </data>
  <data name="Saving" xml:space="preserve">
    <value>Saving</value>
  </data>
  <data name="Search" xml:space="preserve">
    <value>Search</value>
  </data>
  <data name="Searching" xml:space="preserve">
    <value>Searching</value>
  </data>
  <data name="Select" xml:space="preserve">
    <value>Select</value>
  </data>
  <data name="SelectAll" xml:space="preserve">
    <value>Select All</value>
  </data>
  <data name="SelectedPart" xml:space="preserve">
    <value>Currently Selected Part</value>
  </data>
  <data name="Sending" xml:space="preserve">
    <value>Sending</value>
  </data>
  <data name="Service" xml:space="preserve">
    <value>Service</value>
  </data>
  <data name="ServiceName" xml:space="preserve">
    <value>Service Name</value>
  </data>
  <data name="Services" xml:space="preserve">
    <value>Services</value>
  </data>
  <data name="ServiceShort" xml:space="preserve">
    <value>Service</value>
  </data>
  <data name="ServicesShort" xml:space="preserve">
    <value>Services</value>
  </data>
  <data name="Setup_CompanySettings" xml:space="preserve">
    <value>Company Settings</value>
  </data>
  <data name="Setup_GlobalSettings" xml:space="preserve">
    <value>Global Settings</value>
  </data>
  <data name="Setup_Personal" xml:space="preserve">
    <value>Personal Settings</value>
  </data>
  <data name="Setup_Security" xml:space="preserve">
    <value>Security Settings</value>
  </data>
  <data name="Ship" xml:space="preserve">
    <value>Shipped</value>
  </data>
  <data name="ShipASAP" xml:space="preserve">
    <value>Ship ASAP</value>
  </data>
  <data name="Shipped" xml:space="preserve">
    <value>Shipped</value>
  </data>
  <data name="ShippedLines" xml:space="preserve">
    <value>Shipped Lines</value>
  </data>
  <data name="ShipSalesOrder" xml:space="preserve">
    <value>Ship Sales Order</value>
  </data>
  <data name="ShipSalesOrderShort" xml:space="preserve">
    <value>Ship SO</value>
  </data>
  <data name="ShipSalesOrdersShort" xml:space="preserve">
    <value>Ship SOs</value>
  </data>
  <data name="ShipSupplierRMA" xml:space="preserve">
    <value>Ship Supplier RMA</value>
  </data>
  <data name="ShipSupplierRMAShort" xml:space="preserve">
    <value>Ship SRMA</value>
  </data>
  <data name="ShipSupplierRMAsShort" xml:space="preserve">
    <value>Ship SRMAs</value>
  </data>
  <data name="ShortShipped" xml:space="preserve">
    <value>Short Shipped</value>
  </data>
  <data name="ShowingXOfYResults" xml:space="preserve">
    <value>Showing {0} of {1} result(s)</value>
  </data>
  <data name="SO" xml:space="preserve">
    <value>Sales Order</value>
  </data>
  <data name="Sold" xml:space="preserve">
    <value>Sold</value>
  </data>
  <data name="SOReport" xml:space="preserve">
    <value>Sales Order Report</value>
  </data>
  <data name="SourcingShort" xml:space="preserve">
    <value>Sourcing</value>
  </data>
  <data name="SRMA" xml:space="preserve">
    <value>Supplier RMA</value>
  </data>
  <data name="SRMAShipment" xml:space="preserve">
    <value>Supplier RMA Shipment</value>
  </data>
  <data name="StandardShipping" xml:space="preserve">
    <value>(Standard ship in {0})</value>
  </data>
  <data name="Status" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="StockAvailable" xml:space="preserve">
    <value>available</value>
  </data>
  <data name="StockItem" xml:space="preserve">
    <value>Stock Item</value>
  </data>
  <data name="StockItems" xml:space="preserve">
    <value>Stock</value>
  </data>
  <data name="StockItemShort" xml:space="preserve">
    <value>Stock Item</value>
  </data>
  <data name="StockItemsShort" xml:space="preserve">
    <value>Stock</value>
  </data>
  <data name="StockLeftToAllocate" xml:space="preserve">
    <value>left to allocate</value>
  </data>
  <data name="StockOrdered" xml:space="preserve">
    <value>ordered</value>
  </data>
  <data name="StockShort" xml:space="preserve">
    <value>Stock</value>
  </data>
  <data name="SupplierRMA" xml:space="preserve">
    <value>Supplier RMA</value>
  </data>
  <data name="SupplierRMANo" xml:space="preserve">
    <value>Supplier RMA No</value>
  </data>
  <data name="SupplierRMAs" xml:space="preserve">
    <value>Supplier RMAs</value>
  </data>
  <data name="SupplierRMAShipment" xml:space="preserve">
    <value>Supplier RMA Shipment</value>
  </data>
  <data name="SupplierRMAShort" xml:space="preserve">
    <value>SRMA</value>
  </data>
  <data name="SupplierRMAsShort" xml:space="preserve">
    <value>SRMAs</value>
  </data>
  <data name="SupplierShort" xml:space="preserve">
    <value>Supplier</value>
  </data>
  <data name="Team" xml:space="preserve">
    <value>Team</value>
  </data>
  <data name="TM" xml:space="preserve">
    <value>This Month</value>
  </data>
  <data name="Today" xml:space="preserve">
    <value>Today</value>
  </data>
  <data name="ToDoAlert" xml:space="preserve">
    <value>Reminder</value>
  </data>
  <data name="Total" xml:space="preserve">
    <value>Total</value>
  </data>
  <data name="TY" xml:space="preserve">
    <value>This Year</value>
  </data>
  <data name="UpdatedByDateAndUser" xml:space="preserve">
    <value>{0} by {1}</value>
  </data>
  <data name="View" xml:space="preserve">
    <value>View</value>
  </data>
  <data name="Warning" xml:space="preserve">
    <value>Warning</value>
  </data>
  <data name="Weeks_1" xml:space="preserve">
    <value>1 week</value>
  </data>
  <data name="Weeks_2" xml:space="preserve">
    <value>2 weeks</value>
  </data>
  <data name="Welcome" xml:space="preserve">
    <value>Welcome</value>
  </data>
  <data name="XDaysAgo" xml:space="preserve">
    <value>{0} day(s) ago</value>
  </data>
  <data name="Yes" xml:space="preserve">
    <value>Yes</value>
  </data>
  <data name="Yesterday" xml:space="preserve">
    <value>Yesterday</value>
  </data>
  <data name="YTD" xml:space="preserve">
    <value>YTD</value>
  </data>
  <data name="NoMatches" xml:space="preserve">
    <value>No Matches</value>
  </data>
  <data name="DataListNuggetStateButtonTooltip" xml:space="preserve">
    <value>Lock to save your search, unlock to clear it</value>
  </data>
  <data name="Large" xml:space="preserve">
    <value>Large</value>
  </data>
  <data name="Medium" xml:space="preserve">
    <value>Medium</value>
  </data>
  <data name="FieldDefaultValue" xml:space="preserve">
    <value>Default: {0}</value>
  </data>
  <data name="Report" xml:space="preserve">
    <value>Report</value>
  </data>
  <data name="Stock" xml:space="preserve">
    <value>Stock</value>
  </data>
  <data name="StockQuantityAvailableAt" xml:space="preserve">
    <value>{0} at {1}</value>
  </data>
  <data name="NotQuoted" xml:space="preserve">
    <value>Not Quoted</value>
  </data>
  <data name="StockQuantityAvailable" xml:space="preserve">
    <value>{0} available</value>
  </data>
  <data name="Deauthorised" xml:space="preserve">
    <value>Deauthorised</value>
  </data>
  <data name="FromRequirementSourcingResult" xml:space="preserve">
    <value>From Requirement Sourcing Results</value>
  </data>
  <data name="SourcingResults" xml:space="preserve">
    <value>Sourcing Results</value>
  </data>
  <data name="LoginTimeoutInMinutes" xml:space="preserve">
    <value>{0} minutes</value>
  </data>
  <data name="Max" xml:space="preserve">
    <value>max</value>
  </data>
  <data name="Minutes" xml:space="preserve">
    <value>minutes</value>
  </data>
  <data name="MasterApplicationSettings" xml:space="preserve">
    <value>Master Application Settings</value>
  </data>
  <data name="ProFormaReceivingDocket" xml:space="preserve">
    <value>Pro Forma Receiving Docket</value>
  </data>
  <data name="DLNFilter_Contains" xml:space="preserve">
    <value>Contains</value>
  </data>
  <data name="DLNFilter_EndsWith" xml:space="preserve">
    <value>Ends with</value>
  </data>
  <data name="DLNFilter_StartsWith" xml:space="preserve">
    <value>Starts with</value>
  </data>
  <data name="Exported" xml:space="preserve">
    <value>Exported</value>
  </data>
  <data name="Approved" xml:space="preserve">
    <value>Approved</value>
  </data>
  <data name="Disapproved" xml:space="preserve">
    <value>Disapproved</value>
  </data>
  <data name="Unapproved" xml:space="preserve">
    <value>Unapproved</value>
  </data>
  <data name="OriginalOffer" xml:space="preserve">
    <value>Original Offer</value>
  </data>
  <data name="Thumbnail" xml:space="preserve">
    <value>Thumbnail</value>
  </data>
  <data name="CMPDocuments" xml:space="preserve">
    <value>Company_{0}_{1}</value>
  </data>
  <data name="CRMADocuments" xml:space="preserve">
    <value>CRMA_{0}_{1}</value>
  </data>
  <data name="GIDocuments" xml:space="preserve">
    <value>GI_{0}_{1}</value>
  </data>
  <data name="PODocuments" xml:space="preserve">
    <value>PO_{0}_{1}</value>
  </data>
  <data name="SOPDFDocuments" xml:space="preserve">
    <value>SO_{0}_{1}</value>
  </data>
  <data name="SRMADocuments" xml:space="preserve">
    <value>SRMA_{0}_{1}</value>
  </data>
  <data name="StockDocuments" xml:space="preserve">
    <value>STK_{0}_{1}</value>
  </data>
  <data name="InvDocuments" xml:space="preserve">
    <value>INVOICE_{0}_{1}</value>
  </data>
  <data name="ROHS2" xml:space="preserve">
    <value>ROHS2</value>
  </data>
  <data name="ROHSROHS2" xml:space="preserve">
    <value>ROHS2</value>
  </data>
  <data name="SupplierInvoice" xml:space="preserve">
    <value>Supplier Invoice</value>
  </data>
  <data name="Narrative" xml:space="preserve">
    <value>(Max 41)</value>
  </data>
  <data name="SecondRef" xml:space="preserve">
    <value>(Max 16)</value>
  </data>
  <data name="ApproveAndUnExported" xml:space="preserve">
    <value>Approved and Un Exported</value>
  </data>
  <data name="ExportedOnly" xml:space="preserve">
    <value>Exported Only</value>
  </data>
  <data name="HK" xml:space="preserve">
    <value>HK</value>
  </data>
  <data name="UK" xml:space="preserve">
    <value>UK</value>
  </data>
  <data name="AddSupplierInvoice" xml:space="preserve">
    <value>Add Supplier Invoice</value>
  </data>
  <data name="PDFFileName" xml:space="preserve">
    <value>{0}_{1}_{2}_{3}.pdf</value>
  </data>
  <data name="SORPDFDocuments" xml:space="preserve">
    <value>SOR_{0}_{1}</value>
  </data>
  <data name="NewNPR" xml:space="preserve">
    <value>New NPR</value>
  </data>
  <data name="NonPreferred" xml:space="preserve">
    <value>Non Preferred Source</value>
  </data>
  <data name="Traceable" xml:space="preserve">
    <value>Traceable Source</value>
  </data>
  <data name="Trusted" xml:space="preserve">
    <value>Trusted Source</value>
  </data>
  <data name="AS9120" xml:space="preserve">
    <value>AS9120 Required</value>
  </data>
  <data name="Setup_PrinterSettings" xml:space="preserve">
    <value>Company Settings</value>
  </data>
  <data name="ROHSROHS56" xml:space="preserve">
    <value>RoHS 5/6</value>
  </data>
  <data name="ROHSROHS66" xml:space="preserve">
    <value>RoHS 6/6</value>
  </data>
  <data name="NPRShort" xml:space="preserve">
    <value>NPR</value>
  </data>
  <data name="ActualLandedCost" xml:space="preserve">
    <value>Actual Landed Cost</value>
  </data>
  <data name="StockProvision" xml:space="preserve">
    <value>Stock Provision(%)</value>
  </data>
  <data name="ApprovedByAndDate" xml:space="preserve">
    <value>[By: {0}  ({1})]</value>
  </data>
  <data name="Setup_CertificateSettings" xml:space="preserve">
    <value>Global Settings</value>
  </data>
  <data name="Setup_EigthDCode" xml:space="preserve">
    <value>Root Cause Code</value>
  </data>
  <data name="TotalStocKProvisionValue" xml:space="preserve">
    <value>Total stock provision value</value>
  </data>
  <data name="MfpDocuments" xml:space="preserve">
    <value>MFG_{0}_{1}</value>
  </data>
  <data name="NewEPR" xml:space="preserve">
    <value>New EPR</value>
  </data>
  <data name="CommercialInvoice" xml:space="preserve">
    <value>Commercial Invoice</value>
  </data>
  <data name="ExcelFileName" xml:space="preserve">
    <value>{0}_{1}_{2}_{3}.xlsx</value>
  </data>
  <data name="MfeDocuments" xml:space="preserve">
    <value>MFGExcel_{0}_{1}</value>
  </data>
  <data name="ExcelCsvFileName" xml:space="preserve">
    <value>{0}_{1}_{2}_{3}.csv</value>
  </data>
  <data name="ExcelXlsFileName" xml:space="preserve">
    <value>{0}_{1}_{2}_{3}.xls</value>
  </data>
  <data name="MoveintoStock" xml:space="preserve">
    <value>Move into Stock</value>
  </data>
  <data name="Requiredoutwork" xml:space="preserve">
    <value>Required outwork</value>
  </data>
  <data name="Returntosupplier" xml:space="preserve">
    <value>Return to supplier</value>
  </data>
  <data name="Scrap" xml:space="preserve">
    <value>Scrap</value>
  </data>
  <data name="CustomerReqPrint" xml:space="preserve">
    <value>Print Req Enquiry Form for this company</value>
  </data>
  <data name="CustomerBrowseReqPrint" xml:space="preserve">
    <value>Print Req Enquiry Form</value>
  </data>
  <data name="NPR" xml:space="preserve">
    <value>NPR</value>
  </data>
  <data name="Completed" xml:space="preserve">
    <value>Completed</value>
  </data>
  <data name="NotAuthorised" xml:space="preserve">
    <value>Not Authorised</value>
  </data>
  <data name="NotCompleted" xml:space="preserve">
    <value>Not Completed</value>
  </data>
  <data name="Password" xml:space="preserve">
    <value>Fogot password</value>
  </data>
  <data name="Username" xml:space="preserve">
    <value>Forgot user name</value>
  </data>
  <data name="SORPDFDocumentsNew" xml:space="preserve">
    <value>SOREPORT_{0}_{1}</value>
  </data>
  <data name="QCDocument" xml:space="preserve">
    <value>QC_{0}_{1}</value>
  </data>
  <data name="BillofMaterial" xml:space="preserve">
    <value>HUBRFQ</value>
  </data>
  <data name="BOM" xml:space="preserve">
    <value>HUBRFQ</value>
  </data>
  <data name="IPO" xml:space="preserve">
    <value>Internal Purchase Orders</value>
  </data>
  <data name="InternalPurchaseOrder" xml:space="preserve">
    <value>Internal Purchase Order</value>
  </data>
  <data name="POQuote" xml:space="preserve">
    <value>Price Request</value>
  </data>
  <data name="PurchaseQuote" xml:space="preserve">
    <value>Price Request</value>
  </data>
  <data name="PurchaseHub" xml:space="preserve">
    <value>Rebound Dubai</value>
  </data>
  <data name="POHub" xml:space="preserve">
    <value>Rebound-Dubai</value>
  </data>
  <data name="PurchaseQuoteDoc" xml:space="preserve">
    <value>PurchaseQuote_{0}_{1}</value>
  </data>
  <data name="BOMDoc" xml:space="preserve">
    <value>BOM_{0}_{1}</value>
  </data>
  <data name="CSV_Import" xml:space="preserve">
    <value>ImportCSV</value>
  </data>
  <data name="ImportCSV" xml:space="preserve">
    <value>CSV Import</value>
  </data>
  <data name="New" xml:space="preserve">
    <value>New</value>
  </data>
  <data name="Released" xml:space="preserve">
    <value>Released to client</value>
  </data>
  <data name="RPQ" xml:space="preserve">
    <value>New RFQ from client</value>
  </data>
  <data name="TodayOpenPurchaseOrders" xml:space="preserve">
    <value>Today Open Purchase Orders</value>
  </data>
  <data name="UnProcessSalesOrders" xml:space="preserve">
    <value>Un-Process Sales Orders</value>
  </data>
  <data name="Partial" xml:space="preserve">
    <value>Partial release to client</value>
  </data>
  <data name="HomeNuggetBom" xml:space="preserve">
    <value>More HUBRFQ...</value>
  </data>
  <data name="PartialReleased" xml:space="preserve">
    <value>Partial Released</value>
  </data>
  <data name="ClientInvoice" xml:space="preserve">
    <value>Client Invoice</value>
  </data>
  <data name="Setup_InvoiceSetting" xml:space="preserve">
    <value>Invoice</value>
  </data>
  <data name="FromSourcingResult" xml:space="preserve">
    <value>From Sourcing Results</value>
  </data>
  <data name="schedularenablealready" xml:space="preserve">
    <value>Schedular is already enabled for this client.</value>
  </data>
  <data name="Notes" xml:space="preserve">
    <value>Notes:</value>
  </data>
  <data name="PercentSymbol" xml:space="preserve">
    <value>%</value>
  </data>
  <data name="SupplierAdvice" xml:space="preserve">
    <value>Supplier Adviced</value>
  </data>
  <data name="ChkInvMsg" xml:space="preserve">
    <value>View Reports for Purchase Hub</value>
  </data>
  <data name="HubInvoice" xml:space="preserve">
    <value>Hub Invoice</value>
  </data>
  <data name="FromClientInvoice" xml:space="preserve">
    <value>From Client Invoice</value>
  </data>
  <data name="AddExiIPO" xml:space="preserve">
    <value>Add to Existing IPO/PO</value>
  </data>
  <data name="AddNewIPO" xml:space="preserve">
    <value>Create New IPO/PO</value>
  </data>
  <data name="ManualAllocation" xml:space="preserve">
    <value>Manual Allocation to IPO/PO</value>
  </data>
  <data name="HubCredit" xml:space="preserve">
    <value>Hub Credit Notes</value>
  </data>
  <data name="HubCreditNote" xml:space="preserve">
    <value>Hub Credit Note</value>
  </data>
  <data name="CRXB2BLabel" xml:space="preserve">
    <value>CRX B2B</value>
  </data>
  <data name="GoodsInLabel" xml:space="preserve">
    <value>Goods In Label</value>
  </data>
  <data name="RejectedLabel" xml:space="preserve">
    <value>Rejected Label</value>
  </data>
  <data name="StockLabel" xml:space="preserve">
    <value>Stock Label</value>
  </data>
  <data name="NoBid" xml:space="preserve">
    <value>NoBid</value>
  </data>
  <data name="HUBRFQShort" xml:space="preserve">
    <value>HUBRFQ</value>
  </data>
  <data name="InternalPurchaseOrderShort" xml:space="preserve">
    <value>IPO</value>
  </data>
  <data name="MoreOpenInternalPurchaseOrders" xml:space="preserve">
    <value>More Internal Purchase Orders</value>
  </data>
  <data name="PrintOption" xml:space="preserve">
    <value>Current print set to:</value>
  </data>
  <data name="Setup_GCSecurity" xml:space="preserve">
    <value>Global Security Settings</value>
  </data>
  <data name="Setup_GlobalSecurity" xml:space="preserve">
    <value>Global Security Settings</value>
  </data>
  <data name="Excess" xml:space="preserve">
    <value>Excess Label</value>
  </data>
  <data name="Serial" xml:space="preserve">
    <value>Serial</value>
  </data>
  <data name="ChangedFieldLog" xml:space="preserve">
    <value>{0}</value>
  </data>
  <data name="ChangedFieldToValueLog" xml:space="preserve">
    <value>{0} : {1}</value>
  </data>
  <data name="senttocustomerOnly" xml:space="preserve">
    <value>Sent to Customer Only</value>
  </data>
  <data name="Notsent" xml:space="preserve">
    <value>Not Sent</value>
  </data>
  <data name="Setup_GTUpdate" xml:space="preserve">
    <value>Global Settings</value>
  </data>
  <data name="PrintHazardous" xml:space="preserve">
    <value>Hazardous Message</value>
  </data>
  <data name="QCDocument" xml:space="preserve">
    <value>QC_{0}_{1}</value>
  </data>
  <data name="Alternative" xml:space="preserve">
    <value>Alternate</value>
  </data>
  <data name="FirmAlternative" xml:space="preserve">
    <value>Firm Alternate</value>
  </data>
  <data name="PossibleAlternative" xml:space="preserve">
    <value>Possible Alternate</value>
  </data>
  <data name="Setup_SetupList" xml:space="preserve">
    <value>Master Status</value>
  </data>
  <data name="CustomerBrowseReqImport" xml:space="preserve">
    <value>Import Tool</value>
  </data>
  <data name="BOMImport" xml:space="preserve">
    <value>BOM Import</value>
  </data>
  <data name="ClientBOM" xml:space="preserve">
    <value>BOM</value>
  </data>
  <data name="ImportTool" xml:space="preserve">
    <value>Bom Import Tool</value>
  </data>
  <data name="CrossMatch" xml:space="preserve">
    <value>Cross Match</value>
  </data>
  <data name="CountryOfOrigin" xml:space="preserve">
    <value>Country Of Origin</value>
  </data>
  <data name="IHS_Active" xml:space="preserve">
    <value>IHS Active</value>
  </data>
  <data name="IHS_ActiveUnconfirmed" xml:space="preserve">
    <value>IHS Active Unconfirmed</value>
  </data>
  <data name="IHS_ContactMfr" xml:space="preserve">
    <value>IHS Contact Mfr</value>
  </data>
  <data name="IHS_Discontinued" xml:space="preserve">
    <value>IHS Discontinued</value>
  </data>
  <data name="IHS_DiscontinuedUnconfirmed" xml:space="preserve">
    <value>IHS Discontinued Unconfirmed</value>
  </data>
  <data name="IHS_EOL" xml:space="preserve">
    <value>IHS EOL</value>
  </data>
  <data name="IHS_NRFND" xml:space="preserve">
    <value>IHS NRFND</value>
  </data>
  <data name="IHS_Transferred" xml:space="preserve">
    <value>IHS Transferred</value>
  </data>
  <data name="DivisionHeader" xml:space="preserve">
    <value>Division Header</value>
  </data>
  <data name="EditinfoAfterRelease" xml:space="preserve">
    <value>Edit info - After Release</value>
  </data>
  <data name="ShippingInfo" xml:space="preserve">
    <value>Shipping Info</value>
  </data>
  <data name="Bothoptions" xml:space="preserve">
    <value>Both options</value>
  </data>
  <data name="Unchecked" xml:space="preserve">
    <value>Unchecked</value>
  </data>
  <data name="Allocated" xml:space="preserve">
    <value>Allocated</value>
  </data>
  <data name="AuthorisedPartAllocated" xml:space="preserve">
    <value>Authorised Part Allocated</value>
  </data>
  <data name="PartAllocated" xml:space="preserve">
    <value>Part Allocated</value>
  </data>
  <data name="PartPosted" xml:space="preserve">
    <value>Part Posted</value>
  </data>
  <data name="PartShipped" xml:space="preserve">
    <value>Part Shipped</value>
  </data>
  <data name="Posted" xml:space="preserve">
    <value>Posted</value>
  </data>
  <data name="Unposted" xml:space="preserve">
    <value>Unposted</value>
  </data>
  <data name="Approve" xml:space="preserve">
    <value>Approve</value>
  </data>
  <data name="Unapprove" xml:space="preserve">
    <value>Unapprove</value>
  </data>
  <data name="AwaitsInspection" xml:space="preserve">
    <value>Awaits Inspection</value>
  </data>
  <data name="PartReceived" xml:space="preserve">
    <value>Part Received</value>
  </data>
  <data name="IPOOFFERS" xml:space="preserve">
    <value>IPO OFFERS</value>
  </data>
  <data name="IPOREQUESTEDSUPPORT" xml:space="preserve">
    <value>IPO REQUESTED SUPPORT</value>
  </data>
  <data name="QUOTEDTO" xml:space="preserve">
    <value>QUOTED TO</value>
  </data>
  <data name="IHSDocuments" xml:space="preserve">
    <value>IHS_{0}_{1}</value>
  </data>
  <data name="SIDocuments" xml:space="preserve">
    <value>SI_{0}_{1}</value>
  </data>
  <data name="CloneAndAddHUBRFQ" xml:space="preserve">
    <value>Clone &amp; Add HUBRFQ</value>
  </data>
  <data name="CloneAndSendHUB" xml:space="preserve">
    <value>Clone &amp; Send HUB     </value>
  </data>
  <data name="InvPODDocuments" xml:space="preserve">
    <value>POD_{0}_{1}</value>
  </data>
  <data name="SupplierPOApproval" xml:space="preserve">
    <value>Open Supplier/PO Approvals</value>
  </data>
  <data name="DownloadPDF" xml:space="preserve">
    <value>Download PDF</value>
  </data>
  <data name="QuarantineProduct" xml:space="preserve">
    <value>Quarantine Product</value>
  </data>
  <data name="ReportNPR" xml:space="preserve">
    <value>Report NPR</value>
  </data>
  <data name="ShortShipment" xml:space="preserve">
    <value>Short Shipment</value>
  </data>
  <data name="UploadPDF" xml:space="preserve">
    <value>Upload PDF</value>
  </data>
  <data name="UploadPictures" xml:space="preserve">
    <value>Upload Pictures</value>
  </data>
  <data name="GILineDocuments" xml:space="preserve">
    <value>GILine_{0}_{1}</value>
  </data>
  <data name="SOImages" xml:space="preserve">
    <value>Images</value>
  </data>
  <data name="GIPrintLine" xml:space="preserve">
    <value>Print Line</value>
  </data>
  <data name="GIPrintAllLine" xml:space="preserve">
    <value>Print All Line</value>
  </data>
  <data name="DownloadGIPDF" xml:space="preserve">
    <value>PDF Report</value>
  </data>
  <data name="DownloadGIWord" xml:space="preserve">
    <value>Word Report</value>
  </data>
  <data name="Setup_CompanyDetails_Warnings" xml:space="preserve">
    <value>Warnings</value>
  </data>
  <data name="DocFileName" xml:space="preserve">
    <value>{0}_{1}_{2}_{3}.doc</value>
  </data>
  <data name="DocxFileName" xml:space="preserve">
    <value>{0}_{1}_{2}_{3}.docx</value>
  </data>
  <data name="SOExcelDocDocuments" xml:space="preserve">
    <value>SOED_{0}_{1}</value>
  </data>
  <data name="NewShortShipment" xml:space="preserve">
    <value>New Short Shipment</value>
  </data>
  <data name="PORPDFDocumentsNew" xml:space="preserve">
    <value>POREPORT_{0}_{1}</value>
  </data>
  <data name="KPIDivision" xml:space="preserve">
    <value>Division KPI</value>
  </data>
  <data name="KPISales" xml:space="preserve">
    <value>Sales KPI</value>
  </data>
  <data name="KPITeam" xml:space="preserve">
    <value>Team KPI</value>
  </data>
  <data name="MySupplierApprovals" xml:space="preserve">
    <value>My Supplier Approvals</value>
  </data>
  <data name="RelatedReceipt" xml:space="preserve">
    <value>Related Receipt</value>
  </data>
  <data name="GT" xml:space="preserve">
    <value>GT</value>
  </data>
  <data name="StockNumberShort" xml:space="preserve">
    <value>Stock No</value>
  </data>
  <data name="NewLineLot" xml:space="preserve">
    <value>From Lot</value>
  </data>
  <data name="OpenPowerBISales" xml:space="preserve">
    <value>Click here for Sales Reporting Hub Dashboard</value>
  </data>
  <data name="Dual" xml:space="preserve">
    <value>Dual</value>
  </data>
  <data name="MSelect" xml:space="preserve">
    <value>&lt; Select &gt;</value>
  </data>
  <data name="OGELLines" xml:space="preserve">
    <value>OGEL Lines</value>
  </data>
  <data name="EndUserUnderTakingPDF" xml:space="preserve">
    <value>EUU_{0}_{1}</value>
  </data>
  <data name="FromCreditNote" xml:space="preserve">
    <value>From Invoice Line</value>
  </data>
  <data name="TBC" xml:space="preserve">
    <value>TBC</value>
  </data>
  <data name="SourcingSupplier" xml:space="preserve">
    <value>Check Supplier / Manufacturer Data</value>
  </data>
  <data name="SendToPurchaseHub" xml:space="preserve">
    <value>On Hub</value>
  </data>
  <data name="Jsonfile" xml:space="preserve">
    <value>{0}_{1}_{2}_{3}.json</value>
  </data>
  <data name="CustomerQuoted" xml:space="preserve">
    <value>Customer Quoted</value>
  </data>
  <data name="OffersAdded" xml:space="preserve">
    <value>Offers Added</value>
  </data>
  <data name="FromClosedSOLine" xml:space="preserve">
    <value>From Closed SO Line</value>
  </data>
  <data name="OrderGenerated" xml:space="preserve">
    <value>Sales Order Generated</value>
  </data>
  <data name="FromStockLot" xml:space="preserve">
    <value>From Stock Lot</value>
  </data>
  <data name="OfferAdded" xml:space="preserve">
    <value>Offer Added</value>
  </data>
  <data name="PDFDocument" xml:space="preserve">
    <value>PDF Document</value>
  </data>
  <data name="ReportSTO" xml:space="preserve">
    <value>STO</value>
  </data>
  <data name="Hub Only" xml:space="preserve">
    <value>Hub Only</value>
  </data>
  <data name="Hub Sales" xml:space="preserve">
    <value>Hub &amp; Sales</value>
  </data>
  <data name="INSPDFDocuments" xml:space="preserve">
    <value>CIP_{0}_{1}</value>
  </data>
  <data name="CompanyOnStop" xml:space="preserve">
    <value>Ready To Ship</value>
  </data>
  <data name="NewCreditLimit" xml:space="preserve">
    <value>New CAF</value>
  </data>
  <data name="Images Attached" xml:space="preserve">
    <value>Images Attached</value>
  </data>
  <data name="Add New Images" xml:space="preserve">
    <value>Add New Images</value>
  </data>
  <data name="This Sourcing Result has no images attached" xml:space="preserve">
    <value>This Sourcing Result has no images attached</value>
  </data>
  <data name="SelectNewImagesInstruction" xml:space="preserve">
    <value>Select new image(s) and press {0}</value>
  </data>
  <data name="Stock Image File Extension" xml:space="preserve">
    <value>.jpg, .jpeg, .bmp</value>
  </data>
  <data name="Delete Images Attached" xml:space="preserve">
    <value>Delete Images Attached</value>
  </data>
  <data name="DeleteImageInstruction" xml:space="preserve">
    <value>Are you sure you would like to delete this {0}?</value>
  </data>
  <data name="Image" xml:space="preserve">
    <value>Image</value>
  </data>
</root>