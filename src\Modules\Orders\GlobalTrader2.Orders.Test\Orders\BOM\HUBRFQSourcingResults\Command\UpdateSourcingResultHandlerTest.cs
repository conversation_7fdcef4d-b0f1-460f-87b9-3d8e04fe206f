using AutoFixture;
using GlobalTrader2.Core;
using GlobalTrader2.Orders.UserCases.Orders.BOM.HUBRFQSourcingResults.Command;
using Microsoft.Data.SqlClient;
using Moq;
using System.Data;

namespace GlobalTrader2.Orders.Test.Orders.BOM.HUBRFQSourcingResults.Command
{
    public class UpdateSourcingResultHandlerTest
    {
        private readonly Mock<IBaseRepository<object>> _mockRepository;
        private readonly UpdateSourcingResultHandler _handler;
        private readonly IFixture _fixture;

        public UpdateSourcingResultHandlerTest()
        {
            _mockRepository = new Mock<IBaseRepository<object>>();
            _handler = new UpdateSourcingResultHandler(_mockRepository.Object);
            _fixture = new Fixture();
        }

        [Fact]
        public async Task Handle_WithValidCommand_ReturnsSuccess()
        {
            // Arrange
            var command = _fixture.Create<UpdateSourcingResultCommand>();

            _mockRepository.Setup(r => r.ExecuteSqlRawAsync(It.IsAny<string>(), It.IsAny<object[]>()))
                .ReturnsAsync((string query, SqlParameter[] parameters) =>
                {
                    parameters.Last().Value = 1;
                    return 1;
                });            
            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.True(result.Success);
        }

        [Fact]
        public async Task Handle_WithNullOutputParameters_ReturnsError()
        {
            // Arrange
            var command = _fixture.Create<UpdateSourcingResultCommand>();

            _mockRepository.Setup(r => r.ExecuteSqlRawAsync(It.IsAny<string>(), It.IsAny<object[]>()))
                .ReturnsAsync((string query, SqlParameter[] parameters) =>
                {
                    parameters.Last().Value = 0;
                    return 1;
                });
            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.False(result.Success);
        }
    }
}
