﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GlobalTrader2.Dto.Sourcing
{
    public class AddSourcingResultHUBRFQRequestDto
    {
        public int CustomerRequirementNo { get; set; }
        public string? TypeName { get; set; }
        public int? SupplierNo { get; set; }
        public string? Part { get; set; }
        public byte? ROHS { get; set; }
        public string? ROHSStatus { get; set; }
        public int? ManufacturerNo { get; set; }
        public string? DateCode { get; set; }
        public int? ProductNo { get; set; }
        public int? PackageNo { get; set; }
        public int Quantity { get; set; }
        public double Price { get; set; }
        public double SupPrice { get; set; }
        public int? CurrencyNo { get; set; }
        public double EstimatedShippingCostValue { get; set; }
        public int? OfferStatusNo { get; set; }
        public string? Notes { get; set; }
        public string? SPQ { get; set; }
        public string? LeadTime { get; set; }
        public string? FactorySealed { get; set; }
        public string? SupplierTotalQSA { get; set; }
        public string? SupplierMOQ { get; set; }
        public string? SupplierLTB { get; set; }
        public int? MslLevelNo { get; set; }
        public int? UpdatedBy { get; set; }
        public int? RegionNo { get; set; }
        public int? SupplierWarranty { get; set; }
        public int? IHSCountryOfOriginNo { get; set; }
        public int? TypeOfSupplier { get; set; }
        public int? ReasonForSupplier { get; set; }
        public int? RiskOfSupplier { get; set; }
        public int? CountryNo { get; set; }
        public bool IsTestingRecommended { get; set; }
        public DateTime? DeliveryDate { get; set; }
        public string? SellPriceLessReason { get; set; }
    }
}
