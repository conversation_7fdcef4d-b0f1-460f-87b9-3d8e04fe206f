﻿using FluentValidation;
using GlobalTrader2.Aggregator.UseCases.FileUpload.Commands.DeleteSourcingImage;

namespace GlobalTrader2.Aggregator.UseCases.FileUpload.Commands.DeleteSourcingImage
{
    public class DeleteSourcingImageValidator : AbstractValidator<DeleteSourcingImageCommand>
    {
        public DeleteSourcingImageValidator()
        {
            RuleFor(x => x.SourcingImageId).NotEmpty().NotNull();
        }
    }
}