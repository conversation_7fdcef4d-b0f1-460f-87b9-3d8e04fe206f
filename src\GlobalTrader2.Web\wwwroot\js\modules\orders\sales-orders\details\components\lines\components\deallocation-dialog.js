﻿import { EventEmitter } from '../../../../../../../components/base/event-emmiter.js?v=#{BuildVersion}#'
export class DeallocationDialogManager extends EventEmitter {
    constructor(level) {
        super();

        this._level = level;

        this.$lineContainer = $(`#lines-${this._level}`);
        this.$dialog = null;
        this._selectedAllocations = null;

        this.$deallocateButton = this.$lineContainer.find('#deallocate-btn');
    }

    initialize() {
        this.setupDialog();
    }
    async handleSubmitAsync() {
        this.$dialog.find("#yes-btn").prop("disabled", true)
        const allocationIdsString = this._getAllocationIdsString();
        if (GlobalTrader.StringHelper.isNullOrWhitespace(allocationIdsString)) {
            showToast("danger", "Please select a allocation");
            return;
        }
        const header = { "RequestVerificationToken": this.$dialog.find('input[name="__RequestVerificationToken"]').val() }
        const response = await GlobalTrader.ApiClient.deleteAsync(`/orders/sales-order/so-lines/allocation/delete-bulk?allocationIds=${allocationIdsString}`, header);

        this.$dialog.find("#yes-btn").prop("disabled", false)
        if (!response?.success) {
            return showToast("danger", response.errors.toString())
        }
        this.$dialog.dialog("close");
        showToast("success", window.localizedStrings.saveChangedMessage)
        this.$dialog.trigger('deallocateSuccess')
    }

    setupDialog() {
        this.$dialog = this.$lineContainer.find("#deallocate-dialog").dialog({
            height: 'auto',
            width: 'auto',
            maxHeight: $(window).height(),
            buttons: [
                {
                    id: "yes-btn",
                    text: window.localizedStrings.yes,
                    class: 'btn btn-primary fw-normal',
                    html: `<img src="/img/icons/check.svg" alt="${window.localizedStrings.yes}"> ${window.localizedStrings.yes}`,
                    click: async () => {
                        await this.handleSubmitAsync();
                    }
                },
                {
                    text: window.localizedStrings.no,
                    class: 'btn btn-danger fw-normal',
                    html: `<img src="/img/icons/xmark.svg" alt="${window.localizedStrings.no}"> ${window.localizedStrings.no}`,
                    click: () => {
                        this.$dialog.dialog("close")
                    }

                }
            ],
            open: (event, ui) => {
                const customerName = $("#hidCustomer").val();
                const salesOrderNumber = $("#hidSalesOrderNumber").val();
                this.$dialog.find('#sales-order-no').text(salesOrderNumber);
                this.$dialog.find('#customer-name').text(customerName);
            }
        })
    }

    updateSelectedAllocation(selectedAllocations) {
        this._selectedAllocations = selectedAllocations;
    }

    _getAllocationIdsString() {
        return this._selectedAllocations
            .map(row => row.allocationId)
            .filter(id => id != null)
            .join('|'); 
    }
}

