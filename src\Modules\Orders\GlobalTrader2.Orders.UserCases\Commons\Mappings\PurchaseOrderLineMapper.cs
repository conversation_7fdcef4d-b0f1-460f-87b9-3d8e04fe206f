using GlobalTrader2.Dto.PurchaseOrderLine;
using GlobalTrader2.Orders.UseCases.PurchaseOrderLine.Models;

namespace GlobalTrader2.Orders.UseCases.Commons.Mappings
{
    public class PurchaseOrderLineMapper : Profile
    {
        public PurchaseOrderLineMapper()
        {
            CreateMap<PurchaseOrderLineDetailsReadModel, PurchaseOrderLineDetailsDto>()
                .ForMember(x => x.Price, d => d.MapFrom(x => Convert.ToDecimal(x.Price)))
                .ForMember(x => x.LineProfit, d => d.MapFrom(x => Convert.ToDecimal(x.LineProfit)))
                .ForMember(x => x.LineProfitPercentage, d => d.MapFrom(x => Convert.ToDecimal(x.LineProfitPercentage)))
                .ForMember(x => x.ShipInCost, d => d.MapFrom(x => Convert.ToDecimal(x.ShipInCost)));
        }
    }
}