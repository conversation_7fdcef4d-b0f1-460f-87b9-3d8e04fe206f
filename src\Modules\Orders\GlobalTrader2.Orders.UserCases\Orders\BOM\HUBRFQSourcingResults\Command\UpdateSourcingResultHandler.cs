﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GlobalTrader2.Orders.UserCases.Orders.BOM.HUBRFQSourcingResults.Command
{
    public class UpdateSourcingResultHandler : IRequestHandler<UpdateSourcingResultCommand, BaseResponse<bool>>
    {
        private readonly IBaseRepository<object> _baseRepository;

        public UpdateSourcingResultHandler(IBaseRepository<object> baseRepository)
        {
            _baseRepository = baseRepository;
        }

        public async Task<BaseResponse<bool>> Handle(UpdateSourcingResultCommand request, CancellationToken cancellationToken)
        {
            var output = new SqlParameter("@RowsAffected", SqlDbType.Int) { Direction = ParameterDirection.Output };
            SqlParameter[] param =
            {
                new SqlParameter("@SourcingResultId", SqlDbType.Int) { Value = request.SourcingResultId },
                new SqlParameter("@Part", SqlDbType.NVarChar, 30) { Value = (object?)request.Part ?? DBNull.Value },
                new SqlParameter("@ManufacturerNo", SqlDbType.Int) { Value = (object?)request.ManufacturerNo ?? DBNull.Value },
                new SqlParameter("@DateCode", SqlDbType.NVarChar, 5) { Value = (object?)request.DateCode ?? DBNull.Value },
                new SqlParameter("@ProductNo", SqlDbType.Int) { Value = (object?)request.ProductNo ?? DBNull.Value },
                new SqlParameter("@PackageNo", SqlDbType.Int) { Value = (object?)request.PackageNo ?? DBNull.Value },
                new SqlParameter("@Quantity", SqlDbType.Int) { Value = request.Quantity },
                new SqlParameter("@Price", SqlDbType.Float) { Value = request.Price },
                new SqlParameter("@OfferStatusNo", SqlDbType.Int) { Value = (object?)request.OfferStatusNo ?? DBNull.Value },
                new SqlParameter("@SupplierNo", SqlDbType.Int) { Value = (object?)request.SupplierNo ?? DBNull.Value },
                new SqlParameter("@ROHS", SqlDbType.TinyInt) { Value = (object?)request.ROHS ?? DBNull.Value },
                new SqlParameter("@Notes", SqlDbType.NVarChar, 500) { Value = (object?)request.Notes ?? DBNull.Value },
                new SqlParameter("@UpdatedBy", SqlDbType.Int) { Value = (object?)request.UpdatedBy ?? DBNull.Value },
                new SqlParameter("@SuplierPrice", SqlDbType.Float) { Value = (object?)request.SuplierPrice ?? DBNull.Value },
                new SqlParameter("@EstimatedShippingCost", SqlDbType.Float) { Value = (object?)request.EstimatedShippingCostValue ?? DBNull.Value },
                new SqlParameter("@DeliveryDate", SqlDbType.DateTime) { Value = (object?)request.DeliveryDate ?? DBNull.Value },
                new SqlParameter("@PUHUB", SqlDbType.Bit) { Value = (object?)request.PUHUB ?? false },
                new SqlParameter("@SPQ", SqlDbType.NVarChar, 50) { Value = (object?)request.SPQ ?? DBNull.Value },
                new SqlParameter("@LeadTime", SqlDbType.NVarChar, 50) { Value = (object?)request.LeadTime ?? DBNull.Value },
                new SqlParameter("@ROHSStatus", SqlDbType.NVarChar, 50) { Value = (object?)request.ROHSStatus ?? DBNull.Value },
                new SqlParameter("@FactorySealed", SqlDbType.NVarChar, 50) { Value = (object?)request.FactorySealed ?? DBNull.Value },
                new SqlParameter("@MSL", SqlDbType.NVarChar, 50) { Value = (object?)request.MSL ?? DBNull.Value },
                new SqlParameter("@SupplierTotalQSA", SqlDbType.NVarChar, 30) { Value = (object?)request.SupplierTotalQSA ?? DBNull.Value },
                new SqlParameter("@SupplierLTB", SqlDbType.NVarChar, 30) { Value = (object?)request.SupplierLTB ?? DBNull.Value },
                new SqlParameter("@SupplierMOQ", SqlDbType.NVarChar, 30) { Value = (object?)request.SupplierMOQ ?? DBNull.Value },
                new SqlParameter("@RegionNo", SqlDbType.Int) { Value = (object?)request.RegionNo ?? DBNull.Value },
                new SqlParameter("@CurrencyNo", SqlDbType.Int) { Value = (object?)request.CurrencyNo ?? DBNull.Value },
                new SqlParameter("@LinkMultiCurrencyNo", SqlDbType.Int) { Value = (object?)request.LinkMultiCurrencyNo ?? DBNull.Value },
                new SqlParameter("@MSLLevelNo", SqlDbType.Int) { Value = (object?)request.MslLevelNo ?? DBNull.Value },
                new SqlParameter("@SupplierWarranty", SqlDbType.Int) { Value = (object?)request.SupplierWarranty ?? DBNull.Value },
                new SqlParameter("@isTestingRecommended", SqlDbType.Bit) { Value = (object?)request.IsTestingRecommended ?? false },
                new SqlParameter("@PriorityNo", SqlDbType.Int) { Value = (object?)request.PriorityNo ?? DBNull.Value },
                new SqlParameter("@IHSCountryOfOriginNo", SqlDbType.Int) { Value = (object?)request.IHSCountryOfOriginNo ?? DBNull.Value },
                new SqlParameter("@ChangedFields", SqlDbType.NVarChar, -1) { Value = (object?)request.ChangedFields ?? DBNull.Value },
                new SqlParameter("@PartWatchMatchHUBIPO", SqlDbType.Bit) { Value = (object?)request.PartWatchMatchHUBIPO ?? false },
                new SqlParameter("@TypeOfSupplier", SqlDbType.Int) { Value = (object?)request.TypeOfSupplier ?? DBNull.Value },
                new SqlParameter("@ReasonForSupplier", SqlDbType.Int) { Value = (object?)request.ReasonForSupplier ?? DBNull.Value },
                new SqlParameter("@RiskOfSupplier", SqlDbType.Int) { Value = (object?)request.RiskOfSupplier ?? DBNull.Value },
                new SqlParameter("@CountryNo", SqlDbType.Int) { Value = (object?)request.CountryNo ?? DBNull.Value },
                new SqlParameter("@SellPriceLessReason", SqlDbType.NVarChar, 128) { Value = (object?)request.SellPriceLessReason ?? DBNull.Value },
                output
            };


            string procedureName = StoredProcedures.Update_PoHubSourcingResult_V2;
            var queryStr = $"{procedureName} " +
                "@SourcingResultId, @Part, @ManufacturerNo, @DateCode, @ProductNo, @PackageNo, @Quantity, @Price, " +
                "@OfferStatusNo, @SupplierNo, @ROHS, @Notes, @UpdatedBy, @SuplierPrice, @EstimatedShippingCost, @DeliveryDate, " +
                "@PUHUB, @SPQ, @LeadTime, @ROHSStatus, @FactorySealed, @MSL, @SupplierTotalQSA, @SupplierLTB, @SupplierMOQ, " +
                "@RegionNo, @CurrencyNo, @LinkMultiCurrencyNo, @MSLLevelNo, @SupplierWarranty, @isTestingRecommended, @PriorityNo, " +
                "@IHSCountryOfOriginNo, @ChangedFields, @PartWatchMatchHUBIPO, @TypeOfSupplier, @ReasonForSupplier, @RiskOfSupplier, " +
                "@CountryNo, @SellPriceLessReason, @RowsAffected OUTPUT";

            await _baseRepository.ExecuteSqlRawAsync(queryStr, param);

            int rowAffect = (output.Value != DBNull.Value && output.Value != null)
                ? Convert.ToInt32(output.Value.ToString())
                : 0;

            return new BaseResponse<bool>
            {
                Success = rowAffect > 0,
            };
        }
    }
}
