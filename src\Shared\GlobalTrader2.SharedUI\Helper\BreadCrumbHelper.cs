﻿using GlobalTrader2.Dto.Company;
using GlobalTrader2.SharedUI.Constants;
using GlobalTrader2.SharedUI.Enums;
using GlobalTrader2.SharedUI.Models;
using GlobalTrader2.SharedUI.Services;

namespace GlobalTrader2.SharedUI.Helper
{
    public static class BreadCrumbHelper
    {
        public static BreadCrumbMenuModel Contact_MenuNew(SecurityManager _securityManager)
        {
            return new BreadCrumbMenuModel()
            {
                Title = "New",
                Childs = new List<BreadCrumbItemChild>
                {
                    new BreadCrumbItemChild
                    {
                        Title = Navigations.AddCompany.Title,
                        CtaUri = Navigations.AddCompany.CtaUri,
                        CheckPermission = () => _securityManager.CheckSectionLevelPermission(SecurityFunction.Contact_Company_Add)
                    },
                    new BreadCrumbItemChild
                    {
                        Title = Navigations.AddManufacturer.Title,
                        CtaUri = Navigations.AddManufacturer.CtaUri,
                        CheckPermission = () => _securityManager.CheckSectionLevelPermission(SecurityFunction.Contact_Manufacturer_Add)
                    }
                }
            };
        }

        public static BreadCrumbMenuModel Order_MenuNew(SecurityManager _securityManager)
        {
            return new BreadCrumbMenuModel()
            {
                Title = "New",
                Childs =
                [
                    new BreadCrumbItemChild
                    {
                        Title = "Requirement",
                        CtaUri = Navigations.CustomerRequirementAdd.CtaUri,
                        CheckPermission = () => _securityManager.CheckSectionLevelPermission(SecurityFunction.Orders_CustomerRequirement_Add)
                    },
                    new BreadCrumbItemChild
                    {
                        Title = "Quotation",
                        CtaUri = Navigations.QuoteOrderAdd.CtaUri,
                        CheckPermission = () => _securityManager.CheckSectionLevelPermission(SecurityFunction.Orders_Quote_Add)
                    },
                    new BreadCrumbItemChild
                    {
                        Title = "Sales Order",
                        CtaUri = Navigations.SalesOrderAdd.CtaUri,
                        CheckPermission = () => _securityManager.CheckSectionLevelPermission(SecurityFunction.Orders_SalesOrder_Add)
                    },
                    new BreadCrumbItemChild
                    {
                        Title = "Invoice",
                        CtaUri = Navigations.InvoicesAdd.CtaUri,
                        CheckPermission = () => _securityManager.CheckSectionLevelPermission(SecurityFunction.Orders_Invoice_Add)
                    },
                    new BreadCrumbItemChild
                    {
                        Title = "Purchase Order",
                        CtaUri = Navigations.PurchaseOrdersAdd.CtaUri,
                        CheckPermission = () => _securityManager.CheckSectionLevelPermission(SecurityFunction.Orders_PurchaseOrder_Add)
                    },
                    new BreadCrumbItemChild
                    {
                        Title = "Customer RMA",
                        CtaUri = Navigations.CustomerRMAAdd.CtaUri,
                        CheckPermission = () => _securityManager.CheckSectionLevelPermission(SecurityFunction.Orders_CRMA_Add)
                    },
                    new BreadCrumbItemChild
                    {
                        Title = "Supplier RMA",
                        CtaUri = Navigations.SupplierRMAAdd.CtaUri,
                        CheckPermission = () => _securityManager.CheckSectionLevelPermission(SecurityFunction.Orders_SRMA_Add)
                    },
                    new BreadCrumbItemChild
                    {
                        Title = "Credit Note",
                        CtaUri = Navigations.CreditNoteAdd.CtaUri,
                        CheckPermission = () => _securityManager.CheckSectionLevelPermission(SecurityFunction.Orders_CreditNote_Add)
                    },
                    new BreadCrumbItemChild
                    {
                        Title = "Debit Note",
                        CtaUri = Navigations.DebitNoteAdd.CtaUri,
                        CheckPermission = () => _securityManager.CheckSectionLevelPermission(SecurityFunction.Orders_DebitNote_Add)
                    }
                ]
            };
        }

        public static async Task<BreadCrumbMenuModel> CompanyTransactionMenuAsync(int loginId, CompanyTransactionMenuRequest request, SecurityManager _securityManager)
        {
            await _securityManager.LoadUserPagePermissions(loginId, SiteSection.Orders, default, false);
            var response = new BreadCrumbMenuModel
            {
                Id = "company-transactions-menu-breadcrumb",
                Title = "Transactions"
            };
            var groups = new List<List<BreadCrumbItemChild>>
            {
                new(),
                new(),
                new()
            };
            //First group
            if (_securityManager.CheckSectionLevelPermission(SecurityFunction.Orders_PurchaseOrder_Add)
                && !request.IsPOHub
                && request.IsApprovedForPOs
                && !request.IsOnStopPurchasingInfo)
            {
                groups[0].Add(new BreadCrumbItemChild
                {
                    Title = "Purchase Order",
                    CtaUri = RedirectUrlHelper.GetUrlWithParams_PurchaseOrderAdd(request.CompanyId, request.CompanyName, request.ContactId),
                    IsAllow = true
                });
            }
            //Next group
            if (_securityManager.CheckSectionLevelPermission(SecurityFunction.Orders_CustomerRequirement_Add))
            {
                groups[1].Add(new BreadCrumbItemChild
                {
                    Title = "Requirement",
                    CtaUri = RedirectUrlHelper.GetUrlWithParams_CustomerRequirementAdd(request.CompanyId, request.CompanyName, request.ContactId),
                    IsAllow = true
                });
            }

            if (_securityManager.CheckSectionLevelPermission(SecurityFunction.Orders_Quote_Add))
            {
                groups[1].Add(new BreadCrumbItemChild
                {
                    Title = "Quotation",
                    CtaUri = RedirectUrlHelper.GetUrlWithParams_QuoteAdd(request.CompanyId, request.CompanyName, request.ContactId),
                    IsAllow = true
                });
            }

            if (_securityManager.CheckSectionLevelPermission(SecurityFunction.Orders_SalesOrder_Add)
                && request.IsApprovedForSOs
                && (!request.IsOnStopSalesInfo || _securityManager.CheckSectionLevelPermission(SecurityFunction.Orders_SalesOrder_AllowCheckedCompanyOnStop)))
            {
                // Allow SOs only if approved and (when onStop and allow checked company on stop) or (not onStop)
                groups[1].Add(new BreadCrumbItemChild
                {
                    Title = "Sales Order",
                    CtaUri = RedirectUrlHelper.GetUrlWithParams_SalesOrderAdd(request.CompanyId, request.CompanyName, request.ContactId, null),
                    IsAllow = true
                });
            }

            if (_securityManager.CheckSectionLevelPermission(SecurityFunction.Orders_Invoice_Add))
            {
                groups[1].Add(new BreadCrumbItemChild
                {
                    Title = "Invoice",
                    CtaUri = RedirectUrlHelper.GetUrlWithParams_InvoicesAdd(request.CompanyId, request.CompanyName, request.ContactName, request.ContactId),
                    IsAllow = true
                });
            }

            var supplierInvoicePermissions = new List<SecurityFunction>{
                SecurityFunction.Warehouse_SupplierInvoice_Add
            };
            if (await _securityManager.CheckFunctionPermissions(loginId, false, supplierInvoicePermissions))
            {
                groups[1].Add(new BreadCrumbItemChild
                {
                    Title = "Supplier Invoice",
                    CtaUri = RedirectUrlHelper.GetUrlWithParams_SupplierInvoicesAdd(request.CompanyId, request.CompanyName, request.GoodsInId),
                    IsAllow = true
                });
            }
            //Next Group
            if (_securityManager.CheckSectionLevelPermission(SecurityFunction.Orders_CRMA_Add))
            {
                groups[2].Add(new BreadCrumbItemChild
                {
                    Title = "Customer RMA",
                    CtaUri = RedirectUrlHelper.GetUrlWithParams_CustomerRMAAdd(request.CompanyId, request.CompanyName, request.ContactId, request.ContactName),
                    IsAllow = true
                });
            }
            if (_securityManager.CheckSectionLevelPermission(SecurityFunction.Orders_SRMA_Add))
            {
                groups[2].Add(new BreadCrumbItemChild
                {
                    Title = "Supplier RMA",
                    CtaUri = RedirectUrlHelper.GetUrlWithParams_SupplierRMAAdd(request.CompanyId, request.CompanyName, request.ContactId, request.ContactName),
                    IsAllow = true
                });
            }
            if (_securityManager.CheckSectionLevelPermission(SecurityFunction.Orders_CreditNote_Add))
            {
                groups[2].Add(new BreadCrumbItemChild
                {
                    Title = "Credit Note",
                    CtaUri = RedirectUrlHelper.GetUrlWithParams_CreditNoteAdd(request.CompanyId, request.CompanyName, request.ContactId, request.ContactName),
                    IsAllow = true
                });
            }
            if (_securityManager.CheckSectionLevelPermission(SecurityFunction.Orders_DebitNote_Add))
            {
                groups[2].Add(new BreadCrumbItemChild
                {
                    Title = "Debit Note",
                    CtaUri = RedirectUrlHelper.GetUrlWithParams_DebitNoteAdd(request.CompanyId, request.CompanyName, request.ContactId, request.ContactName),
                    IsAllow = true
                });
            }
            //Add separator for each group
            for (int i = 0; i < groups.Count; i++)
            {
                var group = groups[i];
                if (group.Any())
                {
                    if (response.Childs.Count > 0)
                    {
                        response.Childs.Add(new BreadCrumbItemChild
                        {
                            Id = $"Separator_{i}",
                            Title = "SeparateLine"
                        });
                    }

                    response.Childs.AddRange(group);
                }
            }

            return response;
        }

        public static BreadCrumbMenuModel HUBRFQ_PrintMenu(SecurityManager _securityManager, SessionManager _sessionManager, int bomId)
        {
            return new BreadCrumbMenuModel()
            {
                Title = "Print",
                Childs = new List<BreadCrumbItemChild>
                {
                    new BreadCrumbItemChild
                    {
                        Id = "print-hubrfq",
                        Title = "Print",
                        CtaUri = $"#",
                        CheckPermission = () => _sessionManager.IsPOHub &&_securityManager.CheckGeneralPermission(SecurityFunction.AllowPrinting)
                    },
                    new BreadCrumbItemChild
                    {
                        Id = "print-log",
                        Title = "Log",
                        CtaUri = $"#",
                        CheckPermission = () => _sessionManager.IsPOHub &&_securityManager.CheckGeneralPermission(SecurityFunction.AllowPrinting)
                    }
                }
            };
        }
    }
}
