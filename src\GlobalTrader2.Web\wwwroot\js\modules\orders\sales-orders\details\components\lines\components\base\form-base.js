import { SearchSelectComponent } from '../../../../../../../../components/search-select/search-select.component.js?v=#{BuildVersion}#';
export class FormBase {
    _setFieldValue($element, value) {
        switch (true) {
            case $element instanceof SearchSelectComponent:
                $element.selectItem(value);
                break;
            case $element.is('select'):
                $element.dropdown('select', value);
                break;
            case $element.is('textarea'):
                $element.val(value);
                $element.trigger('change');
                break;
            case $element.is('input[type="checkbox"]'):
                $element.prop('checked', value);
                break;
            case $element.hasClass('hasDatepicker'):
                if (!(value instanceof Date)) {
                    value = new Date(value);
                }
                $element.datepicker('setDate', value);
                break;
            case $element.is('input'):
                $element.val(value);
                break;
            case $element.is('span, div, p, label'):
                $element.text(value);
                break;
            default:
                break;
        }
    }

    _showField($fieldName, isShow = true) {
        const specialClasses = ['datepicker', 'dropdown', 'special-input'];
        if (specialClasses.some(cls => $fieldName.hasClass(cls))) {
            $fieldName.parent().parent().toggleClass('d-none', !isShow);
        }
        else {
            $fieldName.parent().toggleClass('d-none', !isShow);
        }
    }

    _enableCheckBox($checkbox, isEnable = true) {
        $checkbox.prop('disabled', !isEnable);
    }

    _checkExistedValue(value) {
        return !(value == null || value == '' || value == undefined);
    }
}