﻿using GlobalTrader2.Dto.PurchaseOrder;

namespace GlobalTrader2.Orders.UserCases.Orders.PurchaseOrder.Queries.GetClosedPurchaseOrderLine
{
    public class GetClosedPurchaseOrderLineHandler : IRequestHandler<GetClosedPurchaseOrderLineQuery, BaseResponse<IEnumerable<PurchaseOrderLineDetailDto>>>
    {
        private readonly IBaseRepository<PurchaseOrderLineDetailReadModel> _repository;
        private readonly IMapper _mapper;

        public GetClosedPurchaseOrderLineHandler(IBaseRepository<PurchaseOrderLineDetailReadModel> repository, IMapper mapper)
        {
            _repository = repository;
            _mapper = mapper;
        }

        public async Task<BaseResponse<IEnumerable<PurchaseOrderLineDetailDto>>> Handle(GetClosedPurchaseOrderLineQuery request, CancellationToken cancellationToken)
        {
            var response = new BaseResponse<IEnumerable<PurchaseOrderLineDetailDto>>();
            var result = await _repository.SqlQueryRawAsync(
                $"{StoredProcedures.SelectAll_PurchaseOrderLine_closed_for_PurchaseOrder} @PurchaseOrderId",
                new[]
                {
                    new SqlParameter("@PurchaseOrderId", SqlDbType.Int) { Value = request.PurchaseOrderId },
                }
            );
            response.Success = true;
            response.Data = _mapper.Map<IEnumerable<PurchaseOrderLineDetailDto>>(result);
            return response;
        }
    }
}
