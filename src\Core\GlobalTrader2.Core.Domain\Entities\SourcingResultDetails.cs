﻿namespace GlobalTrader2.Core.Domain.Entities;

public class SourcingResultDetails
{
    public int SourcingResultId { get; set; }
    public string? FullPart { get; set; }
    public string? Part { get; set; }
    public int? ManufacturerNo { get; set; }
    public string? DateCode { get; set; }
    public int? ProductNo { get; set; }
    public int? PackageNo { get; set; }
    public int? Quantity { get; set; }
    public double? Price { get; set; }
    public int? CurrencyNo { get; set; }
    public int? SupplierNo { get; set; }
    public byte? ROHS { get; set; }
    public int? OfferStatusNo { get; set; }
    public string? Notes { get; set; }
    public string? ManufacturerName { get; set; }
    public string? ProductDescription { get; set; }
    public string? SupplierName { get; set; }
    public bool? ProductInactive { get; set; }
    public int? MSLLevelNo { get; set; }
    public string? PackageDescription { get; set; }
    public string? CustomerPart { get; set; }
    public bool? partWatchMatch { get; set; }
    public string? MSLLevel { get; set; }
    public bool? AS6081 { get; set; }
    public int? POHubCompanyNo { get; set; }
    public string? SourcingNotes { get; set; }
    public DateTime? DeliveryDate { get; set; }
}
