using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using GlobalTrader2.Dto.Converters.DateTime;

namespace GlobalTrader2.Dto.SalesOrderLine
{
    public class EditSalesOrderLineRequest
    {
        [Range(1, int.MaxValue)]
        public int SalesOrderId { get; set; }
        [JsonPropertyName("partNo")]
        public string? Part { get; set; }
        [JsonPropertyName("eccnCode")]
        public int? ECCNNo { get; set; }
        [JsonPropertyName("rohs")]
        public int? ROHS { get; set; }
        public string? DateCode { get; set; }
        public int? Quantity { get; set; }
        [JsonPropertyName("Cost")]
        public double? ServiceCostRef { get; set; }
        public double? Price { get; set; }
        [JsonPropertyName("custPartNo")]
        public string? CustomerPart { get; set; }
        [JsonPropertyName("manufacturer")]
        public int? ManufacturerNo { get; set; }
        [JsonPropertyName("product")]
        public int? ProductNo { get; set; }
        [JsonPropertyName("package")]
        public int? PackageNo { get; set; }
        [JsonConverter(typeof(ShortDateWithCultureFormatterConverter))]
        public DateTime? DatePromised { get; set; }
        [JsonPropertyName("promiseReason")]
        public int? PromiseReasonNo { get; set; }
        [JsonConverter(typeof(ShortDateWithCultureFormatterConverter))]
        [JsonPropertyName("dateRequired")]
        public DateTime? RequiredDate { get; set; }
        [JsonPropertyName("productSource")]
        public int? ProductSource { get; set; }
        [JsonPropertyName("shippingInstructions")]
        public string? Instructions { get; set; }
        [JsonPropertyName("printedNotes")]
        public string? Notes { get; set; }
        [JsonConverter(typeof(ShortDateWithCultureFormatterConverter))]
        [JsonPropertyName("PoDeliveryDate")]
        public DateTime? PODelDate { get; set; }
        [JsonPropertyName("Msl")]
        public string? MSLLevel { get; set; }
        public string? ContractNo { get; set; }
        public bool IsFormChanged { get; set; }
        [JsonPropertyName("shipAsap")]
        public bool? ShipASAP { get; set; }
        [JsonPropertyName("printHazardousWarning")]
        public bool? PrintHazardous { get; set; }
        public bool IsReasonChanged { get; set; }
        [JsonPropertyName("eccnCodeLabel")]
        public string? EccnCode { get; set; }
        [JsonConverter(typeof(ShortDateWithCultureFormatterConverter))]
        public DateTime? PreviousPromiseDate { get; set; }
        public int? SOSerialNo { get; set; }
        public bool Inactive { get; set; }
        public string Taxable { get; set; } = string.Empty;
        public string? Email { get; set; }
        public string? PromiseReasonString { get; set; }
        public int SalesOrderNumber { get; set; }
    }
}