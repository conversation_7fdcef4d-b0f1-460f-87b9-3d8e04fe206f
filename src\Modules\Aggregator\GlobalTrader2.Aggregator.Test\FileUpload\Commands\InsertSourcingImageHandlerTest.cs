﻿using GlobalTrader2.Aggregator.UseCases.FileUpload.Commands.InsertSourcingImage;
using GlobalTrader2.Core.Domain.Entities;
using GlobalTrader2.Core;
using Moq;
using FluentAssertions;

namespace GlobalTrader2.Aggregator.Test.FileUpload.Commands
{
    public class InsertSourcingImageHandlerTests
    {
        [Fact]
        public async Task Handle_Should_Add_SourcingImage_And_Return_Success()
        {
            // Arrange
            var mockRepo = new Mock<IBaseRepository<SourcingImage>>();
            var handler = new InsertSourcingImageHandler(mockRepo.Object);
            var query = new InsertSourcingImageCommand
            {
                SourcingNo = 123,
                Caption = "Test Image",
                ImageName = "test.png",
                UpdateBy = 42
            };

            // Act
            var result = await handler.Handle(query, CancellationToken.None);

            // Assert
            mockRepo.Verify(r => r.AddAsync(It.Is<SourcingImage>(s =>
                s.SourcingResultNo == query.SourcingNo &&
                s.Caption == query.Caption &&
                s.ImageName == query.ImageName &&
                s.UpdatedBy == query.UpdateBy &&
                s.DLUP <= DateTime.UtcNow
            )), Times.Once);

            result.Should().NotBeNull();
            result.Success.Should().BeTrue();
            result.Data.Should().BeTrue();
        }
    }
}
