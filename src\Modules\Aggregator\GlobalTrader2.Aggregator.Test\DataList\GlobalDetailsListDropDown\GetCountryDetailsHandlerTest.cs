﻿using AutoFixture;
using GlobalTrader2.Aggregator.UseCases.DataList.GlobalCountyListDropDown;
using GlobalTrader2.Core;
using GlobalTrader2.Core.Domain.Entities;
using Moq;

namespace GlobalTrader2.Aggregator.Test.DataList.GlobalDetailsListDropDown
{
    public class GetCountryDetailsHandlerTest
    {
        private readonly Mock<IBaseRepository<GetCountryDetailsReadModel>> _mockRepository;
        private readonly GetCountryDetailsHandler _handler;
        private readonly GetCountryDetailsQuery _query;
        private readonly IFixture _fixture;

        public GetCountryDetailsHandlerTest()
        {
            _mockRepository = new Mock<IBaseRepository<GetCountryDetailsReadModel>>();
            _handler = new GetCountryDetailsHandler(_mockRepository.Object);
            _fixture = new Fixture();
            _query = _fixture.Create<GetCountryDetailsQuery>();
        }

        [Fact]
        public async Task Handle_GetIndustry_ReturnsSuccessWithData()
        {
            // Arrange
            var entities = _fixture.Create<IReadOnlyList<GetCountryDetailsReadModel>>();

            _mockRepository
             .Setup(repo => repo.SqlQueryRawAsync(It.IsAny<string>(), It.IsAny<object[]>()))
             .ReturnsAsync(entities);

            // Act
            var response = await _handler.Handle(_query, CancellationToken.None);

            // Assert
            Assert.True(response.Success);
            Assert.True(response.Data is not null);
        }
    }
}
