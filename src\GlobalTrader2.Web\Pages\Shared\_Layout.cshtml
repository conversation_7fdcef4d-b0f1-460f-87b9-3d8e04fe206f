﻿@using GlobalTrader2.SharedUI.Areas.Common.Pages.Shared.Components.LeftNugget
@using GlobalTrader2.SharedUI.Areas.Common.Pages.Shared.Components.RecentlyViewed
@using GlobalTrader2.SharedUI.Constants
@using GlobalTrader2.SharedUI.Interfaces
@using GlobalTrader2.SharedUI.Models
@using GlobalTrader2.SharedUI.Services
@using GlobalTrader2.Web.Pages.Shared.Components.Footer
@using GlobalTrader2.Web.Pages.Shared.Components.TitleBar
@using GlobalTrader2.Web.Pages.Shared.Components.ClientByMaster
@using GlobalTrader2.Web.Pages.Shared.Components.TopMenu

@inject IWebResourceManager WebResourceManager
@inject SessionManager _sessionManager
@inject SettingManager _settingManager;

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Rebound Global:Trader - @ViewData["Title"]</title>
    <meta name="description" content="Rebound Global:Trader - @ViewData["Title"]" />
    <link rel="preload" href="/lib/bootstrap/dist/css/bootstrap.min.css" as="style" onload="this.rel='stylesheet'" />
    <link rel="icon" type="image/png" href="~/img/icons/favicon.ico" />
    <link rel="stylesheet" href="/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="/lib/jquery-ui/jquery-ui.min.css" />
    <link rel="stylesheet" href="/lib/fontawesome/css/all.min.css" />

    @await RenderSectionAsync("HeadBlock", required: false)

    <environment include="Development">
        <link rel="stylesheet" href="~/css/variables.css" asp-append-version="true" />
        <link rel="stylesheet" href="~/css/bootstrap-customize.css" asp-append-version="true" />
        <link rel="stylesheet" href="~/css/bootstrap-customize/button.css" asp-append-version="true" />
        <link rel="stylesheet" href="~/css/bootstrap-customize/dropdown.css" asp-append-version="true" />
        <link rel="stylesheet" href="~/css/bootstrap-customize/nav.css" asp-append-version="true" />
        <link rel="stylesheet" href="~/css/bootstrap-customize/border.css" asp-append-version="true" />
        <link rel="stylesheet" href="~/css/common-table.css" asp-append-version="true" />
        <link rel="stylesheet" href="~/css/common-form.css" asp-append-version="true" />
        <link rel="stylesheet" href="~/css/datatable.css" asp-append-version="true" />
        <link rel="stylesheet" href="~/css/dialog.css" asp-append-version="true" />
        <link rel="stylesheet" href="~/css/accordion.css" asp-append-version="true" />
        <link rel="stylesheet" href="~/css/loading-spinner.css" asp-append-version="true" />
        <link rel="stylesheet" href="~/css/size-spacing.css" asp-append-version="true" />
        <link rel="stylesheet" href="~/css/date-picker.css" asp-append-version="true" />
        <link rel="stylesheet" href="~/css/search-select.css" asp-append-version="true" />
        <link rel="stylesheet" href="~/css/left-nugget.css" asp-append-version="true" />
        <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
        <link rel="stylesheet" href="~/css/card.css" asp-append-version="true" />
        <link rel="stylesheet" href="~/css/filter-section.css" asp-append-version="true" />
        <link rel="stylesheet" href="~/css/modal.css" asp-append-version="true" />
        <link rel="stylesheet" href="~/css/info-content.css" asp-append-version="true" />
        <link rel="stylesheet" href="~/css/document-header-image.css" asp-append-version="true" />
        <link rel="stylesheet" href="~/css/collapsible-fieldset.css" asp-append-version="true" />
        <link rel="stylesheet" href="~/css/icon-button.css" asp-append-version="true" />
        <link rel="stylesheet" href="~/css/accordion-caf.css" asp-append-version="true" />
        <link rel="stylesheet" href="~/css/accordion-epr.css" asp-append-version="true" />
        <link rel="stylesheet" href="~/css/title-note.css" asp-append-version="true" />
        <link rel="stylesheet" href="~/css/breadcrumb-button.css" asp-append-version="true" />
        <link rel="stylesheet" href="https://cdn.datatables.net/scroller/2.4.3/css/scroller.dataTables.min.css" />
    </environment>
    <environment exclude="Development">
        <link rel="stylesheet" href="~/css/site.min.css" />
        <link rel="stylesheet" href="~/css/bootstrap-customize.min.css" />
    </environment>

    <link rel="stylesheet" href="~/GlobalTrader2.Web.styles.css" asp-append-version="true" />
</head>
<body data-loading="false">
    <header>
        @await Component.InvokeAsync(nameof(TitleBar))
    </header>
    <div class="top-menu-container">
        @await Component.InvokeAsync(nameof(TopMenu))
    </div>
    <div class="breadcrumb-container">  
        @await Html.PartialAsync("Partials/Breadcrumb/_Breadcrumb")
    </div>
    <div class="container-fluid main-container">
        <div class="row">
            <div id="leftSidebar" class="col-md-2 px-1 pt-0 sidebar-toggle d-flex flex-row @(_sessionManager.IsLeftPanelVisible ? "" : "d-none")">
                <div class="flex-grow-1">
                    @await RenderSectionAsync("LeftSidebar", required: false)
                    @await Component.InvokeAsync(nameof(LeftNugget),
                             new LeftNuggetProps()
                    {
                        ChildComponent = nameof(RecentlyViewed),
                        IsInitiallyExpanded = true,
                        Item = SideBar.RecentlyViewed

                    })
                </div>
                <div id="sidebar_toggle_btn_2" class="chevron-left-menu-icon">
                    <img src="/img/icons/chevrons-left.svg" alt="chevron left" height="18" />
                </div>
            </div>
            <div class="col sidebar-toggle pt-0" id="toggle_icon" style="@(_sessionManager.IsLeftPanelVisible ? "" : "display:block")">
                <div class="sidebar-close">
                    <div id="sidebar_toggle_btn_1" class="chevron-left-menu-icon">
                        <img src="/img/icons/chevrons-left.svg" alt="chevron left" height="18" />
                    </div>
                </div>
            </div>
            <div class="col content-body bg-white">
                <main role="main">
                    @RenderBody()
                </main>
            </div>
        </div>
    </div>
    <div id="toastContainer" class="position-fixed top-0 end-0 p-3" style="z-index: 1100;"></div>

    @Html.AntiForgeryToken()
    @await Component.InvokeAsync(nameof(Footer))
    @await Html.PartialAsync("Partials/_LocalizerResources")
    @await Html.PartialAsync("Partials/_AppInfo")
    @await Html.PartialAsync("Partials/_TodoReminderDialog")
    
    <script src="/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/lib/jquery/dist/jquery.min.js"></script>
    <script src="/lib/jquery-ui/jquery-ui.min.js"></script>
    <script src="@_settingManager.GetCdnUrl("/lib/signalr/dist/browser/signalr.min.js")"></script>

    <script src="https://cdn.jsdelivr.net/npm/axios@1.7.9/dist/axios.min.js" integrity="sha384-jLwhcmGu/RL8PSTUEl/559f8QVLL4QqM+HBvoZlt4F7XCdsdoDGAwW4nPFfoM7lU" crossorigin="anonymous"></script>
    <script src="@_settingManager.GetCdnUrl("/js/widgets/toast-message.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/helper/api-client.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/helper/function-helper.js")" asp-append-version="true"></script>

    <script src="@_settingManager.GetCdnUrl("/js/site.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/widgets/section-box.js")" asp-append-version="true"></script>

    <script src="@_settingManager.GetCdnUrl("/js/widgets/custom-dialog.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/components/left-nugget.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/core/hubs/notification-hub.js")" asp-append-version="true"></script>
    @await RenderSectionAsync("Scripts", required: false)
    @WebResourceManager.RenderScripts()

</body>

</html>
