﻿using GlobalTrader2.Orders.UserCases.Orders.PurchaseOrder.Queries.GetEprById;

namespace GlobalTrader2.Orders.UserCases.Test.Orders.PurchaseOrder.Queries.GetEprById;

public class GetEprByIdHandlerTests
{
    private readonly IFixture _fixture;
    private readonly GetEprByIdHandler _handler;
    private readonly Mock<IBaseRepository<EprDetailsReadModel>> _mockRepository;

    public GetEprByIdHandlerTests()
    {
        _fixture = new Fixture().Customize(new AutoMoqCustomization());
        _fixture.Behaviors.Add(new OmitOnRecursionBehavior());
        _mockRepository = new Mock<IBaseRepository<EprDetailsReadModel>>();
        _handler = new GetEprByIdHandler(_mockRepository.Object);
    }

    [Fact]
    public async Task Handle_GivenRepositoryReturnsNull_ShouldThrowArgumentException()
    {
        // Arrange
        var query = new GetEprByIdQuery(EprId: 1);

        _mockRepository
            .Setup(r => r.SqlQueryRawAsync(It.IsAny<string>(), It.IsAny<object[]>()))
            .ReturnsAsync((List<EprDetailsReadModel>)null!);

        // Act & Assert
        var ex = await Assert.ThrowsAsync<ArgumentException>(() => _handler.Handle(query, CancellationToken.None));

        Assert.Equal($"Cannot find EPR with id {query.EprId}", ex.Message);
    }

    [Fact]
    public async Task Handle_WhenEprFound_ShouldMapAndReturnCorrectly()
    {
        // Arrange
        var query = new GetEprByIdQuery(EprId: 1);

        var readModel = _fixture.Create<EprDetailsReadModel>();
        var mockResult = new List<EprDetailsReadModel> { readModel };

        _mockRepository
            .Setup(x => x.SqlQueryRawAsync(It.IsAny<string>(), It.IsAny<object[]>()))
            .ReturnsAsync(mockResult);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.Success);
        Assert.Same(readModel, result.Data);
        _mockRepository.Verify(r => r.SqlQueryRawAsync(
            It.IsAny<string>(),
            It.Is<object[]>(p => p.Length == 1)),
            Times.Once);
    }
}
