using System.Text.Json.Serialization;
using GlobalTrader2.Dto.Converters.DateTime;

namespace GlobalTrader2.Dto.SalesOrderLine
{
    public class CreateSalesOrderLineRequest
    {
        public int? SOCurrencyID { get; set; }
        public string? SOCurrencyCode { get; set; }
        [JsonConverter(typeof(ShortDateWithCultureFormatterConverter))]
        public DateTime? SODate { get; set; }
    }
}