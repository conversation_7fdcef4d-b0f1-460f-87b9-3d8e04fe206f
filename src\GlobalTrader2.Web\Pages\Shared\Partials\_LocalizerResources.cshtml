﻿@using GlobalTrader2.SharedUI.Models
@using GlobalTrader2.SharedUI.Services
@using Microsoft.Extensions.Localization;
@using GlobalTrader2.SharedUI.Constants
@using GlobalTrader2.SharedUI.Enums

@inject IStringLocalizer<GlobalTrader2.SharedUI.CommonResources> _commonLocalizer;
@inject IStringLocalizer<GlobalTrader2.SharedUI.MessageResources> _messageLocalizer;
@inject IStringLocalizer<GlobalTrader2.SharedUI.SecurityFunctions> _securityFunctionsLocalizer;
@inject IStringLocalizer<GlobalTrader2.SharedUI.Misc> _miscLocalizer;
@inject SessionManager _sessionManager

@{
    var sortASC = (int)SortColumnDirection.ASC;
    var sortDESC = (int)SortColumnDirection.DESC;

    var navigationParams = new NavigationCompanyDetailParams();
}
<script>
    const GlobalTrader = {};
    window.localizedStrings = {
        modifySuccessMessage: "@_commonLocalizer["ModifySuccess"]",
        disableMessage: "@_commonLocalizer["DisableMessage"]",
        enableMessage: "@_commonLocalizer["EnableMessage"]",
        securityUser: "@_commonLocalizer["SecurityUser"]",
        requiredField: "@_messageLocalizer["Please enter a value"]",
        pleaseEnterANumberFrom: "@_messageLocalizer["Please enter a number from"]",
        saveChangedMessage: "@_messageLocalizer["Your changes were saved"]",
        notAllSaveChangedMessage: "@_messageLocalizer["Not all changes were added successfully"]",
        failToSaveChangedMessage: "@_messageLocalizer["Fail to save your changes"]",
        restrictedManufacturerMessage: "@_messageLocalizer["Restricted Manufacturer Message"]",
        offersFailedToBeAddedMessage: "@_messageLocalizer["Some offers failed to be added"]",
        addToRequrementReverseLogisticsMessage: "@_messageLocalizer["Add To Requrement Reverse Logistics Message"]",
        thereWereSomeProblemsWithYourForm: "@_messageLocalizer["There were some problems with your form."]",
        pleaseCheckBelowAndTryAgain: "@_messageLocalizer["Please check below and try again."]",
        pleaseEnterAValue: "@_messageLocalizer["Please enter a value"]",
        cancel: "@_commonLocalizer["Cancel"]",
        approve: "@_commonLocalizer["Approve"]",
        reject: "@_commonLocalizer["Reject"]",
        save: "@_commonLocalizer["Save"]",
        yes: "@_commonLocalizer["Yes"]",
        no: "@_commonLocalizer["No"]",
        and: "@_commonLocalizer["and"]",
        continue : "@_commonLocalizer["Continue"]",
        thisSecurityGroupHasNoMembers: "@_commonLocalizer["This Security Group has no members"]",
        noDataReceived: "@_messageLocalizer["No_Data_Received"]",
        unexpectedError: "@_messageLocalizer["Unexpected_Error"]",
        groups: "@_commonLocalizer["securityGroup_Group"]",
        members: "@_commonLocalizer["securityGroup_Members"]",
        name: "@_commonLocalizer["securityUser_Name"]",
        login: "@_commonLocalizer["securityUser_Login"]",
        team: "@_commonLocalizer["securityUser_Team"]",
        division: "@_commonLocalizer["securityUser_Division"]",
        accounts: "@_commonLocalizer["securityUser_Accounts"]",
        select: "@_commonLocalizer["Select"]",
        characterCount: "@_commonLocalizer["Character count"]",
        characterMax: "@_commonLocalizer["chrs max"]",
        warehouse: "@_commonLocalizer["Warehouse"]",
        warehouseDivision: "@_commonLocalizer["Warehouse_Division"]",
        printer: "@_commonLocalizer["Printer"]",
        description: "@_commonLocalizer["Description"]",
        sectionName: "@_securityFunctionsLocalizer["SectionName"]",
        unauthorized: "@_commonLocalizer["unauthorized"]",
        AllowViewingTeamDivisionOrCompanyReports: "@_securityFunctionsLocalizer["Allow viewing Team, Division or Company reports"]",
        warehouseLocalCurrency: "@_commonLocalizer["Warehouse_LocalCurrency"]",
        communicationLogTypeName: "@_commonLocalizer["CommunicationLogTypeName"]",
        companyTypeName: "@_commonLocalizer["Name"]",
        loading: "@_commonLocalizer["Loading"]",
        LoginNameTaken: "@_messageLocalizer["The Login Name you entered has already been taken"]",
        ADMailTaken: "@_messageLocalizer["The AD Email you entered has already been taken"]",
        SecurityGroupNotExist: "@_messageLocalizer["The Security Group no longer exists."]",
        SecurityGroupLocked: "@_messageLocalizer["The Security Group is locked and cannot be deleted."]",
        SecurityGroupHasMembers: "@_messageLocalizer["The Security Group still has members. Please remove the users and try again."]",
        AdminSecurityGroupHasNoMembers: "@_messageLocalizer["There were some problems with your form. Please check below and try again. The Administrators Group must retain at least one member."]",
        SecurityGroupAdmin: "@_messageLocalizer["The Security Group is a system group and cannot be deleted."]",
        alreadyExists: "@_commonLocalizer["Already exists"]",
        companyType: "@_commonLocalizer["Company Type"]",
        noDataFound: "@_messageLocalizer["NoDataFound"]",
        countingMethod: "@_commonLocalizer["Counting Method"]",
        countingMethodDescription: "@_commonLocalizer["Description"]",
        ok: "@_commonLocalizer["ok"]",
        tabSecurityTitle: "@_securityFunctionsLocalizer["TabSecurity"]",
        reportSecurityTitle: "@_securityFunctionsLocalizer["ReportsPermissions"]",
        warehousePermissions: "@_securityFunctionsLocalizer["WarehousePermissions"]",
        ordersPermissions: "@_securityFunctionsLocalizer["OrdersPermissions"]",
        item: "@_commonLocalizer["Item"]",
        nextNumber: "@_commonLocalizer["NextNumber"]",
        noResultsFound: "@_commonLocalizer["No results found"]",
        to: "@_messageLocalizer["to"]",
        utilityPermissions: "@_securityFunctionsLocalizer["UtilityPermissions"]",
        generalPermissions: "@_securityFunctionsLocalizer["GeneralPermissions"]",
        setupPermissions: "@_securityFunctionsLocalizer["SetupPermissions"]",
        contactPermissions: "@_securityFunctionsLocalizer["ContactPermissions"]",
        sessionTerminated: "@_messageLocalizer["The Session has been terminated."]",
        fileNotAllowedErrorMessage: "@_messageLocalizer["The file you have selected is not an allowed type"]",
        fileTooLargeErrorMessage: "@_messageLocalizer["The file you have selected is too large. Please select a file smaller than {0} MB"]",
        requiredFileErrorMessage: "@_messageLocalizer["Please choose a file"]",
        documentMB: "@_commonLocalizer["DocumentMB"]",
        taskDateFrom: "@_commonLocalizer["Task Date From"]",
        taskDateTo: "@_commonLocalizer["Task Date To"]",
        taskReminderDate: "@_commonLocalizer["Task Reminder Date"]",
        taskTitle: "@_commonLocalizer["Task Title"]",
        taskType: "@_commonLocalizer["Task Type"]",
        taskCategory: "@_commonLocalizer["Task Category"]",
        taskStatus: "@_commonLocalizer["Task Status"]",
        customerName: "@_commonLocalizer["Customer Name"]",
        salesPerson: "@_commonLocalizer["Salesperson"]",
        quoteNumber: "@_commonLocalizer["Quote Number"]",
        salesOrderNumber: "@_commonLocalizer["Sales Order Number"]",
        documentMB: "@_commonLocalizer["DocumentMB"]",
        validateFutureTime: "@_messageLocalizer["Please enter a date and time in the future"]",
        UseCompanyHeaderForInvoice: "@_messageLocalizer["UseCompanyHeaderForInvoice"]",
        ApplyOnPO: "@_messageLocalizer["ApplyOnPO"]",
        ApplyOnCI: "@_messageLocalizer["ApplyOnCI"]",
        startsWith: "@_commonLocalizer["Starts with"]",
        endsWith: "@_commonLocalizer["Ends with"]",
        contains: "@_commonLocalizer["Contains"]",
        PleaseEnterANumericValueLessThanOrEqualTo: "@_messageLocalizer["Please enter a numeric value less than or equal to"]",
        PleaseEnterANumericValueGreaterThanOrEqualTo: "@_messageLocalizer["Please enter a numeric value greater than or equal to"]",
        MarkItemComplete: "@_commonLocalizer["MarkItemComplete"]",
        MarkItemIncomplete: "@_commonLocalizer["MarkItemIncomplete"]",
        dateMustBeInFuture: "@_commonLocalizer["Please enter a date from today or in the future."]",
        url: "@_commonLocalizer["Url"]",
        from: "@_commonLocalizer["From"]",
        subject: "@_commonLocalizer["Subject"]",
        Date: "@_commonLocalizer["Date"]",
        Rate: "@_commonLocalizer["Rate"]",
        Rate2: "@_commonLocalizer["Rate2"]",
        send: "@_commonLocalizer["Send"]",
        YourMessageHasBeenSent: "@_messageLocalizer["YourMessageHasBeenSent"]",
        NextNumberTooSmall: "@_messageLocalizer["NextNumberTooSmall"]",
        Tax: "@_commonLocalizer["Tax"]",
        CurrentRate: "@_commonLocalizer["CurrentRate"]",
        CurrentRate2: "@_commonLocalizer["CurrentRate2"]",
        TaxCode: "@_commonLocalizer["TaxCode"]",
        PurchaseTaxCode: "@_commonLocalizer["PurchaseTaxCode"]",
        Notes: "@_commonLocalizer["Notes"]",
        AdminNotes: "@_commonLocalizer["AdminNotes"]",
        YouHaveNewMessages: "@_messageLocalizer["You have {0} new message(s)"]",
        NewMessages: "@_messageLocalizer["New Message(s)"]",
        ROHSCompliant: "@_commonLocalizer["ROHSCompliant"]",
        ROHSNonCompliant: "@_commonLocalizer["ROHSNonCompliant"]",
        ROHSExempt: "@_commonLocalizer["ROHSExempt"]",
        ROHS2: "@_commonLocalizer["ROHS2"]",
        ROHS56: "@_commonLocalizer["ROHS56"]",
        ROHS66: "@_commonLocalizer["ROHS66"]",
        ROHSUnknown: "@_commonLocalizer["ROHSUnknown"]",
        ROHSNotApplicable: "@_commonLocalizer["ROHSNotApplicable"]",
        RestrictedManufacturerWarning: "@_messageLocalizer["Restricted Manufacturer Warning"]",
        FileNotFoundMessage: "@_messageLocalizer["FileNotFound"]",
        CannotUploadMoreThan: "@_messageLocalizer["CannotUploadMoreThan"]",
        CannotUploadMoreThanImages: "@_messageLocalizer["CannotUploadMoreThanImages"]",
        NoMultipleFileUploadMessage: "@_messageLocalizer["NoMultipleFileUploadMessage"]",
        PleaseChooseDateAndTimeLaterThanCurrentValues: "@_commonLocalizer["PleaseChooseDateAndTimeLaterThanCurrentValues"]",
        Today: "@_miscLocalizer["Today"]",
        Yesterday: "@_miscLocalizer["Yesterday"]",
        XDaysAgo: "@_miscLocalizer["XDaysAgo"]",
        PageNotFound: "@_messageLocalizer["PageNotFound"]",
        ApplicationSavedSuccessfully: "@_messageLocalizer["ApplicationSavedSuccessfully"]",
        ApplicationAuthorizedSuccessfully: "@_messageLocalizer["ApplicationAuthorizedSuccessfully"]",
        ApplicationRejectedSuccessfully: "@_messageLocalizer["ApplicationRejectedSuccessfully"]",
        IHSServiceUnavailable: "@_messageLocalizer["IHSServiceUnavailable"]",
        ApplicationUpdatedSuccessfully: "@_messageLocalizer["Application Updated Successfully"]",
        partsAdded: "@_messageLocalizer["Parts added successfully:"]",
        partsNotAdded: "@_messageLocalizer["Parts not added:"]",
        reason: "@_messageLocalizer["Reason:"]",
        offerFailedHeader: "@_messageLocalizer["Below offers cannot be added to requirement"]",
        partNumbers: "@_messageLocalizer["Part Numbers:"]",
        requirementLabel: "@_messageLocalizer["Requirement Number:"]",
        timeOut: "@_messageLocalizer["Sorry, the database call has timed out"]",
        LinkAccount: "@_commonLocalizer["Link/Unlink"]",
        refreshToViewRecordMessage: "@_messageLocalizer["Please click on refresh button to view record"]",
        dateTimeLaterThan: "@_messageLocalizer["Expiry Date must be later than or equal to the Start Date."]",
        dateTimeEarlierThan: "@_messageLocalizer["Start Date must be earlier than or equal to the Expiry Date."]",
        noMatches: "@_commonLocalizer["No Matches"]",
        quickBrowseCompanyName: "@_commonLocalizer["quickBrowseCompanyName"]",
        quickBrowseViewLevel: "@_commonLocalizer["quickBrowseViewLevel"]",
        quickBrowseName: "@_commonLocalizer["quickBrowseName"]",
        quickBrowseType: "@_commonLocalizer["quickBrowseType"]",
        quickBrowseCity: "@_commonLocalizer["quickBrowseCity"]",
        quickBrowseCountry: "@_commonLocalizer["quickBrowseCountry"]",
        quickBrowseTel: "@_commonLocalizer["quickBrowseTel"]",
        quickBrowseState: "@_commonLocalizer["quickBrowseState"]",
        quickBrowseCounty: "@_commonLocalizer["quickBrowseCounty"]",
        quickBrowseManufacturerSupplied: "@_commonLocalizer["quickBrowseManufacturerSupplied"]",
        quickBrowseGroupCodeName: "@_commonLocalizer["quickBrowseGroupCodeName"]",
        quickBrowseCompanyType: "@_commonLocalizer["quickBrowseCompanyType"]",
        quickBrowseVATID: "@_commonLocalizer["quickBrowseVATID"]",
        quickBrowseCertificateCategory: "@_commonLocalizer["quickBrowseCertificateCategory"]",
        quickBrowseCertificateNo: "@_commonLocalizer["quickBrowseCertificateNo"]",
        quickBrowseSalesperson: "@_commonLocalizer["quickBrowseSalesperson"]",
        quickBrowseSupplierRating: "@_commonLocalizer["quickBrowseSupplierRating"]",
        quickBrowseCustomerRating: "@_commonLocalizer["quickBrowseCustomerRating"]",
        quickBrowseCustomerNo: "@_commonLocalizer["quickBrowseCustomerNo"]",
        quickBrowseZipCode: "@_commonLocalizer["quickBrowseZipCode"]",
        quickBrowseClientName: "@_commonLocalizer["quickBrowseClientName"]",
        quickBrowseRegion: "@_commonLocalizer["quickBrowseRegion"]",
        quickBrowseEmail: "@_commonLocalizer["quickBrowseEmail"]",
        quickBrowseIndustryType: "@_commonLocalizer["quickBrowseIndustryType"]",
        quickBrowseCompanyStatus: "@_commonLocalizer["quickBrowseCompanyStatus"]",
        filtersApplied: "@_commonLocalizer["filters applied"]",
        noFiltersApplied: "@_commonLocalizer["No filters applied"]",
        quickBrowsePartNo: "@_commonLocalizer["quickBrowsePartNo"]",
        quickBrowseCompany: "@_commonLocalizer["quickBrowseCompany"]",
        quickBrowseContact: "@_commonLocalizer["quickBrowseContact"]",
        quickBrowseSalesOrder: "@_commonLocalizer["quickBrowseSalesOrder"]",
        quickBrowseDateOrderedFrom: "@_commonLocalizer["quickBrowseDateOrderedFrom"]",
        quickBrowseDateOrderedTo: "@_commonLocalizer["quickBrowseDateOrderedTo"]",
        quickBrowseRecentOnly: "@_commonLocalizer["quickBrowseRecentOnly"]",
        quickBrowseIncludeClosed: "@_commonLocalizer["quickBrowseIncludeClosed"]",
        quickBrowseCustomerPO: "@_commonLocalizer["quickBrowseCustomerPO"]",
        quickBrowseContract: "@_commonLocalizer["quickBrowseContract"]",
        quickBrowseIncludeOrderSent: "@_commonLocalizer["quickBrowseIncludeOrderSent"]",
        quickBrowseAS6081: "@_commonLocalizer["quickBrowseAS6081"]",
        quickBrowseDatePromisedFrom: "@_commonLocalizer["quickBrowseDatePromisedFrom"]",
        quickBrowseDatePromisedTo: "@_commonLocalizer["quickBrowseDatePromisedTo"]",
        quickBrowseSalesOrderStatus: "@_commonLocalizer["quickBrowseSalesOrderStatus"]",
        quickBrowseSOCheckedStatus: "@_commonLocalizer["quickBrowseSOCheckedStatus"]",
        allowedExtensionsErrorMsg:"@_commonLocalizer["Allowed extensions"]",
        allowedMaxSizeErrorMsg:"@_commonLocalizer["allowedMaxSizeErrorMsg"]",
        fileSizeZeroErrorMsg:"@_commonLocalizer["Please select a file larger than 0 KB"]",
        noImageInSourcingResultMsg:"@_commonLocalizer["This Sourcing Result has no images attached"]",
        cantLoadImageMsg:"@_commonLocalizer["Sorry, that image was not found"]",
        medium:"@_commonLocalizer["Medium"]",
        large:"@_commonLocalizer["Large"]",
        deleteDocument:"@_commonLocalizer["Delete document"]",
        noFileMessage:"@_commonLocalizer["This has no file attached."]",
		quickBrowsePurchaseOrder: "@_commonLocalizer["quickBrowsePurchaseOrder"]",
        quickBrowseDeliveryDateFrom: "@_commonLocalizer["quickBrowseDeliveryDateFrom"]",
        quickBrowseDeliveryDateTo: "@_commonLocalizer["quickBrowseDeliveryDateTo"]",
		quickBrowsePOHubOnly : "@_commonLocalizer["quickBrowsePOHubOnly"]",
        quickBrowseExpediteDateFrom : "@_commonLocalizer["quickBrowseExpediteDateFrom"]",
		quickBrowseExpediteDateTo : "@_commonLocalizer["quickBrowseExpediteDateTo"]",
		quickBrowseIPONo: "@_commonLocalizer["quickBrowseIPONo"]",
		quickBrowseBuyerName: "@_commonLocalizer["quickBrowseBuyerName"]",
        quickBrowseSupplierApproval: "@_commonLocalizer["quickBrowseSupplierApproval"]",
        quickBrowseAS6081: "@_commonLocalizer["quickBrowseAS6081"]",
        quickBrowseChecked: "@_commonLocalizer["quickBrowseChecked"]",
		quickBrowseStatus: "@_commonLocalizer["quickBrowseStatus"]",
    };
    window.constants = {
        MaxIntegerValue: "@Numeric.MaxIntegerValue",
        sortASC: "@sortASC",
        sortDESC: "@sortDESC",
        sortASCName: "@SortColumnDirection.ASC.ToString().ToLower()",
        sortDESCName: "@SortColumnDirection.DESC.ToString().ToLower()",
        maxInt32Value: "@Int32.MaxValue",
        minInt32Value: "@Int32.MinValue",
    }
    window.url = {
        contactDetailPage: `@Navigations.ContactDetails("").CtaUri`,
        companyDetailPage: `@Navigations.CompanyDetails("").CtaUri`,
        manufacturerPage: `@Navigations.ManufacturerDetails("").CtaUri`,
        stockDetaiPage: `@Navigations.StockDetail("").CtaUri`,
        toDoListPage: `@Navigations.ToDo.CtaUri`,
        bomDetailPage: `@Navigations.BOMBrowseDetail.CtaUri`,
        salesOrdersDetailPage:`@Navigations.SalesOrderDetail("").CtaUri`,
        purchaseRequisitionDetailPage: `@Navigations.PurchaseRequisitionDetail("").CtaUri`,
        customerRequirementDetailPage: `@Navigations.CustomerRequirementDetails("").CtaUri`,
        purchaseQuotesDetailPage : `@Navigations.PriceRequestDetails("").CtaUri`,
        invoiceDetailPage: `@Navigations.InvoiceDetail.CtaUri`,
        purchaseQuotesDetailPage : `@Navigations.PriceRequestDetails("").CtaUri`,
		purchaseOrderDetailPage: `@Navigations.PurchaseOrderDetail("").CtaUri`
    }
    window.constants.params = {
        contactId: `@nameof(navigationParams.con)`,
        companyListType: `@nameof(navigationParams.clt)`,
        companyId: `@nameof(navigationParams.cm)`,
        tab: `@nameof(navigationParams.tab)`,
        manufacturerId: `@nameof(navigationParams.mfr)`,
        stockId: `@nameof(navigationParams.stk)`,
        companyName: `@nameof(navigationParams.cmn)`,
        contactName: `@nameof(navigationParams.ctn)`,
        bomId: `@nameof(navigationParams.BOM)`,
        salesOrdersNo: `@nameof(navigationParams.so)`,
        purchaseRequisitionId: `@nameof(navigationParams.prq)`,
        customerRequirementId : `@nameof(navigationParams.req)`,
        purchaseQuoteId : `@nameof(navigationParams.pqt)`,
        invoiceId: `@nameof(navigationParams.inv)`,
        purchaseQuoteId : `@nameof(navigationParams.pqt)`,
		purchaseOrderId: `@nameof(navigationParams.po)`,
        purchaseOrderLines: `@nameof(navigationParams.pols)`,
        purchaseOrderLineIds: `@nameof(navigationParams.polids)`
    }
    window.sessionKeyValue = {
        loginId: `@_sessionManager.LoginID`,
        loginDivisionId: `@_sessionManager.LoginDivisionID`,
    }
</script>
