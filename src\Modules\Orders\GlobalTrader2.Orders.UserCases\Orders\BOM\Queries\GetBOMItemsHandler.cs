﻿using GlobalTrader2.Core.Helpers;
using GlobalTrader2.Dto.Orders.Requirements;
using GlobalTrader2.Orders.UserCases.Orders.BOM.Queries;
using GlobalTrader2.Orders.UserCases.Orders.Requirements.CurrentAtDate.Queries;
using GlobalTrader2.Core.Enums;



namespace GlobalTrader2.Aggregator.UseCases.DataList.GetFilterBOMLinesList
{
    public class GetBOMItemsHandler : IRequestHandler<GetBomItemsQuery, BaseResponse<GetBomItemsResponseDto>>
    {
        private readonly IBaseRepository<BomCustomerRequirementModel> _repository;
        private readonly IMediator _mediator;


        public GetBOMItemsHandler(IBaseRepository<BomCustomerRequirementModel> repository, IMediator mediator)
        {
            _repository = repository;
            _mediator = mediator;
        }


        public async Task<BaseResponse<GetBomItemsResponseDto>> Handle(GetBomItemsQuery request, CancellationToken cancellationToken)
        {
            var responseData = new GetBomItemsResponseDto();
            string displayStatus = string.Empty;


            var parameters = new List<SqlParameter>()
            {
                new SqlParameter("@BOMNo", SqlDbType.Int) { Value = (object?)request.BOMNo ?? DBNull.Value },
                new SqlParameter("@ClientID", SqlDbType.Int) { Value = request.ClientId },
                new SqlParameter("@IsPoHub", SqlDbType.Bit)  { Value = request.IsPoHub }
            };


            var customerRequirements = await _repository.SqlQueryRawAsync(
                $"{StoredProcedures.SelectAll_CustomerRequirement_for_BOM_V2} @BOMNo, @ClientID, @IsPoHub",
                parameters.ToArray()
            );


            if (customerRequirements != null && customerRequirements.Any())
            {
                var items = new List<BomItemDto>();

                foreach (var cReq in customerRequirements)
                {
                    var isAssigned = !string.IsNullOrEmpty(cReq.AssigneeId) &&
                        cReq.AssigneeId
                            .Split(',', StringSplitOptions.RemoveEmptyEntries)
                            .Select(id => int.TryParse(id, out var parsed) ? parsed : 0)
                            .Any(parsedId => parsedId == request.LoginId);


                    // Calculate PriceInBase if currency is different and value is present
                    string priceInBase = null;

                    if (cReq.CurrencyNo.HasValue && cReq.CurrencyNo.Value != request.ClientCurrencyID)
                    {
                        try
                        {
                            var converted = await ConvertValueToBaseCurrencyAsync(cReq.Price, cReq.CurrencyNo.Value);
                            priceInBase = "\r\n" + String.Format(" ({0})", Functions.FormatCurrency(converted, request.ClientCurrencyCode));
                        }
                        catch (Exception)
                        {
                            // Log error but continue processing
                            priceInBase = null;
                        }
                    }


                    var bomItem = new BomItemDto
                    {
                        CustomerRequirementId = cReq.CustomerRequirementId,
                        CustomerRequirementNumber = cReq.CustomerRequirementNumber,
                        PartNo = cReq.Part ?? string.Empty,
                        Part = GetPartWithAlternate(cReq.Part ?? string.Empty, cReq.AlternateStatus, cReq.Alternate),
                        Closed = cReq.Closed,
                        MfrNo = cReq.ManufacturerNo,
                        Mfr = cReq.ManufacturerCode ?? string.Empty,
                        MfrAdvisoryNotes = cReq.MfrAdvisoryNotes ?? string.Empty,
                        Product = cReq.ProductName ?? string.Empty,
                        DC = cReq.DateCode ?? string.Empty,
                        Package = cReq.PackageName ?? string.Empty,
                        Price = !string.IsNullOrEmpty(cReq.CurrencyCode) ? Functions.FormatCurrency(cReq.Price, cReq.CurrencyCode) : string.Empty,
                        TPriceInBom = !string.IsNullOrEmpty(cReq.BOMCurrencyCode) ? Functions.FormatCurrency(cReq.ConvertedTargetValue, cReq.BOMCurrencyCode) : string.Empty,
                        PriceInBase = priceInBase,
                        Quantity = cReq.Quantity,
                        Alt = cReq.Alternate,
                        ROHS = cReq.ROHS,
                        Date = Functions.FormatDate(cReq.DatePromised),
                        CustomerPart = cReq.CustomerPart,
                        Company = request.IsPoHub ? (cReq.ClientName ?? string.Empty) : (cReq.CompanyName ?? string.Empty),
                        CompanyAdvisoryNotes = request.IsPoHub ? string.Empty : Functions.ReplaceLineBreaks(cReq.CompanyAdvisoryNotes ?? string.Empty),
                        CompanyNo = cReq.CompanyNo,
                        SalesmanName = cReq.SalesmanName ?? string.Empty,
                        Salesman = cReq.Salesman,
                        BOMCode = cReq.BOMCode ?? string.Empty,
                        BOMFullName = cReq.BOMFullName ?? string.Empty,
                        IsAs6081Required = cReq.IsAs6081Required ?? string.Empty,
                        AssignedTo = cReq.AssignedTo ?? string.Empty,
                        IsAssignedToMe = isAssigned,
                        Instructions = Functions.ReplaceLineBreaks(cReq.Instructions ?? string.Empty),
                        DisplayStatus = !cReq.Alternate ? displayStatus : null,
                        BOMNo = cReq.BOMNo,
                        Released = cReq.POHubReleaseBy > 0,
                        CMNo = cReq.CompanyNo,
                        HasSourcingResult = request.IsPoHub ? (cReq.HasHubSourcingResult ?? false) : (cReq.HasClientSourcingResult ?? false),
                        IsRequestToPurchaseQuote = (cReq.RequestToPOHubBy ?? 0) > 0,
                        FactorySealed = cReq.FactorySealed == true ? "YES" : "NO",
                        MSL = cReq.MSL,
                        SourcingResult = cReq.SourcingResult > 0,
                        BOMStatus = cReq.BOMStatus ?? string.Empty,
                        IPOClientNo = cReq.ClientNo,
                        IsNoBid = cReq.IsNoBid ?? false,
                        UpdateByPH = cReq.UpdateByPH,
                        RequestToPOHubBy = cReq.RequestToPOHubBy,
                        SupportTeamMemberNo = cReq.SupportTeamMemberNo,
                        SupportTeamMemberName = cReq.SupportTeamMemberName ?? string.Empty,
                        PartWatchHUBIPO = cReq.PartWatchHUBIPO ?? false,
                        PriceIssueBuyAndSell = cReq.PriceIssueBuyAndSell,
                        IsAllocated = cReq.POHubReleaseBy <= 0 && (request.IsPoHub ? (cReq.HasHubSourcingResult ?? false) : (cReq.HasClientSourcingResult ?? false)),
                        IsReleased = cReq.POHubReleaseBy > 0,
                        PackageNo = cReq.PackageNo,
                        ProductNo = cReq.ProductId
                    };

                    items.Add(bomItem);
                }

                // Check HasImportFile using mediator
                bool hasImportFile = false;
                try
                {
                    var hasImportFileQuery = new CheckBOMHasImportFileQuery { BOMId = request.BOMNo };
                    var hasImportFileResponse = await _mediator.Send(hasImportFileQuery, cancellationToken);
                    hasImportFile = hasImportFileResponse?.Data ?? false;
                }
                catch
                {
                    // Ignore errors
                }

                responseData.Items = items;
                var firstReq = customerRequirements[0];
                responseData.AllHasDelDate = firstReq.AllSorcingHasDelDate == 0;
                responseData.AllHasProduct = firstReq.AllSorcingHasProduct == 0;
                responseData.HasImportFile = hasImportFile;
                responseData.DisplayStatus = displayStatus;
            }
            else
            {
                responseData.Items = new List<BomItemDto>();
                responseData.AllHasDelDate = false;
                responseData.AllHasProduct = false;
                responseData.HasImportFile = false;
                responseData.DisplayStatus = string.Empty;
            }

            return new BaseResponse<GetBomItemsResponseDto>
            {
                Success = true,
                Data = responseData
            };
        }

        private static string GetPartWithAlternate(string strPart, byte? intAltStatus, bool isAlternate)
        {
            if (!string.IsNullOrEmpty(strPart) && intAltStatus > 0 && isAlternate)
            {
                string alternateText = intAltStatus switch
                {
                    (byte)CustReqAlternative.Alternative => "Alternate",
                    (byte)CustReqAlternative.PossibleAlternative => "Possible Alternate", 
                    (byte)CustReqAlternative.FirmAlternative => "Firm Alternate",
                    _ => "Alternate"
                };
                return $"{strPart} ({alternateText})";
            }
            return strPart ?? string.Empty;
        }

        public async Task<double> ConvertValueToBaseCurrencyAsync(double? dblValueToConvert, int currencyNo)
        {
            var dblRate = await _mediator.Send(new GetCurrentAtDateQuery()
            {
                CurrencyNo = currencyNo,
                DatePoint = DateTime.Now,
            });
            double dblValueToConvert_AsDouble = dblValueToConvert == null ? 0 : (double)dblValueToConvert;
            return dblValueToConvert_AsDouble / dblRate.Data!.ExchangeRate;
        }
    }
}
