﻿import { TableFilterComponent } from '../../../components/table-filter/table-filter.component.js?v=#{BuildVersion}#';
import { FieldType } from '../../../components/table-filter/constants/field-type.constant.js?v=#{BuildVersion}#';
import { TextFilterHelper } from "../../../helper/text-filter-helper.js?v=#{BuildVersion}#";
import { DebounceHelper } from '../../../helper/debounce-helper.js?v=#{BuildVersion}#';
import { NumberType } from '../../../components/table-filter/constants/number-type.constant.js?v=#{BuildVersion}#';

$(async () => {
    const purchaseRequisitionsManager = new PurchaseRequisitionsManager();
    await purchaseRequisitionsManager.init();
});
class PurchaseRequisitionsManager {
    constructor() {
        this.$filterToggleButton = $('#btn-filter');
        this.isShowFilter = true;
        this.$purchaseRequisitionsSectionBox = $('#purchase-requisitions-box');
        this.filterInputs = [
            {
                fieldType: FieldType.TEXT,
                label: localizedTitles.partNo,
                name: 'Part',
                id: 'Part',
                value: '',
                attributes: {
                    "maxlength": 50
                },
            },
            {
                fieldType: FieldType.SELECT,
                label: localizedTitles.salesperson,
                name: 'Salesman',
                id: 'Salesman',
                value: '',
                options: {
                    serverside: false,
                    endpoint: 'lists/employee',
                    valueKey: 'loginId',
                    textKey: 'employeeName',
                    isHideRefresButton: false,
                    isCacheApplied: true
                },
            },
            {
                fieldType: FieldType.TEXT,
                label: localizedTitles.company,
                name: 'CMName',
                id: 'CMName',
                value: '',
                attributes: {
                    "maxlength": 50
                },
            },
            {
                fieldType: FieldType.DATE,
                label: localizedTitles.dateOrderedFrom,
                name: 'DateOrderedFrom',
                id: 'DateOrderedFrom',
                value: '',
            },
            {
                fieldType: FieldType.TEXT,
                label: localizedTitles.contact,
                name: 'Contact',
                id: 'Contact',
                value: '',
                attributes: {
                    "maxlength": 50
                },
            },
            {
                fieldType: FieldType.DATE,
                label: localizedTitles.dateOrderedTo,
                name: 'DateOrderedTo',
                id: 'DateOrderedTo',
                value: '',
            },

            {
                fieldType: FieldType.NUMBER,
                label: localizedTitles.salesOrder,
                name: 'SONo',
                id: 'SONo',
                value: '',
                attributes: {
                    "data-input-type": "numeric",
                    "data-input-format": "int",
                    "data-input-min": 0,
                    "data-input-max": 2147483647,
                    "data-input-type-allow-empty": true
                },
                extraPros: {
                    numberType: NumberType.INT
                },

            },

        ];
        this.tableFilter = null;
        this.$purchaseRequisitionTable = $('#purchase-requisitions-table');
      
        this.pageSize = this.$purchaseRequisitionTable.data('default-page-size') || 10;
        this.defaultPageIndex = this.$purchaseRequisitionTable.data('default-page-index');
        this.$lockUnlockButton = $('#lock-unlock-button');
        this.isLockFilter = $('#lock-unlock-button').data('init-value') === 'True';
        this.$warningSection = $('#warning-section');
        this.filterStates = JSON.parse($('#filter-section-wrapper').attr("data-states"));
        this.sortIndex = $('#purchase-requisitions-table').data('default-order-by') || 0;
        this.sortDirection = $('#purchase-requisitions-table').data('default-sort-dir') || window.constants.sortASC;
        this.currentTab = $('#purchase-requisitions-table').data('default-current-tab');
    }
    async init() {
        this.setUpSwitchingTab();
        await this.setUpFilter();
        this.initDataTable();
        this.setUpPurchaseRequisitionSectionBox();
     

    }
    async setUpFilter() {
        this.tableFilter = new TableFilterComponent('#filter-section-wrapper', 'Filter Results', {
            inputConfigs: this.filterInputs
        });
        await this.tableFilter.init();
        this.hideSalesPersonFilter(this.currentTab);
        this.tableFilter.on('applied.mtf', () => {
            this.$purchaseRequisitionTable.DataTable().rows().deselect();
            this.isShowFilter = true;
            this.tableFilter.updateAppliedFilterText();
            GlobalTrader.Helper.reloadPagingDatatableServerSide(this.$purchaseRequisitionTable.DataTable(), true);

        })

        this.tableFilter.on('cancel.mtf', () => {
            if (this.currentXhr) {
                this.currentXhr.abort();
                this.$warningSection.show();
                $('#purchase-requisitions-box').section_box('option', 'loading', false);
                this.showFilterButtons(true);
                this.tableFilter.toggleApplyCancelButtons(true);
            }
        })

        this.tableFilter.on('off.mtf', () => {
            this.$filterToggleButton.prop('checked', false)
            this.$purchaseRequisitionTable.DataTable().rows().deselect();
            this.isShowFilter = false;
            GlobalTrader.Helper.reloadPagingDatatableServerSide(this.$purchaseRequisitionTable.DataTable());
        });

        this.tableFilter.on('reset.mtf', () => {
            history.pushState({ additionalInformation: 'Reset filter' }, 'Reset filter', `/Orders/PurchaseRequisition`);

        });
        Object.values(this.filterStates).forEach(input => {

            if (input.isOn && input.isShown && input.value && input.name) {
                const currentInput = this.tableFilter.getInputElementByName(input.name);
                switch (input.fieldType) {
                    case FieldType.TEXT:
                        currentInput.setValue(TextFilterHelper.getFilterValue(input.searchType, input.value));
                        break;
                    case FieldType.NUMBER:
                        currentInput.setState(input);
                        break;

                    default:
                        currentInput.setValue(input.value);
                        break;
                }

                currentInput.setRequiredCheckbox(true);
                currentInput.syncInputState();
                currentInput.triggerControlChanged();
            }
        });
    }
    setUpPurchaseRequisitionSectionBox() {
        this.$purchaseRequisitionsSectionBox.section_box({
            loading: true,
            loadingContentId: 'purchase-requisitions-table_wrapper',
            onRefreshClick: async (event, ui) => {
                this.refreshDatatable();
            }
        });
    }
    async refreshDatatable() {
        GlobalTrader.Helper.reloadPagingDatatableServerSide(this.$purchaseRequisitionTable.DataTable());
    }
    setUpSwitchingTab() {
        $('button[data-bs-toggle="tab"]').on('shown.bs.tab', (e) => {
            this.currentTab = $(e.target).data('view-level');
            this.hideSalesPersonFilter(this.currentTab);
            GlobalTrader.Helper.reloadPagingDatatableServerSide(this.$purchaseRequisitionTable.DataTable(), true);
        });
    }
    initDataTable() {
        this.showFilterButtons(false);
        $('#purchase-requisitions-box-content').hide();
        let tableColumnsDefine = [
            {
                data: (row) => (
                    {
                        salesOrderLineId: row.salesOrderLineId,
                        salesOrderNumber: row.salesOrderNumber,
                    }
                ),
                name: 'No',
                title: `${localizedTitles.no}`,
                type: 'string',
                render: (data, type, row) => {
                    const purReqUrl = GlobalTrader.PageUrlHelper.Get_Url_PurchaseRequisition(data.salesOrderLineId);
                    return `<span><a class="dt-hyper-link" href="${purReqUrl}">${data.salesOrderNumber}/${data.salesOrderLineId}</a></span>`;
                },
            },
            {
                data: (row) => (
                    {
                        partNo: row.partNo,
                        manufacturerCode: row.manufacturerCode,
                        manufacturerNo: row.manufacturerNo,
                        imageUrl: row.rohsInfo.imageUrl
                    }
                ),
                name: 'partNo_manuFac',
                title: `<span class="dt-column-title d-flex flex-column gap-1">
                            <div>${localizedTitles.partNo}</div>
                            <div class="dt-column-line"></div>
                            <div>${localizedTitles.manufacturer}</div>
                        </span>`, 
                type: 'string',
                render: (data, type, row) => {
                    let manufacturerCode = "";
                    const escapedManufacturerCode = DataTable.render.text().display(data.manufacturerCode ?? "");
                    manufacturerCode = GlobalTrader.StringHelper.setCleanTextValue(escapedManufacturerCode);
                    const url = GlobalTrader.StringHelper.setCleanTextValue(data.imageUrl);
                    return `<p class="m-0 d-flex flex-row flex-nowrap align-items-center gap-5px" style="min-height: 15px;">${data.partNo} ${url ? `<img src="${url}" alt="rohs">` : ''}</p><p class="m-0" style="min-height: 15px;"><a class="dt-hyper-link" href="${GlobalTrader.PageUrlHelper.Get_URL_Manufacturer(data.manufacturerNo)}">${manufacturerCode}</p>`;
                },
            },
            {
                data: 'price',
                name: 'unitPrice',
                title: `${localizedTitles.price}`,
                type: 'string'
            },
            {
                data: 'backOrderQuantity',
                name: 'quantity',
                title: `${localizedTitles.quantity}`,
                type: 'string'
            },
            {
                data: (row) => (
                    {
                        companyName: row.companyName,
                        companyNo: row.companyNo,
                        contactNo: row.contactNo,
                        contactName: row.contactName,
                    }
                ),
                name: 'company_contact',
                title: `<span class="dt-column-title d-flex flex-column gap-1">
                            <div>${localizedTitles.company}</div>
                            <div class="dt-column-line"></div>
                            <div>${localizedTitles.contact}</div>
                        </span>`,
                type: 'string',
                render: (data, type, row) => {
                    return `<p class="m-0" style="min-height: 15px;"><a class="dt-hyper-link" href="${GlobalTrader.PageUrlHelper.Get_URL_Company(data.companyNo)}">${data.companyName}</p><p class="m-0" style="min-height: 15px;"><a class="dt-hyper-link" href="${GlobalTrader.PageUrlHelper.Get_URL_Contact(data.contactNo)}">${data.contactName}</p>`;
                },
            },
            {
                data: 'datePromised',
                name: 'datePromised',
                title: `${localizedTitles.promised}`,
                type: 'string'
            },
        ]
        this.$purchaseRequisitionTable
            .DataTable({
                serverSide: true,
                ajax: {
                    url: '/api/orders/purchase-requisition/list',
                    type: 'POST',
                    contentType: 'application/json',
                    beforeSend: (xhr) => {
                        this.currentXhr = xhr;
                        this.$warningSection.hide();
                        this.tableFilter.toggleApplyCancelButtons(false);
                    },
                    error: function (xhr, status, error) {
                        console.log("AJAX Error: ", status, error);
                    },
                    data: (data) => {
                        let sortDir = data.order.length !== 0 ? GlobalTrader.SortHelper.getSortDirIdByName(data.order[0].dir) : this.sortDirection;
                        const filtersData = this.tableFilter.getDisplayedFilterValues();
                        if (this.isShowFilter) {
                            return JSON.stringify({
                                draw: data.draw,
                                start: data.start,
                                length: data.length,
                                sortDir: sortDir,
                                viewLevel: this.currentTab,
                                orderBy: data.order.length !== 0 ? data.order[0].column : this.sortIndex,
                                filters: filtersData,
                                saveStates: this.isLockFilter,

                            });
                        } else {
                            return JSON.stringify({
                                draw: data.draw,
                                start: data.start,
                                length: data.length,
                                sortDir: sortDir,
                                viewLevel: this.currentTab,
                                orderBy: data.order.length !== 0 ? data.order[0].column : this.sortIndex,
                            });
                        }
                    },


                },
                info: true,
                scrollCollapse: true,
                responsive: true,
                select: false,
                displayStart: this.defaultPageIndex * this.pageSize,
                paging: true,
                ordering: true,
                columnDefs: [
                    { "orderSequence": [window.constants.sortASCName, window.constants.sortDESCName], "targets": "_all" },
                ],
                searching: false,
                pageLength: this.pageSize,
                lengthMenu: [5, 10, 25, 50],


                headerCallback: (thead) => {
                    $(thead).find("th").addClass('align-baseline');
                },
                order: [[this.sortIndex, GlobalTrader.SortHelper.getSortDirNameById(this.sortDirection)]],
                language: {
                    emptyTable: `<i>${window.localizedStrings.noDataFound}</i>`,
                    zeroRecords: `<i>${window.localizedStrings.noDataFound}</i>`,
                    infoFiltered: "",
                    lengthMenu: "_MENU_ per page",
                },
                columns: tableColumnsDefine,
                rowId: 'salesOrderLineId',
                createdRow: function (row, data, dataIndex) {
                    $(row).attr('tabindex', '0');
                },
                dom: '<"dt-layout-row dt-layout-table" <"dt-layout-cell dt-layout-full" rt >>' +
                    '<"dt-layout-row" <"dt-layout-cell dt-layout-start" i l >' +
                    '<"dt-layout-cell dt-layout-end" p >><"clear">'
            })
            .on('preXhr.dt', () => {
                this.$purchaseRequisitionsSectionBox.section_box("option", "loading", true);
                this.showFilterButtons(false);
                if (this.tableFilter.state !== this.tableFilter.ViewState.VISIBLE) {
                    this.tableFilter.toggleApplyCancelButtons(false);
                }
            })
            .on('draw.dt', () => {
                this.$purchaseRequisitionsSectionBox.section_box("option", "loading", false);
                $('#purchase-requisitions-box-content').show();
                this.$purchaseRequisitionTable.DataTable().columns.adjust();
                this.showFilterButtons(true);

                if (this.tableFilter.state === this.tableFilter.ViewState.INVISIBLE) {            
                    this.tableFilter.toggleFilterVisible(false);
                    this.tableFilter.setState(this.tableFilter.ViewState.INVISIBLE)
                } else if (this.tableFilter.state !== this.tableFilter.ViewState.VISIBLE) {
                    this.tableFilter.toggleFilterVisible(false);
                }
                this.tableFilter.toggleApplyCancelButtons(true);
                this.tableFilter.updateAppliedFilterText();

                // Remove neutral sorting icon
                const tableId = this.$purchaseRequisitionTable.DataTable().table().node().id;
                $(`#${tableId} thead th`)
                    .removeClass('dt-orderable-asc dt-orderable-desc')
                    .addClass('position-relative');

                $(`#${tableId} thead th:not(.dt-orderable-none)`)
                    .attr('role', 'button');

                $(`#${tableId} thead th .dt-column-order`).addClass('dt-column-order-custom');

                $(`#${tableId} .badge-hover-text`).each((index, element) => {
                    const handleOnStopMouseEnterDebounce = DebounceHelper.debounce(this.handleOnStopMouseEnter, 100)
                    const handleOnStopMouseLeaveDebounce = DebounceHelper.debounce(this.handleOnStopMouseLeave, 2000)

                    let mouseLeaveTimeoutTask;

                    $(element)
                        .on("mouseenter", (event) => {
                            // Show on stop label 
                            handleOnStopMouseEnterDebounce(event, element);

                            // Cancel hide on stop label
                            if (mouseLeaveTimeoutTask) clearTimeout(mouseLeaveTimeoutTask);
                        })
                        .on("mouseleave", (event) => {

                            // Hide on stop label 
                            mouseLeaveTimeoutTask = handleOnStopMouseLeaveDebounce(event, element);
                        });
                })
            })
            ;

        this.$filterToggleButton.on("change", () => {
            this.isShowFilter = this.$filterToggleButton.is(":checked");
            this.$purchaseRequisitionTable.DataTable().rows().deselect();
            this.showFilterSection(this.isShowFilter)
            GlobalTrader.Helper.reloadPagingDatatableServerSide(this.$purchaseRequisitionTable.DataTable());
        });

        this.$lockUnlockButton.on("click", () => {
            this.isLockFilter = !this.isLockFilter;
            this.setLockOrUnlockFilter(this.isLockFilter);
        })

    }
    showFilterSection(show) {
        show ? $('#filter-section-wrapper').show() : $('#filter-section-wrapper').hide();
    }
    hideSalesPersonFilter(viewLevel) {
        const inputs = this.tableFilter.getInputs();
        viewLevel == 0 ? inputs["Salesman"].instance.wrapper.hide() : inputs["Salesman"].instance.wrapper.show();
    }
    setLockOrUnlockFilter(isLock) {
        const innerHtml = isLock ? '<img alt="lock-button" src="/img/icons/lock.svg">' : '<img alt="lock-button" src="/img/icons/lock-open.svg">';
        this.$lockUnlockButton.html(innerHtml);
    }
    showFilterButtons(isShow) {
        if (isShow) {
            this.$lockUnlockButton.removeClass('force-disabled-content');
            this.tableFilter.enableButtons(['apply', 'off', 'hide', 'reset', 'show']);
        } else {
            this.$lockUnlockButton.addClass('force-disabled-content');
            this.tableFilter.disableButtons(['apply', 'off', 'hide', 'reset', 'show']);
        }
    }
}
