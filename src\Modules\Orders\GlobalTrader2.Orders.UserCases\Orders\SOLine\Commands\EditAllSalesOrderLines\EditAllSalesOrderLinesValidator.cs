
using GlobalTrader2.Dto.LoginManager;

namespace GlobalTrader2.Orders.UserCases.Orders.SOLine.Commands.EditSalesOrderLine
{
    public class EditAllSalesOrderLinesValidator : AbstractValidator<EditAllSalesOrderLinesCommand>
    {
        public EditAllSalesOrderLinesValidator()
        {
            RuleFor(x => x.PromiseReasonNo).NotEqual(default(int));
            RuleFor(x => x.OldEarliestDatePromised).NotEqual(default(DateTime));
            RuleFor(x => x.AllowEditDatePromisedBetweenCurrentMonthAndEnd).NotNull();
            RuleFor(x => x.IsSOAuthorized).NotNull();
            RuleFor(x => x.IsSOAutoAuthorized).NotNull();

            RuleFor(x => x.DatePromised)
                .Must((model, datePromised) =>
                {
                    var today = DateTime.Today;
                    return datePromised >= today;
                })
                .WithMessage("Promised date should be between current date and end of promised date month");
        }
    }
}