﻿import { ReadyToShipService } from './ready-to-ship-service.js?v=#{BuildVersion}#';
export class ReadyToShipManager {

    constructor({ readyToShipData, successCallback }) {
        this.$dialog = $('#ready-to-ship-dialog');
        this.readyToShipData = readyToShipData;
        this.$form = $('#ready-to-ship-form');
        this.apiService = ReadyToShipService;
        this.successCallback = successCallback;
    }
    initialize() {
        this.setUpDialog();
    }
    setUpDialog() {
        this.$dialog.dialog({
            maxHeight: $(window).height(),
            width: "auto",
            height: "auto",
            autoOpen: false,
            draggable: false,
            modal: true,
            open: async () => {
                this.$dialog.dialog("setLoading", true);
                this.bindingData();
                this.$dialog.dialog("setLoading", false);
            },
            close: () => {
                this.$dialog.find(".form-error-summary").hide();
            },
            buttons: [
                {
                    text: window.localizedStrings.yes,
                    class: 'btn btn-primary',
                    id: 'ready-to-ship-btn',
                    html: `<img src="/img/icons/check.svg" alt="Yes icon"/>${window.localizedStrings.yes}`,
                    click: async () => {
                            this.$dialog.find(".form-error-summary").hide();
                            this.$dialog.dialog("setLoading", true);
                            $("#ready-to-ship-btn").hide();
                            $("#ready-to-ship-close-btn").hide();
                                let response = await this.allowToShip();
                                this.$dialog.dialog("setLoading", false);
                             if (!response?.success) {
                            $("#ready-to-ship-btn").show();
                            $("#ready-to-ship-close-btn").show();
                                    showToast("danger", response.title);
                                    return;
                            }
                            $("#ready-to-ship-btn").show();
                            $("#ready-to-ship-close-btn").show();
                            this.$dialog.dialog("close");
                            showToast('success', window.localizedStrings.saveChangedMessage);
                             if (this.successCallback) this.successCallback();
                    }
                },
                {   id:'ready-to-ship-close-btn',
                    text: window.localizedStrings.no,
                    class: 'btn btn-danger',
                    html: `<img src="/img/icons/xmark.svg" alt="No icon"/>${window.localizedStrings.no}`,
                    click: () => {
                        this.$dialog.dialog('close');
                    }
                }
            ],
        });
    }
    bindingData() {
        this.$form.find("span[data-field]").toArray().forEach(element => {
            const $el = $(element);
            const fieldName = $el.data('field');
            let value = this.getPropertyCaseInsensitive(this.readyToShipData, fieldName);
            $el.text(value);
        });
    }
    getPropertyCaseInsensitive(obj, key) {
        key = key.toLowerCase();
        for (let prop in obj) {
            if (prop.toLowerCase() === key) {
                return obj[prop];
            }
        }
        return undefined; // not found
    }
   async allowToShip() {
       return this.apiService.AllowReadyToShip(this.readyToShipData.salesOrderId, this.$form);
    }
    reloadData(data) {
        this.readyToShipData = data;
    }
}