using GlobalTrader2.Core.Constants;
using GlobalTrader2.Core.Interfaces;
using GlobalTrader2.Aggregator.UseCases.FileUpload.Commands.InsertSourcingImage;
using GlobalTrader2.Core.Exceptions;

namespace GlobalTrader2.Aggregator.UseCases.FileUpload.Commands.PostMultipleMediaDocument
{
    public class PostMultipleMediaDocumentHandler(IBlobStorageService blobStorageService, IMediator mediator) : IRequestHandler<PostMultipleMediaDocumentCommand, BaseResponse<bool>>
    {
        private readonly IMediator _mediator = mediator;
        private readonly IBlobStorageService _blobStorageService = blobStorageService;
        private const string propertyMessage = "Upload File";

        public async Task<BaseResponse<bool>> Handle(PostMultipleMediaDocumentCommand request, CancellationToken cancellationToken)
        {
            var response = new BaseResponse<bool>
            {
                Success = false,
            };

            if (request.FileUploadItems == null || !request.FileUploadItems.Any())
            {
                return response;
            }
            
            switch (request.SectionName)
            {
                case ImageSourceFromConstant.GILine:
                    break;
                case ImageSourceFromConstant.GI:
                    break;
                case ImageSourceFromConstant.QC:
                    //usp_insert_SourcingImage
                    foreach (var file in request.FileUploadItems)
                    {
                        await _mediator.Send(new InsertSourcingImageCommand
                        {
                            SourcingNo = (int)request.RefId!,
                            Caption = file.Caption,
                            ImageName = file.FileName,
                            UpdateBy = request.UpdateBy,
                        });
                    }
                    break;
                default:
                    break;
            }


            //Upload to blob
            foreach ( var file in request.FileUploadItems)
            {
                if (file.File == null || file.FileName == null)
                {
                    continue;
                }

                await _blobStorageService.UploadBlobAsync(BlobStorage.MediaContainerName, file.FileName, GetContentType(file.FileName), file.File);
            }

            response.Data = true;
            response.Success = true;

            return response;
        }

        private static string GetContentType(string fileName)
        {
            var extension = Path.GetExtension(fileName).ToLowerInvariant();
            return extension switch
            {
                ".jpeg" => "image/jpeg",
                ".jpg" => "image/jpeg",
                ".png" => "image/png",
                ".gif" => "image/gif",
                ".bmp" => "image/gif",
                _ => throw new ValidationException(new List<BaseError>() {
                    new BaseError()
                    {
                        PropertyMessage = propertyMessage,
                        ErrorMessage = "Uploaded file is not an allowed type"
                    }
                })
            };
        }
    }
}