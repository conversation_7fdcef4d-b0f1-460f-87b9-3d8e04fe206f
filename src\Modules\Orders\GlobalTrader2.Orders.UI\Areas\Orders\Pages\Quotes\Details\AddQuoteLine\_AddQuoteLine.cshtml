@inject IStringLocalizer<GlobalTrader2.SharedUI.CommonResources> _commonLocalizer

<div id="add-quote-line-dialog" class="dialog-container d-none" title="Lines">
    <div class="dialog-description">
        <div class="d-flex justify-content-between">
            <div class="text-uppercase">
                <h5 id="add-edit-company-address-form-title" class="text-uppercase">Add New Quote Line</h5>
            </div>
            <span>
                <span class="me-1 required">*</span>@_commonLocalizer["denotes a required field"]
            </span>
        </div>
        <div class="line"></div>
    </div>
    <div class="dialog-content">
        <div id="add-quote-line-stepper-container"></div>
        <div class="step-pane">
            <form id="add-new-quote-line-select-source-form">
                <p>Select the source for the new Line</p>
                <div class="form-control-wrapper row col-5">
                    <span class="form-label fw-bold col-3">
                        Select Source
                    </span>
                    <div class="col-9 d-flex flex-column gap-2">
                        <div class="d-flex gap-2">
                            <input class="form-check-input check-md" type="radio" name="selectSource" id="selectSourceNewLineItem" value="newLineItem" checked="">
                            <label for="selectSourceNewLineItem">New Line Item</label>
                        </div>
                        <div class="d-flex gap-2">
                            <input class="form-check-input check-md" type="radio" name="selectSource" id="selectSourceFromRequirements" value="fromRequirements">
                            <label for="selectSourceFromRequirements">
                                From Requirements
                            </label>
                        </div>
                        <div class="d-flex gap-2">
                            <input class="form-check-input check-md" type="radio" name="selectSource" id="selectSourceFromRequirementsSourcingResults" value="fromRequirementsSourcingResults">
                            <label for="selectSourceFromRequirementsSourcingResults">
                                From Requirement Sourcing Results
                            </label>
                        </div>
                        <div class="d-flex gap-2">
                            <input class="form-check-input check-md" type="radio" name="selectSource" id="selectSourceFromService" value="fromService">
                            <label for="selectSourceFromService">
                                From Service
                            </label>
                        </div>
                        <div class="d-flex gap-2">
                            <input class="form-check-input check-md" type="radio" name="selectSource" id="selectSourceFromStock" value="fromStock">
                            <label for="selectSourceFromStock">
                                From Stock
                            </label>
                        </div>
                        <div class="d-flex gap-2">
                            <input class="form-check-input check-md" type="radio" name="selectSource" id="selectSourceFromLot" value="fromLot">
                            <label for="selectSourceFromStock">
                                From Lot
                            </label>
                        </div>
                    </div>

                </div>
            </form>
        </div>
        <div class="step-pane d-none">
            <div id="select-item-from-requirement-container">
                <p>
                    Search for and select the item you would like to use as the source for the new Quote Line and press <b>Continue</b>
                </p>
                <div id="select-item-from-requirement-filter"></div>
                <div>
                    <button class="btn btn-primary" id="search-select-item-from-requirement-btn" disabled>
                        <i class="fa-solid fa-magnifying-glass"></i>
                        <span class="lh-base">Search</span>
                    </button>
                </div>
            </div>
            <div id="select-item-from-requirement-sourcing-result-container">
                <p>
                    Search for and select the item you would like to use as the source for the new Quote Line and press <b>Continue</b>
                </p>
                <div id="select-item-from-requirement-sourcing-result-filter"></div>
                <div>
                    <button class="btn btn-primary" id="search-select-item-from-requirement-sourcing-result-btn" disabled>
                        <i class="fa-solid fa-magnifying-glass"></i>
                        <span class="lh-base">Search</span>
                    </button>
                </div>
            </div>
            <div id="select-item-from-service-container">
                <p>
                    Search for and select the item you would like to use as the source for the new Quote Line and press <b>Continue</b>
                </p>
                <form class="form-control-wrapper" id="select-item-from-service-form">
                    <div class="col-12 row">
                        <div id="select-item-from-service-search-container" class="col-5 form-control-wrapper">
                        </div>
                        <div class="col-6 mt-1 form-control-wrapper ps-0">
                            <button class="btn btn-primary" id="search-select-item-from-service-search-btn" disabled="">
                                <i class="fa-solid fa-magnifying-glass"></i>
                                <span class="lh-base">Search</span>
                            </button>
                        </div>
                    </div>
                </form>
            </div>
            <div id="select-item-from-stock-container">
                <p>
                    Search for and select the item you would like to use as the source for the new Quote Line and press <b>Continue</b>
                </p>
            </div>
        </div>
        <div class="step-pane d-none">
            <form id="add-new-quote-line-enter-detail-form">
                <p>Enter the details of the new Quote Line and press <b>Save</b></p>
            </form>
        </div>
        <div class="step-pane d-none">
            <p>
                Search the Lot details for the new Quote Line and press <b>Save</b>
            </p>
            <div>
                <div>
                    <canvas width="20" height="20" style="border: 1px solid #000000; font-weight: bold; background-color: #e0e0e0; width:12px;"></canvas>
                    <span>Quantity In stock (Not available)</span>
                </div>
                <div>
                    <canvas width="20" height="20" style="border: 1px solid #000000;font-weight: bold; background-color: #FFBF00; width:12px;"></canvas>
                    <span>
                        Stock Booked for QuoteLine
                    </span>
                </div>
            </div>
        </div>
    </div>
</div>