﻿@using GlobalTrader2.Dto.Companies.Details
@using GlobalTrader2.Dto.PurchaseOrder
@using GlobalTrader2.Dto.SalesOrderLine
@using GlobalTrader2.SharedUI.Helper
@using GlobalTrader2.Core.Enums
@using Microsoft.AspNetCore.Http
@using Microsoft.AspNetCore.Mvc.Localization
@using Microsoft.Extensions.Localization
@using Microsoft.AspNetCore.Antiforgery

@model GlobalTrader2.Orders.UI.Areas.Orders.Pages.SalesOrders.Details.Index
@inject IStringLocalizer<GlobalTrader2.SharedUI.CommonResources> _commonLocalizer
@inject IStringLocalizer<GlobalTrader2.SharedUI.MessageResources> _messageLocalizer
@inject IViewLocalizer _localizer
@inject Microsoft.AspNetCore.Antiforgery.IAntiforgery Antiforgery
@inject IHttpContextAccessor ContextAccessor

@{
    var sectionBoxClasses = HtmlHelperExtensions.GetSectionBoxClass();
    var headerClasses = HtmlHelperExtensions.GetHeaderClass();
    var contentClasses = HtmlHelperExtensions.GetContentClass();
    ViewData["TodoDialogTittle"] = _commonLocalizer["Main Information"];
    var disableCloseButton = Model.SalesOrderGeneralInfo.Status == SalesOrderStatus.Complete.ToString();
    var httpContext = ContextAccessor.HttpContext;
    var antiForgeryToken = httpContext != null ? Antiforgery.GetAndStoreTokens(httpContext).RequestToken : string.Empty;
}
<input type="hidden" id="hidSalesOrderStatus" />
<input type="hidden" id="hidSalesOrderNumber" />
<input type="hidden" id="hidCustomer" />
<input type="hidden" id="isCurrencyInSameFaimly" />
<input type="hidden" id="isSOAuthorised" />
<input type="hidden" id="oldFreight" />
<input type="hidden" id="hidCurrencyCode" />
<input type="hidden" id="hidCurrencyNo" />
<input type="hidden" id="ctlDateOrdered" />
<div id="sales-orders-main-information-box" class="@sectionBoxClasses mb-3">
    <div class="@headerClasses">
        <span class="section-box-title">
            @_commonLocalizer["Main Information"]
        </span>
        <div class="section-box-button-group" style="display: none">
            <div class="d-flex flex-wrap gap-2">
                @if(Model.SalesOrderMainInfoViewModel.SalesOrderMainInfoPermission.CanEdit && !Model.IsGlobalSalesAccessMember)
                {
                    var excludedEditSatus = new string[] { Core.Enums.SalesOrderStatus.Complete.ToString() };
                    <button class="btn btn-primary" @(excludedEditSatus.Contains(Model.SalesOrderMainInfoViewModel.SOGeneralInfo.Status) ? "disabled" : "") id="edit-main-info-button">
                        <img src="~/img/icons/edit-3.svg" alt="@_commonLocalizer["Edit"]" />
                        <span>@_commonLocalizer["Edit"]</span>
                    </button>
                }
                @if (Model.SalesOrderMainInfoViewModel.SalesOrderMainInfoPermission.CanNotify)
                {
                    <button class="btn btn-warning" id="so-notify-btn" disabled="@disableCloseButton">
                        <img src="~/img/icons/send.svg" alt="Notify icon" width="18" height="18" />
                        <span>@_commonLocalizer["Notify"]</span>
                    </button>
                }
                @if (Model.SalesOrderMainInfoViewModel.SalesOrderMainInfoPermission.CanCloseSalesOrder)
                {
                    <button class="btn btn-danger" id="close-sales-order-btn" disabled="@disableCloseButton">
                        <img src="~/img/icons/x-circle.svg" alt="Close icon" width="18" height="18" />
                        <span>@_commonLocalizer["Close"]</span>
                    </button>
                }
                @if (Model.SalesOrderMainInfoViewModel.SalesOrderMainInfoPermission.CanCreateIpo)
                {
                    <button class="btn btn-primary" id="main-info-create-ipo-btn" disabled>
                        <img src="~/img/icons/edit-3.svg" alt="@_localizer["Create IPO"]" />
                        <span>@_localizer["Create IPO"]</span>
                    </button>
                }
                @if (Model.SalesOrderMainInfoViewModel.SalesOrderMainInfoPermission.CanConfirmSOSent)
                {
                    <button class="btn btn-primary" id="confirm-so-sent-btn"
                            data-antiforgerytoken="@antiForgeryToken">
                        <img src="~/img/icons/check-circle.svg" alt="Confirm icon" width="18" height="18" />
                        <span>@_commonLocalizer["Confirm SO Sent"]</span>
                    </button>
                }
                @if (Model.CanPayByCreditCard)
                {
                    <button class="btn btn-primary" id="pay-by-credit-card-btn">
                        <img src="~/img/icons/credit-card.svg" alt="@_commonLocalizer["Pay By Credit Card"]" width="18" height="18" />
                        <span>@_commonLocalizer["Pay By Credit Card"]</span>
                    </button>
                }
                <button class="btn btn-primary" id="so-detail-view-tree-btn">
                    <img src="~/img/icons/plus.svg" alt="View tree icon" width="18" height="18" />
                    <span>@_commonLocalizer["View Tree"]</span>
                </button>
                <button class="btn btn-primary" id="add-todo-task">
                    <img src="~/img/icons/plus.svg" alt="Add icon" width="18" height="18" />
                    <span>@_commonLocalizer["AddTask"]</span>
                </button>
                <button class="btn btn-primary" id="view-todo-task">
                    <img src="~/img/icons/edit-3.svg" alt="Edit icon" width="18" height="18" />
                    <span></span>
                </button>
            </div>
        </div>
    </div>
    <div class="@contentClasses" style="display: none">
        <div id="sales-orders-main-info-wrapper">
            <div id="warning-section" class="alert alert-warning d-flex align-items-center gap-2 d-none" role="alert">
                <span><img width="18" height="18" src="/img/icons/triangle-exclamation-solid.svg"
                        alt="triangle exclamation solid icon"></span>
                <span>@_messageLocalizer["SOLines_DifferCurrency"]</span>
            </div>
            <div class="row m-0">
                <div class="col-6">
                    <div class="row flex-nowrap mb-2 bg-info-hover">
                        <div class="label col-2 p-0 fw-bold">
                            <span>@_commonLocalizer["Customer"]</span>
                        </div>
                        <div class="value col-10">
                            <span id="sales-order-form-customer-name"></span>
                        </div>
                    </div>
                    <div class="row flex-nowrap mb-2 bg-info-hover">
                        <div class="label col-2 p-0 fw-bold">
                            <span>@_commonLocalizer["Bill to Address"]</span>
                        </div>
                        <div class="value col-10">
                            <span data-field="billingAddress"></span>
                        </div>
                    </div>
                    <div class="row flex-nowrap mb-2 bg-info-hover">
                        <div class="label col-2 p-0 fw-bold">
                            <span>@_commonLocalizer["Bill To Country"]</span>
                        </div>
                        <div class="value col-10">
                            <span data-field="billToCountry"></span>
                        </div>
                    </div>
                    <div class="row flex-nowrap mb-2 bg-info-hover">
                        <div class="label col-2 p-0 fw-bold">
                            <span>@_commonLocalizer["Buyer"]</span>
                        </div>
                        <div class="value col-10 text-break">
                            <span id="sales-order-form-buyer"></span>
                        </div>
                    </div>
                    <div class="row flex-nowrap mb-2 bg-info-hover">
                        <div class="label col-2 p-0 fw-bold">
                            <span>@_commonLocalizer["Salesperson"]</span>
                        </div>
                        <div class="value col-10">
                            <span data-field="salesman"></span>
                        </div>
                    </div>
                    <div class="row flex-nowrap mb-2 bg-info-hover">
                        <div class="label col-2 p-0 fw-bold">
                            <span>@_commonLocalizer["Additional Salesperson"]</span>
                        </div>
                        <div class="value col-10">
                            <span data-field="Salesman2"></span>
                        </div>
                    </div>
                    <div class="row flex-nowrap mb-2 bg-info-hover">
                        <div class="label col-2 p-0 fw-bold">
                            <span>@_commonLocalizer["Division Sales"]</span>
                        </div>
                        <div class="value col-10">
                            <span data-field="divisionName"></span>
                        </div>
                    </div>
                    <div class="row flex-nowrap mb-2 bg-info-hover">
                        <div class="label col-2 p-0 fw-bold">
                            <span>@_commonLocalizer["Division Header"]</span>
                        </div>
                        <div class="value col-10">
                            <span data-field="divisionHeaderName"></span>
                        </div>
                    </div>
                    <div class="row flex-nowrap mb-2 bg-info-hover">
                        <div class="label col-2 p-0 fw-bold">
                            <span>@_commonLocalizer["Currency"]</span>
                        </div>
                        <div class="value col-10">
                            <span data-field="currency"></span>
                        </div>
                    </div>
                    <div class="row flex-nowrap mb-2 bg-info-hover">
                        <div class="label col-2 p-0 fw-bold">
                            <span>@_commonLocalizer["Date Ordered"]</span>
                        </div>
                        <div class="value col-10">
                            <span data-field="dateOrderedDesc"></span>
                        </div>
                    </div>
                    <div class="row flex-nowrap mb-2 bg-info-hover">
                        <div class="label col-2 p-0 fw-bold">
                            <span>@_commonLocalizer["Checked By"]</span>
                        </div>
                        <div class="value col-10">
                            <span data-field="authoriser"></span>
                        </div>
                    </div>
                    <div class="row flex-nowrap mb-2 bg-info-hover">
                        <div class="label col-2 p-0 fw-bold">
                            <span>@_commonLocalizer["Date Checked"]</span>
                        </div>
                        <div class="value col-10">
                            <span data-field="dateAuthorisedDesc"></span>
                        </div>
                    </div>
                    <div class="row flex-nowrap mb-2 bg-info-hover">
                        <div class="label col-2 p-0 fw-bold">
                            <span>@_commonLocalizer["Customer PO"]</span>
                        </div>
                        <div class="value col-10">
                            <span data-field="customerPO"></span>
                        </div>
                    </div>
                    <div class="row flex-nowrap mb-2 bg-info-hover">
                        <div class="label col-2 p-0 fw-bold">
                            <span>@_commonLocalizer["Paid?"]</span>
                        </div>
                        <div class="value col-10 d-flex gap-2">
                            <input data-field="isPaid" class="form-control form-check-input check-sm mt-0 p-0 pe-none"
                                tabindex="-1" type="checkbox">
                            <span id="isPaidByCreditCard" class="d-none">@_commonLocalizer["Paid By Credit Card"]</span>
                        </div>
                    </div>
                    <div class="row flex-nowrap mb-2 bg-info-hover">
                        <div class="label col-2 p-0 fw-bold">
                            <span>@_commonLocalizer["Support Team Member to update"]</span>
                        </div>
                        <div class="value col-10">
                            <span data-field="supportTeamMemberName"></span>
                        </div>
                    </div>
                </div>
                <div class="col-6">
                    <div class="row flex-nowrap mb-2 bg-info-hover">
                        <div class="label col-2 p-0 fw-bold">
                            <span>@_commonLocalizer["Tax"]</span>
                        </div>
                        <div class="value col-10">
                            <span data-field="taxName"></span>
                        </div>
                    </div>
                    <div class="row flex-nowrap mb-2 bg-info-hover">
                        <div class="label col-2 p-0 fw-bold">
                            <span>@_commonLocalizer["Terms"]</span>
                        </div>
                        <div class="value col-10">
                            <span data-field="termsName"></span>
                        </div>
                    </div>
                    <div class="row flex-nowrap mb-2 bg-info-hover">
                        <div class="label col-2 p-0 fw-bold">
                            <span>@_commonLocalizer["Ship Via"]</span>
                        </div>
                        <div class="value col-10">
                            <span data-field="shipViaName"></span>
                        </div>
                    </div>
                    <div class="row flex-nowrap mb-2 bg-info-hover">
                        <div class="label col-2 p-0 fw-bold">
                            <span>@_commonLocalizer["Incoterms"]</span>
                        </div>
                        <div class="value col-10 text-break">
                            <span data-field="incotermName"></span>
                        </div>
                    </div>
                    <div class="row flex-nowrap mb-2 bg-info-hover">
                        <div class="label col-2 p-0 fw-bold">
                            <span>@_commonLocalizer["Shipping Cost"]</span>
                        </div>
                        <div class="value col-10">
                            <span data-field="shippingCost"></span>
                        </div>
                    </div>
                    <div class="row flex-nowrap mb-2 bg-info-hover">
                        <div class="label col-2 p-0 fw-bold">
                            <span>@_commonLocalizer["Freight"]</span>
                        </div>
                        <div class="value col-10">
                            <span data-field="freight"></span>
                        </div>
                    </div>
                    <div class="row flex-nowrap mb-2 bg-info-hover">
                        <div class="label col-2 p-0 fw-bold">
                            <span>@_commonLocalizer["Shipping A/C"]</span>
                        </div>
                        <div class="value col-10">
                            <span data-field="shippingAccountNo"></span>
                        </div>
                    </div>
                    <div class="row flex-nowrap mb-2 bg-info-hover">
                        <div class="label col-2 p-0 fw-bold">
                            <span>@_commonLocalizer["Ship To Address"]</span>
                        </div>
                        <div class="value col-10">
                            <span data-field="shipTo"></span>
                        </div>
                    </div>
                    <div class="row flex-nowrap mb-2 bg-info-hover">
                        <div class="label col-2 p-0 fw-bold">
                            <span>@_commonLocalizer["Notes to customer"]</span>
                        </div>
                        <div class="value col-10">
                            <span data-field="customerNotes"></span>
                        </div>
                    </div>
                    <div class="row flex-nowrap mb-2 bg-info-hover">
                        <div class="label col-2 p-0 fw-bold">
                            <span>@_commonLocalizer["Internal notes"]</span>
                        </div>
                        <div class="value col-10">
                            <span data-field="internalNotes"></span>
                        </div>
                    </div>
                    <div class="row flex-nowrap mb-2 bg-info-hover">
                        <div class="label col-2 p-0 fw-bold">
                            <span>@_commonLocalizer["Purchasing Notes"]</span>
                        </div>
                        <div class="value col-10">
                            <span style="background-color: yellow;" data-field="purchasingNotes"></span>
                        </div>
                    </div>
                    <div class="row flex-nowrap mb-2 bg-info-hover">
                        <div class="label col-2 p-0 fw-bold">
                            <span>@_commonLocalizer["Source of Supply Required"]</span>
                        </div>
                        <div class="value col-10">
                            <input data-field="aS9120" class="form-control form-check-input check-sm mt-0 p-0 pe-none"
                                tabindex="-1" type="checkbox">
                        </div>
                    </div>
                    <div class="row flex-nowrap mb-2 bg-info-hover">
                        <div class="label col-2 p-0 fw-bold">
                            <span>@_commonLocalizer["SO sent to customer"]</span>
                        </div>
                        <div class="value col-10">
                            <input data-field="sentOrder"
                                class="form-control form-check-input check-sm mt-0 p-0 pe-none" tabindex="-1"
                                type="checkbox">
                        </div>
                    </div>
                    <div class="row flex-nowrap mb-2 bg-info-hover">
                        <div class="label col-2 p-0 fw-bold">
                            <span>@_commonLocalizer["Export Control Required?"]</span>
                        </div>
                        <div class="value col-10">
                            <span data-field="oGEL_Required_Text"></span>
                        </div>
                    </div>
                    <div class="row flex-nowrap mb-2 bg-info-hover">
                        <div class="label col-2 p-0 fw-bold">
                            <span>@_commonLocalizer["AS6081 Part Included?"]</span>
                        </div>
                        <div class="value col-10">
                            <span id="AS6081_Field" data-field="aS6081_Text"></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-12 text-end">
                    <span class="updated-by fs-11 fst-italic">
                        @_commonLocalizer["Last updated"]:
                        <span> </span>
                        <span data-field="updatedByText"></span>
                    </span>
                </div>
            </div>
        </div>
    </div>
</div>
@await Html.PartialAsync("Partials/_AddTodoItem")
@await Html.PartialAsync("_EditMainInfo.cshtml")
@await Html.PartialAsync("_PayByCreditCard.cshtml")
@await Html.PartialAsync("../SOLine/_CreateIPO.cshtml", new CreateIpoAndPurchaseOrderViewModel("create-ipo-main-info-dialog", "create-ipo-main-info-form"))