﻿using GlobalTrader2.Core.Domain.Entities;
using GlobalTrader2.Core.Interfaces;
using GlobalTrader2.Dto.Base;
using GlobalTrader2.Dto.Company;
using GlobalTrader2.Dto.Currency;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GlobalTrader2.Orders.UserCases.Orders.BOM.HUBRFQSourcingResults.Query
{
    public class GetCompanyPurchasingInfoHandler : IRequestHandler<GetCompanyPurchasingInfoQuery, BaseResponse<CompanyPurchasingInfoDto>>
    {
        private readonly IBaseRepository<CompanyPurchasingInfoReadModel> _baseRepository;
        private readonly IBaseRepository<Currency> _currencyRepository;
        private readonly IMapper _mapper;

        public GetCompanyPurchasingInfoHandler(IBaseRepository<CompanyPurchasingInfoReadModel> baseRepository, IBaseRepository<Currency> currencyRepository, IMapper mapper)
        {
            _baseRepository = baseRepository;
            _currencyRepository = currencyRepository;
            _mapper = mapper;
        }

        public async Task<BaseResponse<CompanyPurchasingInfoDto>> Handle(GetCompanyPurchasingInfoQuery request, CancellationToken cancellationToken)
        {
            var response = new BaseResponse<CompanyPurchasingInfoDto>();
            var procedureName = StoredProcedures.Company_PurchasingInfo;
            var queryStr = $"{procedureName} @CompanyId";
            SqlParameter[] param =
            [
                new SqlParameter("@CompanyId", SqlDbType.Int ) { Value = request.CompanyId},
            ];

            var company = await _baseRepository.SqlQueryRawAsync(queryStr, param);

            var companyInfo = company.FirstOrDefault();

            if(companyInfo is not null)
            {
                response.Success = true;
                response.Data = new CompanyPurchasingInfoDto
                {
                    CompanyId = companyInfo.CompanyId,
                    Currency = companyInfo.POCurrencyCode,
                    GlobalCurrencyNo = companyInfo.GlobalCurrencyNo,
                    SupplierWarranty = companyInfo.SupplierWarranty,
                    UPLiftPrice = companyInfo.UPLiftPrice,
                    ESTShippingCost = companyInfo.ESTShippingCost,
                    NonPreferredCompany = companyInfo.NonPreferredCompany
                };
                if (companyInfo.GlobalCurrencyNo.HasValue)
                {
                    var currencyList = await _currencyRepository.ListAsync(filter: s => !s.Inactive && s.Buy && s.ClientNo == request.ClientId && s.GlobalCurrencyNo == companyInfo.GlobalCurrencyNo,
                        orderBy: s => s.OrderBy(c => c.CurrencyDescription).ThenBy(c => c.CurrencyCode));
                    if (currencyList is not null)
                    {
                        var currencyResponse = _mapper.Map<IEnumerable<BuyCurrencyDropdownDto>>(currencyList);
                        response.Data.Data = currencyResponse;

                        return response;
                    }
                }
            }

            return response;
        }
    }
} 
