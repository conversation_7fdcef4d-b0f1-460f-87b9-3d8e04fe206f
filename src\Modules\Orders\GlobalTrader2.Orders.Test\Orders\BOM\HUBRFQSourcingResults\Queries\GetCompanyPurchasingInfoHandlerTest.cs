using AutoFixture;
using AutoMapper;
using GlobalTrader2.Core;
using GlobalTrader2.Core.Domain.Entities;
using GlobalTrader2.Core.StoreName;
using GlobalTrader2.Dto.Company;
using GlobalTrader2.Dto.Currency;
using GlobalTrader2.Orders.UserCases.Orders.BOM.HUBRFQSourcingResults.Query;
using Microsoft.Data.SqlClient;
using Moq;
using System.Data;

namespace GlobalTrader2.Orders.UserCases.Test.Orders.BOM.HUBRFQSourcingResults.Queries
{
    public class GetCompanyPurchasingInfoHandlerTest
    {
        private readonly GetCompanyPurchasingInfoHandler _handler;
        private readonly Mock<IBaseRepository<CompanyPurchasingInfoReadModel>> _baseRepository;
        private readonly Mock<IBaseRepository<Currency>> _currencyRepository;
        private readonly Mock<IMapper> _mapper;
        private readonly Fixture _fixture;

        public GetCompanyPurchasingInfoHandlerTest()
        {
            _baseRepository = new Mock<IBaseRepository<CompanyPurchasingInfoReadModel>>();
            _currencyRepository = new Mock<IBaseRepository<Currency>>();
            _mapper = new Mock<IMapper>();
            _fixture = new Fixture();
            _handler = new GetCompanyPurchasingInfoHandler(_baseRepository.Object, _currencyRepository.Object, _mapper.Object);
        }

        [Fact]
        public async Task Handle_WithValidCompanyInfo_ReturnsSuccessResponse()
        {
            // Arrange
            var query = new GetCompanyPurchasingInfoQuery { CompanyId = 1, ClientId = 100 };
            var mockCompanyInfo = new CompanyPurchasingInfoReadModel
            {
                CompanyId = 1,
                POCurrencyCode = "USD",
                GlobalCurrencyNo = 1,
                SupplierWarranty = 12,
                UPLiftPrice = 15.5,
                ESTShippingCost = 25.0,
                NonPreferredCompany = false
            };
            var mockCurrencies = new List<Currency>
            {
                new Currency { CurrencyId = 1, CurrencyCode = "USD", CurrencyDescription = "US Dollar", GlobalCurrencyNo = 1, Buy = true, Inactive = false, ClientNo = 100 },
                new Currency { CurrencyId = 2, CurrencyCode = "EUR", CurrencyDescription = "Euro", GlobalCurrencyNo = 1, Buy = true, Inactive = false, ClientNo = 100 }
            };
            var mockCurrencyDtos = new List<BuyCurrencyDropdownDto>
            {
                new BuyCurrencyDropdownDto { CurrencyId = 1, CurrencyCode = "USD", CurrencyDescription = "US Dollar", CurrencyValue = "1.0" },
                new BuyCurrencyDropdownDto { CurrencyId = 2, CurrencyCode = "EUR", CurrencyDescription = "Euro", CurrencyValue = "0.85" }
            };

            _baseRepository.Setup(x => x.SqlQueryRawAsync(It.IsAny<string>(), It.IsAny<SqlParameter[]>()))
                          .ReturnsAsync(new List<CompanyPurchasingInfoReadModel> { mockCompanyInfo });
            _currencyRepository.Setup(x => x.ListAsync(It.IsAny<Expression<Func<Currency, bool>>>(), It.IsAny<Func<IQueryable<Currency>, IOrderedQueryable<Currency>>>()))
                              .ReturnsAsync(mockCurrencies);
            _mapper.Setup(x => x.Map<IEnumerable<BuyCurrencyDropdownDto>>(It.IsAny<IEnumerable<Currency>>()))
                   .Returns(mockCurrencyDtos);

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            Assert.True(result.Success);
            Assert.NotNull(result.Data);
            Assert.Equal(1, result.Data.CompanyId);
            Assert.Equal("USD", result.Data.Currency);
            Assert.Equal(1, result.Data.GlobalCurrencyNo);
            Assert.Equal(12, result.Data.SupplierWarranty);
            Assert.Equal(15.5, result.Data.UPLiftPrice);
            Assert.Equal(25.0, result.Data.ESTShippingCost);
            Assert.False(result.Data.NonPreferredCompany);
            Assert.NotNull(result.Data.Data);
            Assert.Equal(2, result.Data.Data.Count());
        }

        [Fact]
        public async Task Handle_WithNullCompanyInfo_ReturnsEmptyResponse()
        {
            // Arrange
            var query = new GetCompanyPurchasingInfoQuery { CompanyId = 999, ClientId = 100 };

            _baseRepository.Setup(x => x.SqlQueryRawAsync(It.IsAny<string>(), It.IsAny<SqlParameter[]>()))
                          .ReturnsAsync(new List<CompanyPurchasingInfoReadModel>());

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            Assert.False(result.Success);
            Assert.Null(result.Data);
        }
    }
}
