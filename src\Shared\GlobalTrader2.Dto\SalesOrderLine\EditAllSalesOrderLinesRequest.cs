using System.Text.Json.Serialization;
using GlobalTrader2.Dto.Converters.DateTime;

namespace GlobalTrader2.Dto.SalesOrderLine
{
    public class EditAllSalesOrderLinesRequest
    {
        public int SalesOrderId { get; set; }
        [JsonConverter(typeof(ShortDateWithCultureFormatterConverter))]
        public DateTime DatePromised { get; set; }
        public int PromiseReasonNo { get; set; }
        public bool IsReasonChanged { get; set; }

    }
}
