using GlobalTrader2.Core.Interfaces;
using GlobalTrader2.Orders.UserCases.Orders.SOLine.Commands.InsertSalesOrderLine;
using Microsoft.Data.SqlClient;
using System.Data;

namespace GlobalTrader2.Orders.Test.Orders.SOLine.Commands
{
    public class InsertSalesOrderLineHandlerTest
    {
        private readonly Mock<IBaseRepository<AffectedRows>> _repositoryMock;
        private readonly InsertSalesOrderLineHandler _handler;

        public InsertSalesOrderLineHandlerTest()
        {
            _repositoryMock = new Mock<IBaseRepository<AffectedRows>>();
            _handler = new InsertSalesOrderLineHandler(_repositoryMock.Object);
        }

        [Fact]
        public async Task InsertSalesOrderLineHandler_WhenValidRequest_InsertsDataAndReturnsNewId()
        {
            // Arrange
            var command = new InsertSalesOrderLineCommand
            {
                SalesOrderNo = 12345,
                Part = "TestPart123",
                ManufacturerNo = 101,
                DateCode = "2024",
                PackageNo = 201,
                Quantity = 100,
                Price = 50.00,
                DatePromised = new DateTime(2024, 6, 15),
                RequiredDate = new DateTime(2024, 6, 10),
                Instructions = "Test instructions",
                ProductNo = 301,
                Taxable = "Y",
                CustomerPart = "CUST123",
                Posted = false,
                ShipASAP = true,
                ServiceNo = 401,
                StockNo = 501,
                ROHS = 1,
                Notes = "Test notes",
                UpdatedBy = 456,
                QuoteLineNo = null, // No quote line to avoid triggering quote update
                ProductSource = 2,
                SourcingResultNo = 601,
                IsCreateSOClone = false,
                PODeliveryDate = new DateTime(2024, 6, 20),
                PrintHazardous = true,
                MSLLevel = "3",
                ContractNo = "CONTRACT123",
                DocId = 701,
                DocType = "P",
                EIRequired = 1,
                EvidenceNotes = "Evidence notes",
                TestingType = 2,
                ECCNCode = "5A002",
                ECCNNo = 801,
                AS6081 = true,
                ServiceCostRef = 25.50
            };

            var expectedSalesOrderLineId = 9999;

            // Setup repository mock to capture the output parameter and simulate success
            _repositoryMock
                .Setup(r => r.ExecuteSqlRawAsync(
                    It.IsAny<string>(),
                    It.IsAny<object[]>()))
                .Callback<string, object[]>((sql, parameters) =>
                {
                    // Find the output parameter and set its value
                    var sqlParameters = parameters.Cast<SqlParameter>();
                    var outputParam = sqlParameters.FirstOrDefault(p => p.Direction == ParameterDirection.Output);
                    if (outputParam != null)
                    {
                        outputParam.Value = expectedSalesOrderLineId;
                    }
                })
                .ReturnsAsync(1);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.True(result.Success);
            Assert.Equal(expectedSalesOrderLineId, result.Data);

            // Verify repository was called with correct stored procedure
            _repositoryMock.Verify(
                r => r.ExecuteSqlRawAsync(
                    It.Is<string>(sql => sql.Contains("usp_insert_SalesOrderLine")),
                    It.Is<object[]>(parameters => 
                        parameters.Length == 38 && // Expected number of parameters including output
                        parameters.Cast<SqlParameter>().Any(p => p.ParameterName == "@SalesOrderNo" && (int)p.Value == command.SalesOrderNo) &&
                        parameters.Cast<SqlParameter>().Any(p => p.ParameterName == "@Part" && (string)p.Value == command.Part) &&
                        parameters.Cast<SqlParameter>().Any(p => p.ParameterName == "@Quantity" && (int)p.Value == command.Quantity) &&
                        parameters.Cast<SqlParameter>().Any(p => p.Direction == ParameterDirection.Output && p.ParameterName == "@SalesOrderLineId"))),
                Times.Once);
        }

        [Fact]
        public async Task InsertSalesOrderLineHandler_WhenQuoteLineProvidedWithUpdatedBy_UpdatesQuoteStatus()
        {
            // Arrange
            var command = new InsertSalesOrderLineCommand
            {
                SalesOrderNo = 12345,
                Part = "TestPart123",
                Quantity = 100,
                Price = 50.00,
                DatePromised = new DateTime(2024, 6, 15),
                RequiredDate = new DateTime(2024, 6, 10),
                QuoteLineNo = 777, // Quote line provided
                UpdatedBy = 456,   // UpdatedBy provided
                Posted = false,
                ShipASAP = false,
                IsCreateSOClone = false,
                PrintHazardous = false,
                EIRequired = 0,
                AS6081 = false
            };

            var expectedSalesOrderLineId = 9999;

            // Setup repository mock for insert operation
            _repositoryMock
                .Setup(r => r.ExecuteSqlRawAsync(
                    It.Is<string>(sql => sql.Contains("usp_insert_SalesOrderLine")),
                    It.IsAny<object[]>()))
                .Callback<string, object[]>((sql, parameters) =>
                {
                    var sqlParameters = parameters.Cast<SqlParameter>();
                    var outputParam = sqlParameters.FirstOrDefault(p => p.Direction == ParameterDirection.Output);
                    if (outputParam != null)
                    {
                        outputParam.Value = expectedSalesOrderLineId;
                    }
                })
                .ReturnsAsync(1);

            // Setup repository mock for quote update operation
            _repositoryMock
                .Setup(r => r.ExecuteSqlRawAsync(
                    It.Is<string>(sql => sql.Contains("usp_update_QuoteLine_Close")),
                    It.IsAny<object[]>()))
                .ReturnsAsync(1);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.True(result.Success);
            Assert.Equal(expectedSalesOrderLineId, result.Data);

            // Verify insert operation was called
            _repositoryMock.Verify(
                r => r.ExecuteSqlRawAsync(
                    It.Is<string>(sql => sql.Contains("usp_insert_SalesOrderLine")),
                    It.IsAny<object[]>()),
                Times.Once);

            // Verify quote update operation was called
            _repositoryMock.Verify(
                r => r.ExecuteSqlRawAsync(
                    It.Is<string>(sql => sql.Contains("usp_update_QuoteLine_Close")),
                    It.Is<object[]>(parameters =>
                        parameters.Cast<SqlParameter>().Any(p => p.ParameterName == "@QuoteLineId" && (int)p.Value == command.QuoteLineNo) &&
                        parameters.Cast<SqlParameter>().Any(p => p.ParameterName == "@ReasonNo" && (int)p.Value == 1) &&
                        parameters.Cast<SqlParameter>().Any(p => p.ParameterName == "@UpdatedBy" && (int)p.Value == command.UpdatedBy) &&
                        parameters.Cast<SqlParameter>().Any(p => p.ParameterName == "@Reasons" && (string)p.Value == string.Empty))),
                Times.Once);
        }

        [Fact]
        public async Task InsertSalesOrderLineHandler_WhenQuoteLineProvidedButNoUpdatedBy_DoesNotUpdateQuoteStatus()
        {
            // Arrange
            var command = new InsertSalesOrderLineCommand
            {
                SalesOrderNo = 12345,
                Part = "TestPart123",
                Quantity = 100,
                Price = 50.00,
                DatePromised = new DateTime(2024, 6, 15),
                RequiredDate = new DateTime(2024, 6, 10),
                QuoteLineNo = 777, // Quote line provided
                UpdatedBy = null,  // No UpdatedBy
                Posted = false,
                ShipASAP = false,
                IsCreateSOClone = false,
                PrintHazardous = false,
                EIRequired = 0,
                AS6081 = false
            };

            var expectedSalesOrderLineId = 9999;

            _repositoryMock
                .Setup(r => r.ExecuteSqlRawAsync(
                    It.Is<string>(sql => sql.Contains("usp_insert_SalesOrderLine")),
                    It.IsAny<object[]>()))
                .Callback<string, object[]>((sql, parameters) =>
                {
                    var sqlParameters = parameters.Cast<SqlParameter>();
                    var outputParam = sqlParameters.FirstOrDefault(p => p.Direction == ParameterDirection.Output);
                    if (outputParam != null)
                    {
                        outputParam.Value = expectedSalesOrderLineId;
                    }
                })
                .ReturnsAsync(1);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.True(result.Success);
            Assert.Equal(expectedSalesOrderLineId, result.Data);

            // Verify insert operation was called
            _repositoryMock.Verify(
                r => r.ExecuteSqlRawAsync(
                    It.Is<string>(sql => sql.Contains("usp_insert_SalesOrderLine")),
                    It.IsAny<object[]>()),
                Times.Once);

            // Verify quote update operation was NOT called
            _repositoryMock.Verify(
                r => r.ExecuteSqlRawAsync(
                    It.Is<string>(sql => sql.Contains("usp_update_QuoteLine_Close")),
                    It.IsAny<object[]>()),
                Times.Never);
        }

        [Fact]
        public async Task InsertSalesOrderLineHandler_WhenNoQuoteLineProvided_DoesNotUpdateQuoteStatus()
        {
            // Arrange
            var command = new InsertSalesOrderLineCommand
            {
                SalesOrderNo = 12345,
                Part = "TestPart123",
                Quantity = 100,
                Price = 50.00,
                DatePromised = new DateTime(2024, 6, 15),
                RequiredDate = new DateTime(2024, 6, 10),
                QuoteLineNo = 0,  // No quote line
                UpdatedBy = 456,
                Posted = false,
                ShipASAP = false,
                IsCreateSOClone = false,
                PrintHazardous = false,
                EIRequired = 0,
                AS6081 = false
            };

            var expectedSalesOrderLineId = 9999;

            _repositoryMock
                .Setup(r => r.ExecuteSqlRawAsync(
                    It.Is<string>(sql => sql.Contains("usp_insert_SalesOrderLine")),
                    It.IsAny<SqlParameter[]>()))
                .Callback<string, object[]>((sql, parameters) =>
                {
                    var sqlParameters = parameters.Cast<SqlParameter>();
                    var outputParam = sqlParameters.FirstOrDefault(p => p.Direction == ParameterDirection.Output);
                    if (outputParam != null)
                    {
                        outputParam.Value = expectedSalesOrderLineId;
                    }
                })
                .ReturnsAsync(1);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.True(result.Success);
            Assert.Equal(expectedSalesOrderLineId, result.Data);

            // Verify quote update operation was NOT called
            _repositoryMock.Verify(
                r => r.ExecuteSqlRawAsync(
                    It.Is<string>(sql => sql.Contains("usp_update_QuoteLine_Close")),
                    It.IsAny<object[]>()),
                Times.Never);
        }

        [Fact]
        public async Task InsertSalesOrderLineHandler_WhenRepositoryThrowsException_ThrowsException()
        {
            // Arrange
            var command = new InsertSalesOrderLineCommand
            {
                SalesOrderNo = 12345,
                Quantity = 100,
                Price = 50.00,
                DatePromised = new DateTime(2024, 6, 15),
                RequiredDate = new DateTime(2024, 6, 10),
                Posted = false,
                ShipASAP = false,
                IsCreateSOClone = false,
                PrintHazardous = false,
                EIRequired = 0,
                AS6081 = false
            };

            _repositoryMock
                .Setup(r => r.ExecuteSqlRawAsync(
                    It.IsAny<string>(),
                    It.IsAny<object[]>()))
                .ThrowsAsync(new Exception("Database error"));

            // Act & Assert
            await Assert.ThrowsAsync<Exception>(() => _handler.Handle(command, CancellationToken.None));

            // Verify quote update was not attempted due to exception
            _repositoryMock.Verify(
                r => r.ExecuteSqlRawAsync(
                    It.Is<string>(sql => sql.Contains("usp_update_QuoteLine_Close")),
                    It.IsAny<object[]>()),
                Times.Never);
        }

        [Fact]
        public async Task InsertSalesOrderLineHandler_WhenOutputParameterIsNull_ThrowsArgumentNullException()
        {
            // Arrange
            var command = new InsertSalesOrderLineCommand
            {
                SalesOrderNo = 12345,
                Quantity = 100,
                Price = 50.00,
                DatePromised = new DateTime(2024, 6, 15),
                RequiredDate = new DateTime(2024, 6, 10),
                Posted = false,
                ShipASAP = false,
                IsCreateSOClone = false,
                PrintHazardous = false,
                EIRequired = 0,
                AS6081 = false
            };

            // Setup repository to return null for output parameter (simulating database issue)
            _repositoryMock
                .Setup(r => r.ExecuteSqlRawAsync(
                    It.IsAny<string>(),
                    It.IsAny<object[]>()))
                .Callback<string, object[]>((sql, parameters) =>
                {
                    // Don't set the output parameter value (leave it null)
                })
                .ReturnsAsync(1);

            // Act & Assert
            await Assert.ThrowsAsync<ArgumentNullException>(() => _handler.Handle(command, CancellationToken.None));
        }
    }
}