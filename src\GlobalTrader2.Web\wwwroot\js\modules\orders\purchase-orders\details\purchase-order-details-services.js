﻿import { PurchaseOrderDetailsApiUrl } from '../../../../config/api-endpoint-config.js?v=#{BuildVersion}#'

export class PurchaseOrderDetailsService {
    static #baseUrl = PurchaseOrderDetailsApiUrl;

    static async getPurchaseOrderMainInfoAsync(purchaseOrderId) {
        return await await GlobalTrader.ApiClient.getAsync(`${this.#baseUrl}/${purchaseOrderId}/main-info`);
    }

    static async getEprListAsync(purchaseOrderId, orderBy = null) {
        let url = this.#baseUrl + "/" + purchaseOrderId + "/epr/list";

        if (orderBy !== null) {
            url += "?orderBy=" + orderBy;
        }

        return await GlobalTrader.ApiClient.getAsync(url);
    }
}
