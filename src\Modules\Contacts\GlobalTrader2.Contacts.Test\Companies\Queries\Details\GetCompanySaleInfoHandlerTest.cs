﻿using AutoMapper;
using GlobalTrader2.Core.Domain.Entities;
using GlobalTrader2.Core;
using Moq;
using GlobalTrader2.Contacts.UseCases.Companies.Queries.Details.GetCompanySaleInfo;
using GlobalTrader2.Contacts.UseCases.Commons.Mappings;
using MediatR;

namespace GlobalTrader2.Contacts.UseCases.Test.Companies.Queries.Details
{
    public class GetCompanySaleInfoHandlerTest
    {
        private readonly Mock<IBaseRepository<SalesInfo>> _repository;
        private readonly Mock<IBaseRepository<ThisYearSalesOrderValueReadModel>> _thisYearSaleOrderValueRepository;
        private readonly Mock<IBaseRepository<LastYearSalesOrderValueReadModel>> _lastYearSaleOrderValueRepository;
        private readonly Mock<IBaseRepository<InvoiceNotExportedReadModel>> _invoiceNotExportedRepository;
        private readonly Mock<IBaseRepository<SalesOrderForCompanyReadModel>> _salesOrderOpenRepository;
        private readonly Mock<IBaseRepository<SalesOrderSummaryValuesReadModel>> _salesOrderValueRepository;
        private readonly Mock<IBaseRepository<ConvertedValueBetweenTwoCurrenciesReadModel>> _convertRepository;
        private readonly IMapper _mapper;
        private readonly GetCompanySaleInfoHandler _handler;
        private readonly Mock<IMediator> _mediator;

        public GetCompanySaleInfoHandlerTest()
        {
            _repository = new Mock<IBaseRepository<SalesInfo>>();
            _mediator = new Mock<IMediator>();
            _thisYearSaleOrderValueRepository = new Mock<IBaseRepository<ThisYearSalesOrderValueReadModel>>();
            _lastYearSaleOrderValueRepository = new Mock<IBaseRepository<LastYearSalesOrderValueReadModel>>();
            _invoiceNotExportedRepository = new Mock<IBaseRepository<InvoiceNotExportedReadModel>>();
            _salesOrderOpenRepository = new Mock<IBaseRepository<SalesOrderForCompanyReadModel>>();
            _salesOrderValueRepository = new Mock<IBaseRepository<SalesOrderSummaryValuesReadModel>>();
            _convertRepository = new Mock<IBaseRepository<ConvertedValueBetweenTwoCurrenciesReadModel>>();
            var mappingConfig = new MapperConfiguration((config) => {
                config.AddProfile(new SalesOrderMapper());
            });
            _mapper = mappingConfig.CreateMapper();

            _handler = new GetCompanySaleInfoHandler(
                _repository.Object,
                _thisYearSaleOrderValueRepository.Object,
                _lastYearSaleOrderValueRepository.Object,
                _invoiceNotExportedRepository.Object,
                _salesOrderOpenRepository.Object,
                _salesOrderValueRepository.Object,
                _convertRepository.Object,
                _mapper,
                _mediator.Object);
        }

        [Fact]
        public async Task GetSalesInfo_WithInvalidCompanyId_ShouldThowException() 
        {
            // Arrange
            var request = new GetCompanySaleInfoQuery()
            {
                ClientCurrencyCode = "",
                CompanyId = 1,
                CultureInfo = null,
                ClientCurrencyID = 1
            };

            _repository.Setup(r => r.SqlQueryRawAsync(It.IsAny<string>(), It.IsAny<object[]>()))
                .ReturnsAsync(new List<SalesInfo>());

            // Act + Assert
            await Assert.ThrowsAsync<ArgumentNullException>(() => _handler.Handle(request, CancellationToken.None));
        }

        [Fact]
        public async Task GetSalesInfo_WithValidCompanyId_ShouldReturnSuccess() {
            // Arrange
            var request = new GetCompanySaleInfoQuery()
            {
                ClientCurrencyCode = "",
                CompanyId = 1,
                CultureInfo = null,
                ClientCurrencyID = 1
            };

            var saleInfos = new List<SalesInfo>() {
                new SalesInfo (){ CompanyId = 1, ExchangeRate = 1}
            };
            
            var thisYearValue = new List<ThisYearSalesOrderValueReadModel>() {
                new ThisYearSalesOrderValueReadModel (){ SalesOrderValueYTDInBase = 1, SalesOrderValueYTD = 1, SOCurrencyCode = ""}
            };
            
            var lastYearValue = new List<LastYearSalesOrderValueReadModel>() {
                new LastYearSalesOrderValueReadModel (){ SalesOrderValueLastYearInBase = 1, SalesOrderValueLastYear = 1, SOCurrencyCode = ""}
            };

            var invoiceNotExported = new List<InvoiceNotExportedReadModel>() { 
                new InvoiceNotExportedReadModel(){ TotalNotExportedValue = 1 }
            };
            
            var saleOrders = new List<SalesOrderForCompanyReadModel>() {
                new SalesOrderForCompanyReadModel (){ SalesOrderId = 1}
            };
            
            var saleOrderSumaries = new List<SalesOrderSummaryValuesReadModel>() {
                new SalesOrderSummaryValuesReadModel (){ SalesOrderId = 1, TotalValue = 1,CurrencyNo = 1, DateOrdered = DateTime.Now, Freight = 1, LineSubTotal = 1, TotalTax = 1}
            };

            var convertedValue = new List<ConvertedValueBetweenTwoCurrenciesReadModel>() {
                new ConvertedValueBetweenTwoCurrenciesReadModel (){ ConvertedValue = 1}
            };

            _repository.Setup(r => r.SqlQueryRawAsync(It.IsAny<string>(), It.IsAny<object[]>()))
                .ReturnsAsync(saleInfos);
            _thisYearSaleOrderValueRepository.Setup(r => r.SqlQueryRawAsync(It.IsAny<string>(), It.IsAny<object[]>()))
                .ReturnsAsync(thisYearValue);
            _lastYearSaleOrderValueRepository.Setup(r => r.SqlQueryRawAsync(It.IsAny<string>(), It.IsAny<object[]>()))
                .ReturnsAsync(lastYearValue);
            _invoiceNotExportedRepository.Setup(r => r.SqlQueryRawAsync(It.IsAny<string>(), It.IsAny<object[]>()))
                .ReturnsAsync(invoiceNotExported);
            _salesOrderOpenRepository.Setup(r => r.SqlQueryRawAsync(It.IsAny<string>(), It.IsAny<object[]>()))
                .ReturnsAsync(saleOrders);
            _salesOrderValueRepository.Setup(r => r.SqlQueryRawAsync(It.IsAny<string>(), It.IsAny<object[]>()))
                .ReturnsAsync(saleOrderSumaries);
            _convertRepository.Setup(r => r.SqlQueryRawAsync(It.IsAny<string>(), It.IsAny<object[]>()))
                .ReturnsAsync(convertedValue);

            // Act 
            var response = await _handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.True(response.Success);
            Assert.NotNull(response.Data);
            Assert.True(response.Data.Balance == "0.00" 
                && response.Data.Days1 == "0.00"
                && response.Data.Days30 == "0.00"
                && response.Data.Days60 == "0.00"
                && response.Data.Days90 == "0.00"
                && response.Data.Days120 == "0.00"
                && response.Data.CurrentMonth == "0.00"
            );
        }
    }
}
