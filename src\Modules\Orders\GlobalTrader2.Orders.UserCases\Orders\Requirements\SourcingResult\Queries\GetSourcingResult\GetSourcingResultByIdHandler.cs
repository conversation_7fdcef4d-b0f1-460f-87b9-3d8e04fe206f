﻿using GlobalTrader2.Core.Helpers;
using GlobalTrader2.Dto.CustomerRequirement;

namespace GlobalTrader2.Orders.UserCases.Orders.Requirements.SourcingResult.Queries.GetSourcingResult;

public class GetSourcingResultByIdHandler : IRequestHandler<GetSourcingResultByIdQuery, BaseResponse<SourcingResultDetailsDto>>
{
    private readonly IBaseRepository<SourcingResultDetails> _repository;

    public GetSourcingResultByIdHandler(IBaseRepository<SourcingResultDetails> repository)
    {
        _repository = repository;
    }

    public async Task<BaseResponse<SourcingResultDetailsDto>> Handle(GetSourcingResultByIdQuery request, CancellationToken cancellationToken)
    {
        var response = new BaseResponse<SourcingResultDetailsDto>();

        var result = await _repository.SqlQueryRawAsync(
            $"{StoredProcedures.Select_SourcingResult} @SourcingResultId",
            [
                new SqlParameter("@SourcingResultId", request.SourcingResultId)
            ]);

        if (result != null && result.Any())
        {
            var sourcingResult = result[0];
            
            response.Success = true;
            response.Data = new SourcingResultDetailsDto
            {
                SourcingResultId = sourcingResult.SourcingResultId,
                CurrencyNo = sourcingResult.CurrencyNo,
                DateCode = sourcingResult.DateCode,
                FullPart = sourcingResult.FullPart,
                ManufacturerName = sourcingResult.ManufacturerName,
                ManufacturerNo = sourcingResult.ManufacturerNo,
                MSLLevelNo = sourcingResult.MSLLevelNo,
                MSLLevel = sourcingResult.MSLLevel,
                Notes = sourcingResult.Notes,
                OfferStatusNo = sourcingResult.OfferStatusNo,
                PackageDescription = sourcingResult.PackageDescription,
                PackageNo = sourcingResult.PackageNo,
                Part = sourcingResult.Part,
                PartWatchMatch = sourcingResult.partWatchMatch ?? false,
                Price = sourcingResult.Price ?? 0,
                ProductDescription = sourcingResult.ProductDescription,
                ProductInactive = sourcingResult.ProductInactive,
                ProductNo = sourcingResult.ProductNo,
                Quantity = sourcingResult.Quantity ?? 0,
                ROHS = sourcingResult.ROHS,
                SupplierName = sourcingResult.SupplierName,
                SupplierNo = sourcingResult.SupplierNo,
                IsIPO = sourcingResult.POHubCompanyNo.HasValue && sourcingResult.POHubCompanyNo.Value > 0,
                AS6081 = sourcingResult.AS6081,
                SourcingNotes = Functions.ReplaceLineBreaks(sourcingResult.SourcingNotes),
                PoDeliveryDate = sourcingResult.DeliveryDate,
                CustomerPart = sourcingResult.CustomerPart,
            };
        }

        return response;
    }
}
