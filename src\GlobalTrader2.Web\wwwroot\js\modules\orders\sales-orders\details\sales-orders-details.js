﻿import { SalesOrdersMainInfoManager } from './components/main-info/main-info.js?v=#{BuildVersion}#';
import { LinesManager } from './components/lines/lines.js?v=#{BuildVersion}#';
import { ExportApprovalStatusManager } from './components/export-approval-status/export-approval-status.js?v=#{BuildVersion}#';
import { AuthorisationManager } from './authorisation/authorisation.js?v=#{BuildVersion}#'
import { DocumentsManager } from '../../../../components/documents-list/document-list.js?v=#{BuildVersion}#'; 
import { EndUserUndertakingManager } from './components/end-user-undertaking/end-user-undertaking.js?v=#{BuildVersion}#';

$(async () => {
    const salesOrderDetailsManager = new SalesOrdersDetailsManager();
    salesOrderDetailsManager.initialize();
});
class SalesOrdersDetailsManager {
    constructor() {
        this.salesOrderId = getParameterByName('so');
        this.salesOrderLineId = getParameterByName('sol') ?? null;
        this.salesOrdersMainInfoManager = new SalesOrdersMainInfoManager({
            salesOrderId: this.salesOrderId,
            companyId: SODetailGeneralInfo.salesOrderCompanyId,
            globalLoginClientNo: SODetailGeneralInfo.soClientId,
            refreshCallback: (data) => {
            },

            onEditMainInfoSuccess: () => {
                this.authorisationManager.refreshSectionBox();
                this.lines.refreshSectionBox();
                this.exportApprovalStatusManager.refreshSectionBox();
                this.customerPoOnlyPDFDocumentsManager.refreshDocumentsSectionBox();
                this.PDFDocumentsManager.refreshDocumentsSectionBox();
                this.soPaymentFilesDocumentsManager.refreshDocumentsSectionBox();
                this.excelDocumentsManager.refreshDocumentsSectionBox();
                this.sorPdfDocumentsManager.refreshDocumentsSectionBox();
            }
        });
        this.authorisationManager = new AuthorisationManager({
            salesOrderId: this.salesOrderId,
            refreshCallback: () => {
                this.lines.updateAuthData(this.authorisationManager.authorisationData);
                this.lines.refreshSectionBox();
            },
            onAuthoriseSuccess: () => {
                this.sorPdfDocumentsManager.refreshDocumentsSectionBox();
            },
            onRequestApprovalSuccess: () => {
                this.sorPdfDocumentsManager.refreshDocumentsSectionBox();
            }
        });
        this.lines = new LinesManager(this.salesOrderId, this.salesOrderLineId, () => this.authorisationManager.refreshSectionBox());
        this.exportApprovalStatusManager = new ExportApprovalStatusManager(this.salesOrderId);
        this.endUserUndertakingManager = new EndUserUndertakingManager(this.salesOrderId);
        this.customerPoOnlyPDFDocumentsManager = null;
        this.PDFDocumentsManager = null;
        this.soPaymentFilesDocumentsManager = null;
        this.excelDocumentsManager = null;
        this.sorPdfDocumentsManager = null;

        this._isAllowAction = $('#isAllowAction').val() === 'True';
    }

    async initialize() {
        this.exportApprovalStatusManager.initialize();
        this.initializeCustomerPoOnlyPdfSection();
        this.initializePdfDocumentsSection();
        this.initializeSOPaymentFilesPdfSection();
        this.initializeExcelDocumentsSection();
        this.initializeSorPdfDocumentsSection();
        this.setupOnSuccessPayByCreditCard();
        if (this._isAllowAction) {
            this.endUserUndertakingManager.initialize();
        }
        await Promise.all([
            this.salesOrdersMainInfoManager.initialize(),// Await to get field AS9120,
            this.authorisationManager.initialize()
        ]);
       
        const salesOrdersMainInfoModel = { ...this.salesOrdersMainInfoManager.salesOrdersMainInfo, autoApproveSO: SODetailGeneralInfo.autoApproveSO }
        this.lines.init(salesOrdersMainInfoModel, this.authorisationManager.authorisationData);

        this._setupMainInfoEventListener();
        this._setupLineSectionEventListener();
    }

    async initializeCustomerPoOnlyPdfSection() {
        customerPoOnlyPdfSectionComponent.isReadOnly = customerPoOnlyPdfSectionComponent.isReadOnly.toLowerCase() === "true";
        this.customerPoOnlyPDFDocumentsManager = new DocumentsManager({
            documentSectionComponent: customerPoOnlyPdfSectionComponent, //retrieve from <script> .cshtml
            id: this.salesOrderId,
            uploadDialogParams: {
                dialogSelector: customerPoOnlyPdfUploadComponent.dialogId,
                formSelector: customerPoOnlyPdfUploadComponent.formId,
                allowedFileExtensions: customerPoOnlyPdfUploadComponent.allowedFileExtensions.split(", "),
            },
            removeDocumentDialogParams: {
                dialogSelector: customerPoOnlyPdfRemoveComponent.dialogId,
                sectionName: customerPoOnlyPdfRemoveComponent.sectionName,
            },
            refreshCallback: () => { this.authorisationManager.refreshSectionBox() }
        });
        await this.customerPoOnlyPDFDocumentsManager.initialize();
    }

    async initializePdfDocumentsSection() {
        pdfDocumentsSectionComponent.isReadOnly = pdfDocumentsSectionComponent.isReadOnly.toLowerCase() === "true";
        this.PDFDocumentsManager = new DocumentsManager({
            documentSectionComponent: pdfDocumentsSectionComponent, //retrieve from <script> .cshtml
            id: this.salesOrderId,
            uploadDialogParams: {
                dialogSelector: pdfDocumentsUploadComponent.dialogId,
                formSelector: pdfDocumentsUploadComponent.formId,
                allowedFileExtensions: pdfDocumentsUploadComponent.allowedFileExtensions.split(", "),
            },
            removeDocumentDialogParams: {
                dialogSelector: pdfDocumentsRemoveComponent.dialogId,
                sectionName: pdfDocumentsRemoveComponent.sectionName,
            },
           
        });
        await this.PDFDocumentsManager.initialize();
    }

    async initializeSOPaymentFilesPdfSection() {
        soPaymentFilesSectionComponent.isReadOnly = soPaymentFilesSectionComponent.isReadOnly.toLowerCase() === "true";
        this.soPaymentFilesDocumentsManager = new DocumentsManager({
            documentSectionComponent: soPaymentFilesSectionComponent, //retrieve from <script> .cshtml
            id: this.salesOrderId,
        });
        await this.soPaymentFilesDocumentsManager.initialize();
    }

    async initializeExcelDocumentsSection() {
        excelDocumentsSectionComponent.isReadOnly = excelDocumentsSectionComponent.isReadOnly.toLowerCase() === "true";
        this.excelDocumentsManager = new DocumentsManager({
            documentSectionComponent: excelDocumentsSectionComponent, //retrieve from <script> .cshtml
            id: this.salesOrderId,
            uploadDialogParams: {
                dialogSelector: excelDocumentsUploadComponent.dialogId,
                formSelector: excelDocumentsUploadComponent.formId,
                allowedFileExtensions: excelDocumentsUploadComponent.allowedFileExtensions.split(", "),
            },
            removeDocumentDialogParams: {
                dialogSelector: excelDocumentsRemoveComponent.dialogId,
                sectionName: excelDocumentsRemoveComponent.sectionName,
            },
        });
        await this.excelDocumentsManager.initialize();
    }

    async initializeSorPdfDocumentsSection() {
        sorPdfDocumentsSectionComponent.isReadOnly = sorPdfDocumentsSectionComponent.isReadOnly.toLowerCase() === "true";
        this.sorPdfDocumentsManager = new DocumentsManager({
            documentSectionComponent: sorPdfDocumentsSectionComponent, //retrieve from <script> .cshtml
            id: this.salesOrderId,
            removeDocumentDialogParams: {
                dialogSelector: sorPdfDocumentsRemoveComponent.dialogId,
                sectionName: sorPdfDocumentsRemoveComponent.sectionName,
            },
        });
        await this.sorPdfDocumentsManager.initialize();
    }

    setupOnSuccessPayByCreditCard() {
        this.salesOrdersMainInfoManager.payByCreditCardDialogManager.on("PayByCreditCardSuccess", async () => {
            this.authorisationManager.refreshSectionBox();
            this.soPaymentFilesDocumentsManager.refreshDocumentsSectionBox();
            this.lines.refreshSectionBox();
        });
    }

    _setupMainInfoEventListener() {
        this.salesOrdersMainInfoManager.on('editSuccess', () => {
            this.lines.updateSalesOrderMainInfo(this.salesOrdersMainInfoManager.salesOrdersMainInfo);
        });
    }

    _setupLineSectionEventListener() {
        this.lines.on('editLineSuccess', async () => {
            await this.authorisationManager.refreshSectionBox();
            this.lines.updateAuthData(this.authorisationManager.authorisationData);
            this.lines.refreshSectionBox();
            this.exportApprovalStatusManager.refreshSectionBox();
        });

        this.lines.on('enabledCheckBoxUpdated', async () => {
            this.salesOrdersMainInfoManager.updateCreateIpoButton(this.lines.checkBoxEnabled);
        })

        this.lines.addNewLine.$dialog.on('addNewLineSuccess', async (event, newSalesOrderLineId) => {
            await Promise.all([this.authorisationManager.refreshSectionBox(), this.authorisationManager.refreshSectionBox()]);
            
            this.lines.updateAuthData(this.authorisationManager.authorisationData);
            this.lines.updateSalesOrderMainInfo(this.salesOrdersMainInfoManager.salesOrdersMainInfo);
            this.lines.refreshSectionBox();

            this.exportApprovalStatusManager.refreshSectionBox();
            this.endUserUndertakingManager.refreshSectionBox();
        });

        //Deallocate success
        const self = this;
        $('[id="deallocate-dialog"]').each(function () {
            $(this).on('deallocateSuccess', async function () {
                self.lines._sectionBox.loading(false);
                await Promise.all([
                    self.salesOrdersMainInfoManager.refreshSectionBox(), // Await to get field AS9120,
                    self.authorisationManager.refreshSectionBox(),
                ]);
                self.lines.updateAuthData(self.authorisationManager.authorisationData);
                self.lines.refreshSectionBox();
            });
        });
    }
}
