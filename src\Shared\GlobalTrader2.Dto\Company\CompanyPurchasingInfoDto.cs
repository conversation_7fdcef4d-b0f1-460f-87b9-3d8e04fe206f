﻿using GlobalTrader2.Dto.Currency;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GlobalTrader2.Dto.Company
{
    public class CompanyPurchasingInfoDto
    {
        public int? CompanyId { get; set; }
        public int? GlobalCurrencyNo { get; set; }
        public string? Currency { get; set; }
        public int? SupplierWarranty { get; set; }
        public double? UPLiftPrice { get; set; }
        public double? ESTShippingCost { get; set; }
        public bool? NonPreferredCompany { get; set; }
        public IEnumerable<BuyCurrencyDropdownDto>? Data { get; set; }
    }
}
