using GlobalTrader2.Core.Interfaces;
using GlobalTrader2.Core.StoreName;
using GlobalTrader2.Dto.SalesOrderLine;
using GlobalTrader2.Orders.UserCases.Orders.SOLine.Queries.GetClosedSoLinesBySoId;
using GlobalTrader2.Orders.UserCases.Orders.SOLine.Queries.GetInfoForEditAllSoLines;
using Microsoft.Data.SqlClient;

namespace GlobalTrader2.Orders.UserCases.Test.Orders.SOLine.Queries;
public class GetInfoForEditAllSoLinesHandlerTests
{
    [Fact]
    public async Task Handle_Should_Return_Correct_SoForEditAllLines()
    {
        // Arrange
        var soId = 1001;
        var salesOrders = new List<SalesOrder>
        {
            new SalesOrder { SalesOrderId = soId, ClientNo = 1, AuthorisedBy = 1 }
        }.AsQueryable();

        var soLines = new List<SalesOrderLine>
        {
            new SalesOrderLine { SalesOrderNo = soId, Closed = false, DatePromised = new DateTime(2025, 8, 5) },
            new SalesOrderLine { SalesOrderNo = soId, Closed = false, DatePromised = new DateTime(2025, 8, 3) },
            new SalesOrderLine { SalesOrderNo = soId, Closed = true, DatePromised = new DateTime(2025, 7, 1) } // should be ignored
        }.AsQueryable();

        var soRepoMock = new Mock<IBaseRepository<SalesOrder>>();
        var soLineRepoMock = new Mock<IBaseRepository<SalesOrderLine>>();
        var queryableServiceMock = new Mock<IQueryableAsyncService>();

        soRepoMock.Setup(r => r.ListAsQueryable()).Returns(salesOrders);
        soLineRepoMock.Setup(r => r.ListAsQueryable()).Returns(soLines);

        var expected = new SoForEditAllLines
        {
            SalesOrderId = soId,
            ClientNo = 1,
            IsAuthorized = true,
            EarliestDatePromised = new DateTime(2025, 8, 3)
        };

        queryableServiceMock
            .Setup(q => q.FirstOrDefaultAsync(It.IsAny<IQueryable<SoForEditAllLines>>()))
            .ReturnsAsync((IQueryable<SoForEditAllLines> q) => q.FirstOrDefault());

        var handler = new GetInfoForEditAllSoLinesHandler(
            soRepoMock.Object,
            soLineRepoMock.Object,
            queryableServiceMock.Object
        );

        var query = new GetInfoForEditAllSoLinesQuery { SalesOrderId = soId };

        // Act
        var response = await handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(response.Success);
        Assert.NotNull(response.Data);
        Assert.Equal(expected.SalesOrderId, response.Data.SalesOrderId);
        Assert.Equal(expected.ClientNo, response.Data.ClientNo);
        Assert.Equal(expected.IsAuthorized, response.Data.IsAuthorized);
        Assert.Equal(expected.EarliestDatePromised, response.Data.EarliestDatePromised);
    }
}