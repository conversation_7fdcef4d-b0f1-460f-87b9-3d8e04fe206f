﻿using GlobalTrader2.Core.Enums;
using GlobalTrader2.Dto.Contact;
using GlobalTrader2.SharedUI.Authorization;
using GlobalTrader2.SharedUI.Constants;
using GlobalTrader2.SharedUI.Enums;
using GlobalTrader2.SharedUI.Models;
using GlobalTrader2.SharedUI.Services;
using GlobalTrader2.UserAccount.UseCases.RecentlyViewed.Commands.Create;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Extensions.DependencyInjection;

namespace GlobalTrader2.SharedUI.Bases
{
    [SectionAuthorize]
    public class BasePageModel : PageModel
    {
        private List<NavigationItem>? _breadCrumb;
        private List<BreadCrumbMenuModel>? _breadCrumbMenu;
        public List<NavigationItem> BreadCrumb
        {
            get
            {
                if (_breadCrumb == null)
                {
                    _breadCrumb = new List<NavigationItem>();
                }

                return _breadCrumb;
            }
            set
            {
                _breadCrumb = value;
            }
        }
        public List<BreadCrumbMenuModel> BreadCrumbMenu
        {
            get
            {
                if (_breadCrumbMenu == null)
                {
                    _breadCrumbMenu = new List<BreadCrumbMenuModel>();
                }

                return _breadCrumbMenu;
            }
            set
            {
                _breadCrumbMenu = value;
            }
        }

        public SiteSection SiteSection { get; set; }
        public SitePage PageType { get; set; }
        public bool IsDataHasOtherClient { get; set; } = false;
        protected List<string>? _lstTabs = null;
        public List<ViewLevelList>? VisibleTab = null;

        protected readonly SecurityManager? _securityManager;

        public BasePageModel(SecurityManager securityManager)
        {
            _securityManager = securityManager;
        }

        public BasePageModel()
        {
        }

        public override async Task OnPageHandlerExecutionAsync(PageHandlerExecutingContext context, PageHandlerExecutionDelegate next)
        {
            if (IsAllowAnonymous(context.HandlerInstance.GetType()))
            {
                await next.Invoke();
                return;
            }

            if (context.HttpContext.Request.Method == "GET")
            {
                var loginId = HttpContext.Session.GetInt32(SessionKey.LoginID);

                if (loginId.HasValue && _breadCrumb?.Count > 0)
                {
                    var mediator = context.HttpContext.RequestServices
                    .GetRequiredService<ISender>();

                    var lastBreadcrumb = _breadCrumb[_breadCrumb.Count - 1];
                    var command = new CreateRecentlyViewedCommand
                    {
                        LoginNo = loginId.Value,
                        PageTitle = lastBreadcrumb.PageTitle,
                        PageUrl = lastBreadcrumb.CtaUri
                    };
                    await mediator.Send(command);
                }
            }

            if (_securityManager != null)
            {
                await LoadUserPagePermissionsAsync();

                var sectionPermissionRequirements = GetPageSectionPermissions(context);
                var sectionLevelPermissionRequirement = GetPageSectionLevelPermissions(context);

                var allPermissions = sectionPermissionRequirements
                    .Concat(sectionLevelPermissionRequirement);

                if (allPermissions.Any(permission => !_securityManager.CheckSectionLevelPermission(permission)))
                {
                    context.Result = Redirect("/NotFound");
                    return;
                }
            }

            CheckPermissionBreadCrumbItem();

            await next.Invoke();
        }

        private async Task LoadUserPagePermissionsAsync()
        {
            var loginId = HttpContext.Session.GetInt32(SessionKey.LoginID);

            if (!loginId.HasValue || _securityManager == null) return;

            await _securityManager.LoadUserPagePermissions(loginId.Value, SiteSection, PageType, IsDataHasOtherClient);
        }

        public static bool IsAllowAnonymous(Type pageModelType)
        {
            if (HasAttribute(pageModelType, typeof(AllowAnonymousAttribute))) return true;

            if (HasAttribute(pageModelType, typeof(SectionAuthorizeAttribute))) return false;

            if (HasAttribute(pageModelType, typeof(SectionLevelAuthorizeAttribute))) return false;

            if (HasAttribute(pageModelType, typeof(AuthorizeAttribute))) return false;

            return true;
        }

        public static bool HasAttribute(Type pageModelType, Type attributeType)
        {
            return pageModelType.GetCustomAttributes(attributeType, true).Length > 0;
        }

        private static IEnumerable<SecurityFunction> GetSecurityFunctions<T>(PageHandlerExecutingContext context, Func<T, IEnumerable<SecurityFunction>> selector)
            where T : Attribute
        {
            if (context.HandlerInstance?.GetType().GetCustomAttributes(typeof(T), true).FirstOrDefault() is not T attribute)
                return Enumerable.Empty<SecurityFunction>();

            return selector(attribute) ?? Enumerable.Empty<SecurityFunction>();
        }

        public static IEnumerable<SecurityFunction> GetPageSectionPermissions(PageHandlerExecutingContext context)
            => GetSecurityFunctions<SectionAuthorizeAttribute>(context, attr => attr.SectionPermissions ?? Enumerable.Empty<SecurityFunction>());

        public static IEnumerable<SecurityFunction> GetPageSectionLevelPermissions(PageHandlerExecutingContext context)
            => GetSecurityFunctions<SectionLevelAuthorizeAttribute>(context, attr => attr.SectionLevelPermissions ?? Enumerable.Empty<SecurityFunction>());


        protected async Task<List<int>> GetVisibleTabSecurityList(SecurityFunction enmPageId)
        {
            var listTabs = new List<int>();
            var loginId = HttpContext.Session.GetInt32(SessionKey.LoginID);
            if (loginId.HasValue)
            {
                var result = await _securityManager!.GetTabSecurityList((int)enmPageId, loginId.Value);
                if (result != null)
                {
                    var tabMappings = new List<(bool IsActive, int TabIndex)>
                    {
                        (result.MyTab, (int)ViewLevelList.My),
                        (result.TeamTab, (int)ViewLevelList.Team),
                        (result.DivisionTab, (int)ViewLevelList.Division),
                        (result.CompanyTab, (int)ViewLevelList.Company)
                    };

                    listTabs.AddRange(tabMappings.Where(t => t.IsActive).Select(t => t.TabIndex));
                }
            }
            return listTabs;
        }

        protected async Task<List<int>> GetVisibleCompanyTabSecurityList(SecurityFunction enmPageId1, SecurityFunction enmPageId2)
        {
            var listTabs = new List<int>();
            var loginId = HttpContext.Session.GetInt32(SessionKey.LoginID);
            if (loginId.HasValue)
            {
                var result = await _securityManager!.GetCompanyTabSecurityList((int)enmPageId1, (int)enmPageId2, loginId.Value);
                if (result != null)
                {
                    var tabMappings = new List<(bool IsActive, int TabIndex)>
                    {
                        (result.MyTab, (int)ViewLevelList.My),
                        (result.TeamTab, (int)ViewLevelList.Team),
                        (result.DivisionTab, (int)ViewLevelList.Division),
                        (result.CompanyTab, (int)ViewLevelList.Company)
                    };

                    listTabs.AddRange(tabMappings.Where(t => t.IsActive).Select(t => t.TabIndex));
                }
            }
            return listTabs;
        }

        protected async Task<Dictionary<ViewLevelList, bool>> GetTabSecurityFunctionPermission(SecurityFunction enmPageId)
        {
            var tabPermission = new Dictionary<ViewLevelList, bool>();
            var loginId = HttpContext.Session.GetInt32(SessionKey.LoginID);
            if (loginId.HasValue)
            {
                var result = await _securityManager!.GetTabSecurityList((int)enmPageId, loginId.Value);
                if (result != null)
                {
                    tabPermission.Add(ViewLevelList.My, result.MyTab);
                    tabPermission.Add(ViewLevelList.Team, result.TeamTab);
                    tabPermission.Add(ViewLevelList.Division, result.DivisionTab);
                    tabPermission.Add(ViewLevelList.Company, result.CompanyTab);
                }
            }
            return tabPermission;
        }

        protected static bool CheckTabSecurityInDetailPage(TabSecurityInDetailPageRequest request)
        {
            bool IsAllow = new (int tab, bool canView)[]
            {
                ((int)ViewLevelList.Company, request.ClientID == request.LoginClientID),
                ((int)ViewLevelList.Division, request.DivisionID == request.LoginDivisionID),
                ((int)ViewLevelList.Team, request.TeamID == request.LoginTeamID),
                ((int)ViewLevelList.My, request.LoginID == request.LoginUserID)
            }
            .Where(x => request.ListTabs.Contains(x.tab))
            .Any(x => x.canView);

            return IsAllow;
        }

        protected async Task<List<int>> GetInVisibleTabSecurityList(SecurityFunction enmPageId)
        {
            var listTabs = new List<int>();
            var loginId = HttpContext.Session.GetInt32(SessionKey.LoginID);
            if (loginId.HasValue)
            {
                var result = await _securityManager!.GetTabSecurityList((int)enmPageId, loginId.Value);
                if (result != null)
                {
                    var tabMappings = new List<(bool IsActive, int TabIndex)>
                    {
                        (result.MyTab, (int)ViewLevelList.My),
                        (result.TeamTab, (int)ViewLevelList.Team),
                        (result.DivisionTab, (int)ViewLevelList.Division),
                        (result.CompanyTab, (int)ViewLevelList.Company)
                    };

                    listTabs.AddRange(tabMappings.Where(t => !t.IsActive).Select(t => t.TabIndex));
                }
            }
            return listTabs;
        }

        protected void CheckPermissionBreadCrumbItem()
        {
            if (_breadCrumbMenu == null) return;

            _breadCrumbMenu = _breadCrumbMenu
                .Select(item =>
                {
                    var filteredChildren = item.Childs
                        .Select(child =>
                        {
                            // Evaluate permissions for each child
                            child.EvaluatePermission();
                            return child;
                        })
                        .Where(child => child.IsAllow == true) // Remove children not allowed
                        .ToList();

                    item.Childs = filteredChildren;
                    return item;
                })
                .Where(item => item.Childs.Count > 0)
                .ToList();
        }
    }
}
