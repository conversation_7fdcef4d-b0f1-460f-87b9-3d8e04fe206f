﻿using GlobalTrader2.SharedUI.Models;
namespace GlobalTrader2.SharedUI.Constants
{
    public static class QuickJumpOptions
    {
        public static readonly IEnumerable<QuickJumpOption> OrderQuickJumpOptions = new[]
        {
            new QuickJumpOption
            {
                Id = "quickJumpSearchReq",
                Value = "Req",
                Label = "Req",
                Type = "number",
                ValidateUrl = "/orders/customer-requirements/search-id",
                ValidateKey = "reqNum",
                SearchKey = "req",
                Uri = Navigations.CustomerRequirementDetail.CtaUri,
                RelatedUris =
                [
                    Navigations.CustomerRequirements.CtaUri,
                    Navigations.CustomerRequirementDetail.CtaUri,
                    Navigations.CustomerRequirementAdd.CtaUri,
                    Navigations.PurchaseRequisitions.CtaUri,
                    Navigations.PurchaseRequisitionDetails.CtaUri
                ],
                MaxLength = 10,
            },
            new QuickJumpOption
            {
                Id = "quickJumpSearchSRMA",
                Value = "SRMA",
                Label = "SRMA",
                Type = "number",
                ValidateUrl = "/orders/supplier-rma/search-id",
                ValidateKey = "supplierRmaNum",
                SearchKey = "srma",
                DataKey = "supplierRMAId",
                Uri = Navigations.SupplierRmaDetail.CtaUri,
                RelatedUris =
                [
                    Navigations.SupplierRMAs.CtaUri,
                ],
                MaxLength = 10,
            },
            new QuickJumpOption
            {
                Id = "quickJumpSearchQuote",
                Value = "Quote",
                Label = "Quote",
                Type = "number",
                ValidateUrl = "/orders/quotes/search-id",
                ValidateKey = "quoteNum",
                SearchKey = "qt",
                Uri = Navigations.QuoteDetail.CtaUri,
                RelatedUris =
                [
                    Navigations.Quotes.CtaUri,
                    Navigations.PriceRequest.CtaUri,
                    Navigations.PriceRequestDetails("").CtaUri
                ],
                MaxLength = 10,
            },
            new QuickJumpOption
            {
                Id = "quickJumpSearchCredit",
                Value = "Credit",
                Label = "Credit",
                Type = "number",
                ValidateUrl = "/orders/credits/search-id",
                ValidateKey = "creditNum",
                SearchKey = "crd",
                Uri = Navigations.CreditNoteDetail.CtaUri,
                RelatedUris =
                [
                    Navigations.CreditNotes.CtaUri,
                ],
                MaxLength = 10,
            },
            new QuickJumpOption
            {
                Id = "quickJumpSearchPO",
                Value = "PO",
                Label = "PO",
                Type = "number",
                ValidateUrl = "/orders/purchase-order/search-id",
                ValidateKey = "poNum",
                SearchKey = "po",
                DataKey = "purchaseOrderId",
                Uri = Navigations.PurchaseOrderDetail("").CtaUri,
                RelatedUris =
                [
                    Navigations.PurchaseOrders.CtaUri,
                    Navigations.PurchaseOrdersAdd.CtaUri,
                ],
                MaxLength = 10,
            },
            new QuickJumpOption
            {
                Id = "quickJumpSearchDebit",
                Value = "Debit",
                Label = "Debit",
                Type = "number",
                ValidateUrl = "/orders/debits/search-id",
                ValidateKey = "debitNum",
                SearchKey = "deb",
                DataKey = "debitId",
                Uri = Navigations.DebitNoteDetail.CtaUri,
                RelatedUris =
                [
                    Navigations.DebitNotes.CtaUri,
                ],
                MaxLength = 10,
            },
            new QuickJumpOption
            {
                Id = "quickJumpSearchSO",
                Value = "SO",
                Label = "SO",
                Type = "number",
                ValidateUrl = "/orders/sales-orders/search-id",
                ValidateKey = "soNum",
                SearchKey = "so",
                DataKey = "salesOrderId",
                Uri = Navigations.SalesOrderDetail("").CtaUri,
                RelatedUris =
                [
                    Navigations.SalesOrders.CtaUri,
                    Navigations.SalesOrderAdd.CtaUri,
                ],
                MaxLength = 10,
            },
            new QuickJumpOption
            {
                Id = "quickJumpSearchSourcing",
                Value = "Sourcing",
                Label = "Sourcing",
                Type = "text",
                Uri = Navigations.Sourcing.CtaUri,
                SearchKey = "pn",
                MaxLength = 30,
                UpperCase = true,
            },
            new QuickJumpOption
            {
                Id = "quickJumpSearchInvoice",
                Value = "Invoice",
                Label = "Invoice",
                Type = "number",
                ValidateUrl = "/orders/invoices/search-id",
                ValidateKey = "invoiceNum",
                SearchKey = "inv",
                DataKey = "invoiceId",
                Uri = Navigations.InvoiceDetail.CtaUri,
                RelatedUris =
                [
                    Navigations.Invoices.CtaUri,
                ],
                MaxLength = 10,
            },
            new QuickJumpOption
            {
                Id = "quickJumpSearchIPO",
                Value = "IPO",
                Label = "IPO",
                Type = "number",
                ValidateUrl = "/orders/internal-purchase-orders/search-id",
                ValidateKey = "ipoNum",
                SearchKey = "ipo",
                Uri = Navigations.InternalPurchaseOrderDetail.CtaUri,
                RelatedUris =
                [
                    Navigations.InternalPurchaseOrders.CtaUri,
                ],
                MaxLength = 10,
            },
            new QuickJumpOption
            {
                Id = "quickJumpSearchCRMA",
                Value = "CRMA",
                Label = "CRMA",
                Type = "number",
                ValidateUrl = "/orders/customer-rma/search-id",
                ValidateKey = "customerRmaNum",
                SearchKey = "crma",
                DataKey = "customerRMAId",
                Uri = Navigations.CustomerRmaDetail.CtaUri,
                RelatedUris =
                [
                    Navigations.CustomerRMAs.CtaUri,
                ],
                MaxLength = 10,
            },
            new QuickJumpOption
            {
                Id = "quickJumpSearchHUBRFQ",
                Value = "HUBRFQ",
                Label = "HUBRFQ",
                Type = "text",
                ValidateUrl = "/orders/bom/search-id",
                ValidateKey = "bomName",
                SearchKey = "BOM",
                Uri = Navigations.BOMBrowseDetail.CtaUri,
                RelatedUris =
                [
                    Navigations.BOMBrowse.CtaUri,
                    Navigations.BOMBrowseAdd.CtaUri
                ],
                MaxLength = 30,
                UpperCase = true,
            },
        };

        public static readonly IEnumerable<QuickJumpOption> ContactQuickJumpOptions = new[]
        {
            new QuickJumpOption
            {
                Id = "quickJumpSearchCompany",
                Value = "Company",
                Label = "Company",
                Type = "text" ,
                Uri = Navigations.AllCompanies.CtaUri,
                SearchKey = "cmn",
                RelatedUris = new string[]
                {
                    Navigations.CompanyDetails("").CtaUri,
                    Navigations.CompanyDetails("").CtaUri + "?clt=0",
                    Navigations.AddCompany.CtaUri,
                    Navigations.Contact.CtaUri,
                },
                MaxLength = 40,
            },
            new QuickJumpOption
            {
                Id = "quickJumpSearchSupplier",
                Value = "Supplier",
                Label = "Supplier",
                Type = "text",
                Uri = Navigations.Suppliers.CtaUri,
                SearchKey = "cmn",
                RelatedUris = new string[]
                {
                    Navigations.CompanyDetails("").CtaUri + "?clt=2",
                },
                MaxLength = 40,
            },
            new QuickJumpOption
            {
                Id = "quickJumpSearchCustomer",
                Value = "Customer",
                Label = "Customer",
                Type = "text",
                Uri = Navigations.Customers.CtaUri,
                SearchKey = "cmn",
                RelatedUris = new string[]
                {
                    Navigations.CompanyDetails("").CtaUri + "?clt=1",
                    Navigations.CustomerGroupCode.CtaUri,
                },
                MaxLength = 40,
            },
            new QuickJumpOption
            {
                Id = "quickJumpSearchProspect",
                Value = "Prospect",
                Label = "Prospect",
                Type = "text",
                Uri = Navigations.Prospects.CtaUri,
                SearchKey = "cmn",
                RelatedUris = new string[]
                {
                    Navigations.CompanyDetails("").CtaUri + "?clt=3",
                },
                MaxLength = 40,
            },
            new QuickJumpOption{
                Id = "quickJumpSearchManufacturer",
                Value = "Manufacturer",
                Label = "Manufacturer",
                Type = "text",
                Uri = Navigations.Manufacturers.CtaUri,
                SearchKey = "mfn",
                RelatedUris = new string[]
                {
                    Navigations.AddInManufacturerGroupCode.CtaUri,
                    Navigations.AddManufacturer.CtaUri,
                    Navigations.ManufacturerDetails("").CtaUri,
                },
                MaxLength = 40,
            },
            new QuickJumpOption
            {
                Id = "quickJumpSearchContact",
                Value = "Contact",
                Label = "Contact",
                Type = "text",
                Uri = Navigations.Contacts.CtaUri,
                SearchKey = "ctn",
                RelatedUris = new string[]
                {
                    Navigations.ContactDetails("").CtaUri,
                },
                MaxLength = 40,
            },
        };
    }
}
