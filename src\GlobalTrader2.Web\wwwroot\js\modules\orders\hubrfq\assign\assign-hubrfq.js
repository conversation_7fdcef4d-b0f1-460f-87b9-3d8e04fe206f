﻿import { FilterTableSectionBox } from "../../../../components/base/filter-table-section-box.component.js?v=#{BuildVersion}#"
import { LiteDatatable } from "../../../../components/base/lite-datatable.component.js?v=#{BuildVersion}#"
import { SectionBox } from "../../../../components/base/section-box.component.js?v=#{BuildVersion}#"
import { AssignHUBRFQTalbeFilterComponent } from "./assign-hubrfq-table-filter.js?v=#{BuildVersion}#"
import { AssignHUBRFQFilterSectionBox } from "./assign-hubrfq-filter-section-box.js?v=#{BuildVersion}#"
import { inputFilterDefinition } from "./input-filter-definition.js?v=#{BuildVersion}#"
import { ClientId } from '../../constants/orders.constant.js?v=#{BuildVersion}';

$(async () => {
    const state = {
        tabId: 0, // default currentTab
        tabDic: {
            0: "my-tab",
            1: "team-tab",
        },
        filterStateType: 28,//BOMLines
        requirementsTable: null,
        filter: null,
        sectionBox: null,
        filterSectionBox: null,
        addRequirementDialogInited: false,
        lastFilter: null, // state already made a call
        isSearchRequirement: false,
    }
    const preferences = await getPreferences();
    state.preferences = preferences;

    function registerTabEvents() {
        $(document).on('click', '#assign-hubrfq-nav-tabs-wrapper button', async (e, data) => {
            const ignoreSave = data?.notResetPageRequired
            setTimeout(function () {
                openningTab($(e.target).attr("tabId"), ignoreSave, !data?.notResetPageRequired);
            }, 100);
        })
    }

    async function initFilterTableSectionBoxAsync() {
        state.requirementsTable = new LiteDatatable('#assignHubrfqTbl', {
            lengthChange: false, // Disable default length selector since we're using custom input
            info: false,
            paging: true,
            serverSide: true,
            ajax: {
                url: '/api/orders/bom/assign-list',
                type: 'POST',
            },
            ordering: true,
            disableSelect: false,
            pageConfig: {
                pageSize: 50 
            },
            columns: [
                {
                    data: 'bomId',
                    name: 'bomId',
                    visible: false
                },
                {
                    title: `<div style="margin-bottom: 5px; padding-bottom: 8px;">${assignHubrfqTitle.HubrfqName}</div>`,
                    className: 'text-wrap text-break header-custom align-top',
                    orderSequence: ["asc", "desc"],
                    data: 'bomName',
                    width: "14%",
                    render: function (data, type, row) {
                        let bomNameHtml = '<br>';

                        if (row.bomName) {
                            bomNameHtml = $('<a>')
                                .attr('href', GlobalTrader.PageUrlHelper.GET_URL_BOM(row.bomId))
                                .addClass('dt-hyper-link')
                                .text(row.bomName)
                                .prop('outerHTML') + '<br>';
                        }
                        return `${bomNameHtml}`;
                    }
                },
                {
                    title: `<div style="border-bottom: dotted 1px #999; margin-bottom: 5px; padding-bottom: 8px;">${assignHubrfqTitle.ClientCode}</div>${assignHubrfqTitle.RequestedBy}`,
                    className: 'text-wrap text-break header-custom align-top',
                    orderSequence: ["asc", "desc"],
                    data: 'clientCode',
                    width: "12%",
                    render: function (data, type, row) {
                        const clientCodeHtml = row.clientCode ? $('<a>').text(row.clientCode).prop('outerHTML') + '<br>' : '<br>';
                        const requestedByHtml = row.requestedby ? $('<a>').text(row.requestedby).prop('outerHTML') + '<br>' : '<br>';
                        return `${clientCodeHtml}${requestedByHtml}`;
                    }
                },
                {
                    title: `<div style="border-bottom: dotted 1px #999; margin-bottom: 5px; padding-bottom: 8px;">${assignHubrfqTitle.AssignedUser}</div>${assignHubrfqTitle.Quantity}`,
                    className: 'text-wrap text-break header-custom align-top',
                    orderSequence: ["asc", "desc"],
                    data: 'assignedUser',
                    width: "10%",
                    render: function (data, type, row) {
                        let assignedUserHtml = '';
                        assignedUserHtml = row.assignedUser ? $('<a>').text(row.assignedUser).prop('outerHTML') + '<br>' : '<br>';                    
                        const quantityHtml = row.quantity ? $('<a>').text(row.quantity).prop('outerHTML') + '<br>' : '<br>';

                        return `${assignedUserHtml}${quantityHtml}`;
                    }
                },
                {
                    title: `<div style="border-bottom: dotted 1px #999; margin-bottom: 5px; padding-bottom: 8px;">${assignHubrfqTitle.Code}</div>${assignHubrfqTitle.PartNo}`,
                    className: 'text-wrap text-break header-custom align-top',
                    orderSequence: ["asc", "desc"],
                    data: 'bomCode',
                    width: "8%",
                    render: function (data, type, row) {
                        const codeHtml = row.bomCode ? $('<a>').text(row.bomCode).prop('outerHTML') + '<br>' : '<br>';
                        const partHtml = row.part ? $('<a>').text(row.part).prop('outerHTML') + '<br>' : '<br>';

                        return `${codeHtml}${partHtml}`;
                    }
                },
                {
                    title: `<div style="margin-bottom: 5px; padding-bottom: 8px;">${assignHubrfqTitle.Company}</div>`,
                    className: 'text-wrap text-break header-custom align-top',
                    orderSequence: ["asc", "desc"],
                    data: 'companyName',
                    width: "10%",
                    render: function (data, type, row) {
                        const companyHtml = row.companyName ? $('<a>').text(row.companyName).prop('outerHTML') + '<br>' : '<br>';
                        return `${companyHtml}`;
                    }
                },
                {
                    title: `<div style="margin-bottom: 5px; padding-bottom: 8px;">${assignHubrfqTitle.Status}</div>`,
                    className: 'text-wrap text-break header-custom align-top',
                    orderSequence: ["asc", "desc"],
                    data: 'status',
                    width: "8%",
                    render: function (data, type, row) {
                        const statusHtml = row.status ? $('<a>').text(row.status).prop('outerHTML') + '<br>' : '<br>';
                        return `${statusHtml}`;
                    }
                }, 
                {
                    title: `<div style="margin-bottom: 5px; padding-bottom: 8px;">${assignHubrfqTitle.Date}</div>`,
                    className: 'text-wrap text-break header-custom align-top',
                    orderSequence: ["asc", "desc"],
                    data: 'dlup',
                    width: "8%",
                    render: function (data, type, row) {
                        const dlupHtml = row.dlup ? $('<a>').text(row.dlup).prop('outerHTML') + '<br>' : '<br>';
                        return `${dlupHtml}`;
                    }
                },                               
            ],
            rowId: 'bomId'
        })

        let inputs = inputFilterDefinition;

        const clientInput = inputs.find(x => x.name == "Client");
        clientInput.options.onSelected = (data, selectedClient) => {
            if (!data) {
                return;
            };

            let salespersonEndpoint = `/lists/employee`;
            if (selectedClient) {
                salespersonEndpoint += `?globalLoginClientNo=${selectedClient}`;
            } else {
                salespersonEndpoint += `?globalLoginClientNo=${ClientId.Default}`;
            }

            const salespersonFilter = state.filter.getInputElementByName("Salesperson");

            const currentSalespersonValue = $(salespersonFilter.element).dropdown("selectedValue");

            $(salespersonFilter.element).dropdown("reset");

            const handleSalespersonReload = (data, currentSelected) => {
                if (currentSalespersonValue && data?.some(item => item.loginId == currentSalespersonValue)) {
                    $(salespersonFilter.element).dropdown("select", currentSalespersonValue);
                }
            };

            $(salespersonFilter.element).dropdown("option", "onSuccessLoaded", handleSalespersonReload);

            $(salespersonFilter.element).dropdown("reload", salespersonEndpoint);
        }
        
        state.filter = new AssignHUBRFQTalbeFilterComponent("#assignHubrfq #filter-section-wrapper", "Filter Results", {
            inputConfigs: inputs
            //templateId:'my-template' // template insert input to filter
        });
        state.sectionBox = new SectionBox('#assignHubrfq', {
            loadingContentId: state.requirementsTable.getContainerId() // only table should be hidden while processing api requests
        }, {
            enableFilterButton: false
        });

        state.filterSectionBox = new AssignHUBRFQFilterSectionBox(state.requirementsTable, state.filter, state.sectionBox, {
            prepareDataBeforeReload: prepareDataBeforeReload
        });

        state.filterSectionBox.registerHiddenButtonFilterEvents();

        state.sectionBox.setLock(state.preferences.saveDLNState);
        state.filterSectionBox.setFilterStateType(state.filterStateType)
        await state.filterSectionBox.initAsync();
        $(`${state.requirementsTable.state.selector} thead th .dt-column-order`).addClass('dt-column-order-custom');;
        const appliedCount = state.filterSectionBox.countAppliedFilters(state.tabId);
        updateFilterAppliedMessage(appliedCount);
        activeTab(currentTab); 
    }

    async function getPreferences() {
        const response = await GlobalTrader.ApiClient.getAsync(`/user-account/profile/preferences`, {}, {});
        // handle error?
        return response?.data;
    }

    function setHeaderOrDetailDefault() {
        state.filter.getInputElementByName('HeaderOrDetail').setValue('header');
    }
    function setClientSelectState() {
        const clientInput = state.filter.getInputElementByName('Client');
        if (assignHubrfqPageState.IsPOHub) {
            clientInput.wrapper.show();
        } else {
            clientInput.wrapper.hide();
        }
    }

    function updateFilterAppliedMessage(appliedCount) {
        let message;
        if (appliedCount === 0) {
            message = `${assignHubrfqLocalized.No} ${assignHubrfqLocalized.Filter} ${assignHubrfqLocalized.Applied}`;
        } else {
            const filterLabel = appliedCount === 1 ? (assignHubrfqLocalized.Filter) : (assignHubrfqLocalized.Filters);
            message = `${appliedCount} ${filterLabel} ${assignHubrfqLocalized.Applied}`;
        }
        state.filter.container.find('p[name="count-filter-applied"]').text(message);
    }

    function setupFilterCountEvents() {
        let isCancel = false;
        state.filter.on('cancel.mtf', () => {
            isCancel = true;
        });

        state.filterSectionBox.on('processed.mftsb', () => {
            setTimeout(() => {
                if (!isCancel) {
                    const latestAppliedCount = state.filterSectionBox.countAppliedFilters(state.tabId);
                    updateFilterAppliedMessage(latestAppliedCount);
                }
                else {
                    isCancel = false;
                }
            }, 0);
        });
    }

    function activeTab(tabId) {
        state.tabId = tabId;
        let tab = state.tabDic[tabId];

        $(`#${tab}`).trigger('click', {
            notResetPageRequired: true
        });
    }

    function prepareDataBeforeReload(filterData) {
        state.isSearchRequirement = filterData.HeaderOrDetail?.value == 'detail';
        let headerOrDetailValue = filterData.HeaderOrDetail?.value == 'detail' ? 'detail' : 'header';
        let requestData = {
            ViewLevelList: state.tabId,
            HeaderOrDetail: headerOrDetailValue,
            Status: filterData.Status?.value || null,
            AssignedUser: filterData.AssignedUser?.value || null,
            Division: filterData.Division?.value || null,
            SalesPersonId: filterData.Salesperson?.value || null,
            SelectedClientId: filterData.Client?.value || null,
            Name: !shouldInclude(filterData.Name) ? null : filterData.Name?.value,
            Code: !shouldInclude(filterData.Code) ? null : filterData.Code?.value,
            IsAS6081Tab: (state.tabId == 1),
            MyPageSize: parseInt($('#customPageLength').val()) || 50
        }
        function shouldInclude(input) {
            return input?.isOn && input.isShown;
        }
        return requestData;
    }
    async function openningTab(tabId, ignoreSave, resetPageRequired) {
        // this section for example & testing
        //params for testing
        state.tabId = tabId;
        const data = state.filter.getAppliedValues();
        const convertedData = prepareDataBeforeReload(data)
        if (resetPageRequired) {
            state.requirementsTable.resetPage();
        }
        convertedData._ignoredSave = ignoreSave;
        await state.requirementsTable.reloadAsync(convertedData);
    }

    function setupEventListeners() {
        state.filter.on('applied.mtf', async function() {
        const newLength = parseInt($('#customPageLength').val());
        if (newLength > 0) {
            state.requirementsTable.setPageSize(newLength);
            
            const data = state.filter.getAppliedValues();
            const convertedData = prepareDataBeforeReload(data);
                       
            await state.requirementsTable.reloadAsync(convertedData);
        }
        });

        state.requirementsTable.on('draw.dt', function () {
            const info = state.requirementsTable.state.table.page.info();

            $('#customTableInfo').text(
                `Showing ${info.recordsTotal === 0 ? 0 : info.start + 1} to ${info.end} of ${info.recordsTotal} entries`
            );

            // Hide all default DataTables controls since we're using custom ones
            const pagination = $('#assignHubrfqTbl_wrapper').find('.dt-paging');
            const pageLength = $('#assignHubrfqTbl_wrapper').find('.dt-length');
            pagination.hide();
            pageLength.hide();
        });
    }

    async function initAsync() {
        registerTabEvents();
        await initFilterTableSectionBoxAsync();
        setupFilterCountEvents();
        setHeaderOrDetailDefault();
        setClientSelectState();
        setupEventListeners();
    }


    await initAsync();
}) 