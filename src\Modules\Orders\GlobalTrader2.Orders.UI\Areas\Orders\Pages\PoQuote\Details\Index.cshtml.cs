﻿using GlobalTrader2.UserAccount.UseCases.RecentlyViewed.Commands.Create;
using Microsoft.AspNetCore.Http;
using GlobalTrader2.Orders.UserCases.Orders.PurchaseOrderQuote.Queries;
using GlobalTrader2.Orders.UserCases.Orders.PurchaseOrderQuote.Queries.DTO;

namespace GlobalTrader2.Orders.UI.Areas.Orders.Pages.PoQuote.Details
{
    public class IndexModel : BasePageModel
    {
        private readonly SessionManager _sessionManager;
        private readonly IMediator _mediator;

        [FromQuery(Name = "pqt")]
        public int? PoQuoteId { get; set; }
        public POQuoteForPageDto Detail { get; set; }

        public IndexModel(SecurityManager securityManager, IMediator mediator, SessionManager sessionManager) : base(securityManager)
        {
            SiteSection = SiteSection.Orders;
            PageType = SitePage.Orders_POQuoteDetail;
            _sessionManager = sessionManager;
            _mediator = mediator;
        }


        public async Task<IActionResult> OnGetAsync(CancellationToken cancellationToken)
        {
            if (!PoQuoteId.HasValue || !_sessionManager.IsPOHub)
                return NotFound();


            // Load BOM details
            var detail = await _mediator.Send(new GetPOQuoteForPageQuery(PoQuoteId.Value), cancellationToken);
            if (detail?.Data == null)
                return NotFound();

            Detail = detail.Data;

            await SetupPermissions();
            AddBreadCrumbs();
            await UpdateRecentlyView();

            return Page();
        }

        // Loading perrmission of Setup section level
        private async Task SetupPermissions()
        {
            var loginId = HttpContext.Session.GetInt32(SessionKey.LoginID);

            if (!loginId.HasValue) return;

            await _securityManager!.LoadSectionLevelPermissions(loginId.Value, (int)SiteSection.Orders, false);
        }  


        private void AddBreadCrumbs()
        {
            BreadCrumb.Add(Navigations.Home);
            BreadCrumb.Add(Navigations.Orders);
            BreadCrumb.Add(Navigations.PriceRequest);
            BreadCrumb.Add(Navigations.PriceRequestDetails(Detail.POQuoteNumber.ToString()));// TODO: Need to populate correct, then remove this comment
        }

        private async Task UpdateRecentlyView()
        {
            var lastBreadcrumb = BreadCrumb[^1];
            var command = new CreateRecentlyViewedCommand
            {
                LoginNo = HttpContext.Session.GetInt32(SessionKey.LoginID),
                PageTitle = lastBreadcrumb.PageTitle,
                PageUrl = $"{lastBreadcrumb.CtaUri}{HttpContext.Request.QueryString.Value}"
            };
            await _mediator.Send(command);
        }
    }
}
