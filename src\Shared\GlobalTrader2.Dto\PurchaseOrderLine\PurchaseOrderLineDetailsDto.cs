namespace GlobalTrader2.Dto.PurchaseOrderLine
{
    public class PurchaseOrderLineDetailsDto
    {
        public int PurchaseOrderLineId { get; set; }
        public int PurchaseOrderNo { get; set; }
        public string FullPart { get; set; } = string.Empty;
        public string Part { get; set; } = string.Empty;
        public int Quantity { get; set; }
        public decimal Price { get; set; }
        public DateTime DeliveryDate { get; set; }
        public double IpoLineTotal { get; set; }
        public string CurrencyCode { get; set; } = string.Empty;
        public string ClientCurrencyCode { get; set; } = string.Empty;
        public decimal LineProfit { get; set; }
        public decimal LineProfitPercentage { get; set; }
        public int CurrencyNo { get; set; }
        public DateTime DateOrdered { get; set; }
        public int HubCurrencyNo { get; set; }

        // Add more fields below (if needed)
        public byte? ROHS { get; set; }
        public string? SupplierPart { get; set; }
        public string? DateCode { get; set; }
        public string? ProductDescription { get; set; }
        public string? ProductDutyCode { get; set; }
        public DateTime? PromiseDate { get; set; }
        public int? SupplierWarranty { get; set; }
        public string? CountryOfOrigin { get; set; }
        public string? IHSProduct { get; set; }
        public string? HTSCode { get; set; }
        public string? ECCNCode { get; set; }
        public string? PackagingSize { get; set; }
        public string? Descriptions { get; set; }
        public bool? RepeatOrder { get; set; }
        public int? QuantityReceived { get; set; }
        public int? QuantityAllocated { get; set; }
        public bool? Taxable { get; set; }
        public decimal? ShipInCost { get; set; }
        public string? LineNotes { get; set; }
        public bool? ReqSerialNo { get; set; }
        public string? MSLevel { get; set; }
        public bool? AS6081 { get; set; }
    }
}