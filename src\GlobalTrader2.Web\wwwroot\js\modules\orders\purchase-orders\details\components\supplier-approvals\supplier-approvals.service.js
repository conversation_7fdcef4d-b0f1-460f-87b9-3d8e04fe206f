import { PurchaseOrderDetailsApiUrl } from '../../../../../../config/api-endpoint-config.js?v=#{BuildVersion}#';

export class SupplierApprovalsService {
    constructor() {
        this.apiUrl = PurchaseOrderDetailsApiUrl;
    }
    
    async getTabDataAsync(purchaseOrderId, tabLevel) {
        switch (tabLevel) {
            case 0: // APPROVAL_STATUS
                return this.getApprovalStatusData(purchaseOrderId);
            case 1: // SUPPLIER_INFORMATION
                return this.getSupplierInformationData(purchaseOrderId);
            case 2: // SUPPLIER_APPROVAL_HISTORY
                return this.getSupplierApprovalHistoryData(purchaseOrderId);
            default:    
                return [];
        }
    }

    async getApprovalStatusData(purchaseOrderId) {
        return [];
    }
    async getSupplierInformationData(purchaseOrderId) {
        return await GlobalTrader.ApiClient.getAsync(`${this.apiUrl}/${purchaseOrderId}/supplier-approval/trade-references`)
    }
    async getSupplierApprovalHistoryData(purchaseOrderId) {
        return []; 
    }
}