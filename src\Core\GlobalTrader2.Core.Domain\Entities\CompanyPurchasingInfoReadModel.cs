﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GlobalTrader2.Core.Domain.Entities
{
    [Keyless]
    public class CompanyPurchasingInfoReadModel
    {
        public int? CompanyId { get; set; }
        public int? GlobalCurrencyNo { get; set; }
        public string? POCurrencyCode { get; set; }
        public int? SupplierWarranty { get; set; }
        public double? UPLiftPrice { get; set; }
        public double? ESTShippingCost { get; set; }
        public bool? NonPreferredCompany { get; set; }
    }
}
