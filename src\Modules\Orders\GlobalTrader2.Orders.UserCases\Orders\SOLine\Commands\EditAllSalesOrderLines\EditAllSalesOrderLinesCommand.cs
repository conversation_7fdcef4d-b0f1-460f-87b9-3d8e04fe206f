namespace GlobalTrader2.Orders.UserCases.Orders.SOLine.Commands.EditSalesOrderLine
{
    public class EditAllSalesOrderLinesCommand : IRequest<BaseResponse<int>>
    {
        public int SalesOrderNo { get; set; }
        public DateTime DatePromised { get; set; }
        public int UpdatedBy { get; set; }
        public bool IsReasonChanged { get; set; }
        public int PromiseReasonNo { get; set; }

        public DateTime OldEarliestDatePromised { get; set; }
        public bool? IsSOAuthorized { get; set; }
        public bool? IsSOAutoAuthorized { get; set; }
        public bool? AllowEditDatePromisedBetweenCurrentMonthAndEnd { get; set; }
    }
}