﻿using GlobalTrader2.Orders.UserCases.Orders.PurchaseOrder.Queries.GetEprList;

namespace GlobalTrader2.Orders.UserCases.Test.Orders.PurchaseOrder.Queries.GetEprList;
public class GetEprListHandlerTests
{
    private readonly Mock<IBaseRepository<Epr>> _mockRepository;
    private readonly GetEprListHandler _handler;
    private readonly Fixture _fixture;

    public GetEprListHandlerTests()
    {
        _mockRepository = new Mock<IBaseRepository<Epr>>();
        _handler = new GetEprListHandler(_mockRepository.Object);
        _fixture = new Fixture();
    }

    [Fact]
    public async Task Handle_GivenRepositoryReturnsNull_ShouldThrowArgumentException()
    {
        // Arrange
        var query = new GetEprListQuery(PurchaseOrderId: 1, OrderBy: null);

        _mockRepository
            .Setup(r => r.ListAsync(
                It.IsAny<Expression<Func<Epr, bool>>>(),
                It.IsAny<Func<IQueryable<Epr>, IOrderedQueryable<Epr>>>(),
                It.IsAny<Expression<Func<Epr, object?>>[]>()))
            .ReturnsAsync((List<Epr>)null!);

        // Act & Assert
        var ex = await Assert.ThrowsAsync<ArgumentException>(() => _handler.Handle(query, CancellationToken.None));

        Assert.Equal($"Cannot find list of EPR with the purchase order id {query.PurchaseOrderId}", ex.Message);
    }

    [Fact]
    public async Task Handle_WhenPurchaseOrderFound_ShouldMapAndReturnCorrectly()
    {
        // Arrange
        var query = new GetEprListQuery(PurchaseOrderId: 1, OrderBy: null);

        var readModel = _fixture.Create<Epr>();
        var mockResult = new List<Epr> { readModel };

        _mockRepository
            .Setup(r => r.ListAsync(
                It.IsAny<Expression<Func<Epr, bool>>>(),
                It.IsAny<Func<IQueryable<Epr>, IOrderedQueryable<Epr>>>(),
                It.IsAny<Expression<Func<Epr, object?>>[]>()))
            .ReturnsAsync(mockResult);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.Success);
        Assert.NotNull(result.Data);
        Assert.Single(result.Data);
        Assert.Same(readModel, result.Data.First());

        _mockRepository.Verify(r => r.ListAsync(
            It.IsAny<Expression<Func<Epr, bool>>>(),
            It.IsAny<Func<IQueryable<Epr>, IOrderedQueryable<Epr>>>(),
            It.IsAny<Expression<Func<Epr, object?>>[]>()), Times.Once);
    }
}
