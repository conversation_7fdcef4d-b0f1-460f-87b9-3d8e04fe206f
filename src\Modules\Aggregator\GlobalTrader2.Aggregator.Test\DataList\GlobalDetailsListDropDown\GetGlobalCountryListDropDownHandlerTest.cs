using AutoFixture;
using GlobalTrader2.Aggregator.UseCases.DataList.GlobalCountyListDropDown;
using GlobalTrader2.Core;
using GlobalTrader2.Core.Domain.Entities;
using GlobalTrader2.Dto.Base;
using Moq;
using Microsoft.Data.SqlClient;
using System.Data;

namespace GlobalTrader2.Aggregator.Test.DataList.GlobalDetailsListDropDown
{
    public class GetGlobalCountryListDropDownHandlerTest
    {
        private readonly Mock<IBaseRepository<MasterCountryDropdownReadModel>> _mockRepository;
        private readonly GetGlobalCountryListDropDownHandler _handler;
        private readonly IFixture _fixture;

        public GetGlobalCountryListDropDownHandlerTest()
        {
            _mockRepository = new Mock<IBaseRepository<MasterCountryDropdownReadModel>>();
            _handler = new GetGlobalCountryListDropDownHandler(_mockRepository.Object);
            _fixture = new Fixture();
        }

        [Fact]
        public async Task Handle_WhenCountriesExist_ReturnsSuccessWithData()
        {
            // Arrange
            var query = _fixture.Create<GetGlobalCountryListDropDownQuery>();
            var countries = _fixture.CreateMany<MasterCountryDropdownReadModel>(3).ToList();
            
            // Set specific properties to test mapping
            countries[0].GlobalCountryId = 1;
            countries[0].GlobalCountryName = "United States";
            countries[1].GlobalCountryId = 2;
            countries[1].GlobalCountryName = "Canada";
            countries[2].GlobalCountryId = 3;
            countries[2].GlobalCountryName = "";

            _mockRepository
                .Setup(repo => repo.SqlQueryRawAsync(It.IsAny<string>(), It.IsAny<SqlParameter[]>()))
                .ReturnsAsync(countries);

            // Act
            var response = await _handler.Handle(query, CancellationToken.None);

            // Assert
            Assert.True(response.Success);
            Assert.NotNull(response.Data);
            Assert.Equal(3, response.Data.Count);
            
            Assert.Equal(1, response.Data[0].Id);
            Assert.Equal("United States", response.Data[0].Name);
            Assert.Equal(2, response.Data[1].Id);
            Assert.Equal("Canada", response.Data[1].Name);
            Assert.Equal(3, response.Data[2].Id);
            Assert.Equal(string.Empty, response.Data[2].Name); // Empty string mapped correctly
        }

        [Fact]
        public async Task Handle_WhenNoCountriesExist_ReturnsSuccessWithEmptyList()
        {
            // Arrange
            var query = _fixture.Create<GetGlobalCountryListDropDownQuery>();
            var emptyList = new List<MasterCountryDropdownReadModel>();

            _mockRepository
                .Setup(repo => repo.SqlQueryRawAsync(It.IsAny<string>(), It.IsAny<SqlParameter[]>()))
                .ReturnsAsync(emptyList);

            // Act
            var response = await _handler.Handle(query, CancellationToken.None);

            // Assert
            Assert.True(response.Success);
            Assert.NotNull(response.Data);
            Assert.Empty(response.Data);
        }

        [Fact]
        public async Task Handle_WhenIncludeSelectedIsNull_SetsToTrue()
        {
            // Arrange
            var query = new GetGlobalCountryListDropDownQuery
            {
                IncludeSelected = null,
                ClientNo = 123
            };

            var countries = _fixture.CreateMany<MasterCountryDropdownReadModel>(1).ToList();

            _mockRepository
                .Setup(repo => repo.SqlQueryRawAsync(It.IsAny<string>(), It.IsAny<SqlParameter[]>()))
                .ReturnsAsync(countries);

            // Act
            var response = await _handler.Handle(query, CancellationToken.None);

            // Assert
            Assert.True(response.Success);
            Assert.True(query.IncludeSelected); // Should be set to true
        }

        [Fact]
        public async Task Handle_WhenIncludeSelectedIsFalse_SetsToTrue()
        {
            // Arrange
            var query = new GetGlobalCountryListDropDownQuery
            {
                IncludeSelected = false,
                ClientNo = 123
            };

            var countries = _fixture.CreateMany<MasterCountryDropdownReadModel>(1).ToList();

            _mockRepository
                .Setup(repo => repo.SqlQueryRawAsync(It.IsAny<string>(), It.IsAny<SqlParameter[]>()))
                .ReturnsAsync(countries);

            // Act
            var response = await _handler.Handle(query, CancellationToken.None);

            // Assert
            Assert.True(response.Success);
            Assert.True(query.IncludeSelected); // Should be set to true
        }

        [Fact]
        public async Task Handle_WhenIncludeSelectedIsTrue_RemainsTrue()
        {
            // Arrange
            var query = new GetGlobalCountryListDropDownQuery
            {
                IncludeSelected = true,
                ClientNo = 123
            };

            var countries = _fixture.CreateMany<MasterCountryDropdownReadModel>(1).ToList();

            _mockRepository
                .Setup(repo => repo.SqlQueryRawAsync(It.IsAny<string>(), It.IsAny<SqlParameter[]>()))
                .ReturnsAsync(countries);

            // Act
            var response = await _handler.Handle(query, CancellationToken.None);

            // Assert
            Assert.True(response.Success);
            Assert.True(query.IncludeSelected); // Should remain true
        }

        [Fact]
        public async Task Handle_CallsRepositoryWithCorrectParameters()
        {
            // Arrange
            var query = new GetGlobalCountryListDropDownQuery
            {
                IncludeSelected = true,
                ClientNo = 456
            };

            var countries = _fixture.CreateMany<MasterCountryDropdownReadModel>(1).ToList();

            _mockRepository
                .Setup(repo => repo.SqlQueryRawAsync(It.IsAny<string>(), It.IsAny<SqlParameter[]>()))
                .ReturnsAsync(countries);

            // Act
            await _handler.Handle(query, CancellationToken.None);

            // Assert
            _mockRepository.Verify(
                repo => repo.SqlQueryRawAsync(
                    It.Is<string>(s => s.Contains("@IncludeSelected, @ClientNo")),
                    It.Is<SqlParameter[]>(p => 
                        p.Length == 2 &&
                        p[0].ParameterName == "@IncludeSelected" &&
                        p[0].Value.Equals(true) &&
                        p[1].ParameterName == "@ClientNo" &&
                        p[1].Value.Equals(456)
                    )
                ),
                Times.Once
            );
        }

        [Fact]
        public async Task Handle_WithNullGlobalCountryName_MapsToEmptyString()
        {
            // Arrange
            var query = _fixture.Create<GetGlobalCountryListDropDownQuery>();
            var countries = new List<MasterCountryDropdownReadModel>
            {
                new MasterCountryDropdownReadModel 
                { 
                    GlobalCountryId = 1, 
                    GlobalCountryName = null 
                }
            };

            _mockRepository
                .Setup(repo => repo.SqlQueryRawAsync(It.IsAny<string>(), It.IsAny<SqlParameter[]>()))
                .ReturnsAsync(countries);

            // Act
            var response = await _handler.Handle(query, CancellationToken.None);

            // Assert
            Assert.True(response.Success);
            Assert.NotNull(response.Data);
            Assert.Single(response.Data);
            Assert.Equal(1, response.Data[0].Id);
            Assert.Equal(string.Empty, response.Data[0].Name);
        }
    }
}
