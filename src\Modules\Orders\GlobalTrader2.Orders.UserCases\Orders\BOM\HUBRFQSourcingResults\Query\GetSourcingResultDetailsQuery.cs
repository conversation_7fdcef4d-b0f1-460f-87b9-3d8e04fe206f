﻿using GlobalTrader2.Dto.CustomerRequirement;
using GlobalTrader2.Dto.Sourcing;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GlobalTrader2.Orders.UserCases.Orders.BOM.HUBRFQSourcingResults.Query
{
    public class GetSourcingResultDetailsQuery : IRequest<BaseResponse<SourcingResultsDetailsHUBRFQ>>
    {
        public int SourcingResultId { get; set; }
    }
}
