using GlobalTrader2.Dto.BOM;
using GlobalTrader2.Orders.UserCases.Orders.BOM;

namespace GlobalTrader2.Orders.UserCases.Test.Orders.BOM.Queries
{
    public class BOMDLNDetailQueryHandlerTest
    {
        private readonly Mock<IBaseRepository<BOMDLNDetailModel>> _mockRepository;
        private readonly Mock<IBaseRepository<BOMPHDetailModel>> _mockBomPOHubRepository;
        private readonly Mock<IBaseRepository<BOMPHAssignModel>> _mockBomPHAssignRepository;
        private readonly Mock<IBaseRepository<BOMPHDetailAssignModel>> _mockBomPHDetailAssignRepository;
        private readonly Mock<IBaseRepository<BOMPHAS6081AssignModel>> _mockBomPHAS6081AssignRepository;
        private readonly Mock<IBaseRepository<BOMPHAS6081DetailAssignModel>> _mockBomPHAS6081DetailAssignRepository;
        private readonly Mock<IMapper> _mockMapper;
        private readonly BOMDLNDetailQueryHandler _handler;

        public BOMDLNDetailQueryHandlerTest()
        {
            _mockRepository = new Mock<IBaseRepository<BOMDLNDetailModel>>();
            _mockBomPOHubRepository = new Mock<IBaseRepository<BOMPHDetailModel>>();
            _mockBomPHAssignRepository = new Mock<IBaseRepository<BOMPHAssignModel>>();
            _mockBomPHDetailAssignRepository = new Mock<IBaseRepository<BOMPHDetailAssignModel>>();
            _mockBomPHAS6081AssignRepository = new Mock<IBaseRepository<BOMPHAS6081AssignModel>>();
            _mockBomPHAS6081DetailAssignRepository = new Mock<IBaseRepository<BOMPHAS6081DetailAssignModel>>();
            _mockMapper = new Mock<IMapper>();
            _handler = new BOMDLNDetailQueryHandler(
                _mockRepository.Object,
                _mockBomPOHubRepository.Object,
                _mockBomPHAssignRepository.Object,
                _mockBomPHDetailAssignRepository.Object,
                _mockBomPHAS6081AssignRepository.Object,
                _mockBomPHAS6081DetailAssignRepository.Object,
                _mockMapper.Object);
        }

        [Fact]
        public async Task Handle_WithValidRequest_ReturnsSuccessWithMappedData()
        {
            // Arrange
            var query = new BOMDLNDetailQuery
            {
                ClientId = 123,
                TeamId = 456,
                PageIndex = 0,
                PageSize = 10,
                BOMCode = "TEST",
                BOMName = "Test BOM"
            };

            var entities = new List<BOMDLNDetailModel>
            {
                new BOMDLNDetailModel { BOMId = 1, BOMName = "Test BOM", BOMCode = "TEST" }
            };

            var expectedDTOs = new List<BOMDLNDetailDTO>
            {
                new BOMDLNDetailDTO { BOMId = 1, BOMName = "Test BOM", BOMCode = "TEST" }
            };

            _mockRepository.Setup(r => r.SqlQueryRawAsync(It.IsAny<string>(), It.IsAny<object[]>()))
                .ReturnsAsync(entities);

            _mockMapper.Setup(m => m.Map<IEnumerable<BOMDLNDetailDTO>>(entities))
                .Returns(expectedDTOs);

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.Success.Should().BeTrue();
            result.Data.Should().NotBeNull();
            result.Data.Should().HaveCount(1);
            result.Data.First().BOMId.Should().Be(1);
        }
        [Fact]
        public async Task Handle_WithValidRequest_ReturnsSuccessWithMappedDataFromPoHubRepo()
        {
            // Arrange
            var query = new BOMDLNDetailQuery
            {
                ClientId = 123,
                TeamId = 456,
                PageIndex = 0,
                PageSize = 10,
                BOMCode = "TEST",
                BOMName = "Test BOM",
                IsPOHub = true,
                IsAssignToMe = 0
            };

            var entities = new List<BOMPHDetailModel>
            {
                new() {
                    ID = 1,
                    BOMId = 1,
                    BOMName = "Test BOM",
                    BOMCode = "TEST",
                    RowCnt = 1,
                    CompanyName = "Test Comp",
                    CompanyNo = 1,
                    CompanyType = "Type",
                    Inactive = false,
                    DLUP = DateTime.Now,
                    CurrencyId = 1,
                    TotalBomLinePrice = 1,
                    POCurrencyNo = 2,
                    DateRequestToPOHub = DateTime.Now,
                    DivisionName = "Division name",
                    Requestedby = "Name",
                    SalesmanName = "Name",
                    ContactName = "Contact",
                    ExpediteNotes = "notes",
                    ContactNo = 1,
                    RequiredDate = DateTime.Now,
                    RequiredDateStatus = "Test",
                    AssignedUser = "Test",
                    Status = "Test",
                    RowNum = 2
                }
            };

            var expectedDTOs = new List<BOMDLNDetailDTO>
            {
                new BOMDLNDetailDTO { BOMId = 1, BOMName = "Test BOM", BOMCode = "TEST" }
            };

            _mockBomPOHubRepository.Setup(r => r.SqlQueryRawAsync(It.IsAny<string>(), It.IsAny<object[]>()))
                .ReturnsAsync(entities);

            _mockMapper.Setup(m => m.Map<IEnumerable<BOMDLNDetailDTO>>(entities))
                .Returns(expectedDTOs);

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.Success.Should().BeTrue();
            result.Data.Should().NotBeNull();
            result.Data.Should().HaveCount(1);
            result.Data.First().BOMId.Should().Be(1);
        }
        [Fact]
        public async Task Handle_WithValidRequest_ReturnsSuccessWithMappedDataFromPHAssignRepo()
        {
            // Arrange
            var query = new BOMDLNDetailQuery
            {
                ClientId = 1,
                TeamId = 2,
                PageIndex = 0,
                PageSize = 10,
                BOMCode = "BOMCODE",
                BOMName = "BOMNAME",
                IsPOHub = true,
                IsAssignToMe = 1,
                MyPageSize = 0,
                IsAS6081Tab = false,
                IsSearchFromRequirements = false
            };
            var entities = new List<BOMPHAssignModel>
            {
                new BOMPHAssignModel
                {
                BOMId = 1,
                BOMCode = "BOMCODE",
                BOMName = "BOMNAME",
                Inactive = false,
                RowNum = 1,
                CompanyName = "Company",
                CompanyNo = 2,
                Status = "Status",
                DLUP = DateTime.Now,
                DateRequestToPOHub = DateTime.Now,
                AssignedUser = "User",
                ClientCode = "ClientCode",
                RequestedBy = "Requester",
                SalesmanName = "Salesman",
                ContactName = "Contact",
                ContactNo = 3,
                RequiredDateStatus = "RequiredDateStatus",
                RequiredDate = DateTime.Now,
                ExpediteNotes = "Notes",
                RowCnt = 4
                }
            };
                    var expectedDTOs = new List<BOMDLNDetailDTO>
            {
                new BOMDLNDetailDTO
                {
                    BOMId = 2,
                    BOMName = "BOMNAME",
                    BOMCode = "BOMCODE"
                    // set other properties as needed
                }
            };

            _mockBomPHAssignRepository.Setup(r => r.SqlQueryRawAsync(It.IsAny<string>(), It.IsAny<object[]>()))
                .ReturnsAsync(entities);

            _mockMapper.Setup(m => m.Map<IEnumerable<BOMDLNDetailDTO>>(entities))
                .Returns(expectedDTOs);

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.Success.Should().BeTrue();
            result.Data.Should().NotBeNull();
            result.Data.Should().HaveCount(1);
        }

        [Fact]
        public async Task Handle_WithValidRequest_ReturnsSuccessWithMappedDataFromPHDetailAssignRepo()
        {
            // Arrange
            var query = new BOMDLNDetailQuery
            {
                ClientId = 1,
                TeamId = 2,
                PageIndex = 0,
                PageSize = 10,
                BOMCode = "BOMCODE",
                BOMName = "BOMNAME",
                IsPOHub = true,
                IsAssignToMe = 1,
                MyPageSize = 0,
                IsAS6081Tab = false,
                IsSearchFromRequirements = true
            };
            var entities = new List<BOMPHDetailAssignModel>
            {
                new BOMPHDetailAssignModel
                {
                BOMId = 1,
                BOMCode = "BOMCODE",
                BOMName = "BOMNAME",
                Inactive = false,
                RowNum = 1,
                CompanyName = "Company",
                CompanyNo = 2,
                Status = "Status",
                DLUP = DateTime.Now,
                DateRequestToPOHub = DateTime.Now,
                AssignedUser = "User",
                ClientCode = "ClientCode",
                RequestedBy = "Requester",
                SalesmanName = "Salesman",
                ContactName = "Contact",
                ContactNo = 3,
                RequiredDateStatus = "RequiredDateStatus",
                RequiredDate = DateTime.Now,
                ExpediteNotes = "Notes",
                Part = "Part",
                Quantity = 5,
                CustomerRequirementId = 6,
                RowCnt = 4
                }
            };
                    var expectedDTOs = new List<BOMDLNDetailDTO>
            {
                new BOMDLNDetailDTO
                {
                    BOMId = 2,
                    BOMName = "BOMNAME",
                    BOMCode = "BOMCODE"
                    // set other properties as needed
                }
            };

            _mockBomPHDetailAssignRepository.Setup(r => r.SqlQueryRawAsync(It.IsAny<string>(), It.IsAny<object[]>()))
                .ReturnsAsync(entities);

            _mockMapper.Setup(m => m.Map<IEnumerable<BOMDLNDetailDTO>>(entities))
                .Returns(expectedDTOs);

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.Success.Should().BeTrue();
            result.Data.Should().NotBeNull();
            result.Data.Should().HaveCount(1);
        }

        [Fact]
        public async Task Handle_WithValidRequest_ReturnsSuccessWithMappedDataFromPHAS6081AssignRepo()
        {
            // Arrange
            var query = new BOMDLNDetailQuery
            {
                ClientId = 1,
                TeamId = 2,
                PageIndex = 0,
                PageSize = 10,
                BOMCode = "BOMCODE",
                BOMName = "BOMNAME",
                IsPOHub = true,
                IsAssignToMe = 1,
                MyPageSize = 0,
                IsSearchFromRequirements = false,
                IsAS6081Tab = true,
            };
            var entities = new List<BOMPHAS6081AssignModel>
            {
                new BOMPHAS6081AssignModel
                {
                BOMId = 1,
                BOMCode = "BOMCODE",
                BOMName = "BOMNAME",
                Inactive = false,
                RowNum = 1,
                CompanyName = "Company",
                CompanyNo = 2,
                Status = "Status",
                DLUP = DateTime.Now,
                DateRequestToPOHub = DateTime.Now,
                AssignedUser = "User",
                ClientCode = "ClientCode",
                RequestedBy = "Requester",
                SalesmanName = "Salesman",
                ContactName = "Contact",
                ContactNo = 3,
                RequiredDateStatus = "RequiredDateStatus",
                RequiredDate = DateTime.Now,
                ExpediteNotes = "Notes",
                RowCnt = 4
                }
            };
                    var expectedDTOs = new List<BOMDLNDetailDTO>
            {
                new BOMDLNDetailDTO
                {
                    BOMId = 2,
                    BOMName = "BOMNAME",
                    BOMCode = "BOMCODE"
                    // set other properties as needed
                }
            };

            _mockBomPHAS6081AssignRepository.Setup(r => r.SqlQueryRawAsync(It.IsAny<string>(), It.IsAny<object[]>()))
                .ReturnsAsync(entities);

            _mockMapper.Setup(m => m.Map<IEnumerable<BOMDLNDetailDTO>>(entities))
                .Returns(expectedDTOs);

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.Success.Should().BeTrue();
            result.Data.Should().NotBeNull();
            result.Data.Should().HaveCount(1);
        }

        [Fact]
        public async Task Handle_WithValidRequest_ReturnsSuccessWithMappedDataFromPHAS6081DetailAssignRepo()
        {
            // Arrange
            var request = new GetBOMAssignRequest
            {
                ViewLevelList = 1,
                Code = "BOMCODE",
                Name = "BOMNAME",
                Status = 1,
                Division = 1,
                SalesPersonId = 1,
                IsAS6081Tab = true,
                MyPageSize = 50,
                AssignedUser = null,
                HeaderOrDetail = "header",
                SelectedClientId = 1,
                CompanyTypeId = 1
            };
            var query = new BOMDLNDetailQuery
            {
                ClientId = 1,
                TeamId = 2,
                PageIndex = 0,
                PageSize = 10,
                BOMCode = "BOMCODE",
                BOMName = "BOMNAME",
                IsPOHub = true,
                IsAssignToMe = 1,
                MyPageSize = 0,
                IsSearchFromRequirements = true,
                IsAS6081Tab = true,
            };
            var entities = new List<BOMPHAS6081DetailAssignModel>
            {
                new BOMPHAS6081DetailAssignModel
                {
                BOMId = 1,
                BOMCode = "BOMCODE",
                BOMName = "BOMNAME",
                Inactive = false,
                RowNum = 1,
                CompanyName = "Company",
                CompanyNo = 2,
                Status = "Status",
                DLUP = DateTime.Now,
                DateRequestToPOHub = DateTime.Now,
                AssignedUser = "User",
                ClientCode = "ClientCode",
                RequestedBy = "Requester",
                SalesmanName = "Salesman",
                ContactName = "Contact",
                ContactNo = 3,
                RequiredDateStatus = "RequiredDateStatus",
                RequiredDate = DateTime.Now,
                ExpediteNotes = "Notes",
                Part = "Part",
                Quantity = 5,
                CustomerRequirementId = 6,
                RowCnt = 4
                }
            };
                    var expectedDTOs = new List<BOMDLNDetailDTO>
            {
                new BOMDLNDetailDTO
                {
                    BOMId = 2,
                    BOMName = "BOMNAME",
                    BOMCode = "BOMCODE"
                    // set other properties as needed
                }
            };

            _mockBomPHAS6081DetailAssignRepository.Setup(r => r.SqlQueryRawAsync(It.IsAny<string>(), It.IsAny<object[]>()))
                .ReturnsAsync(entities);

            _mockMapper.Setup(m => m.Map<IEnumerable<BOMDLNDetailDTO>>(entities))
                .Returns(expectedDTOs);

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.Success.Should().BeTrue();
            result.Data.Should().NotBeNull();
            result.Data.Should().HaveCount(1);
        }
        [Fact]
        public async Task Handle_WithMyPageSize_UpdatesPageSizeCorrectly()
        {
            // Arrange
            var query = new BOMDLNDetailQuery
            {
                IsPOHub = true,
                IsAssignToMe = 1,
                PageSize = 10,
                MyPageSize = 25,
                ClientId = 123
            };

            var entities = new List<BOMDLNDetailModel>();
            var expectedDTOs = new List<BOMDLNDetailDTO>();

            _mockRepository.Setup(r => r.SqlQueryRawAsync(It.IsAny<string>(), It.IsAny<object[]>()))
                .ReturnsAsync(entities);

            _mockMapper.Setup(m => m.Map<IEnumerable<BOMDLNDetailDTO>>(It.IsAny<IEnumerable<BOMDLNDetailModel>>()))
                .Returns(expectedDTOs);

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            query.PageSize.Should().Be(25);
        }

        [Fact]
        public async Task Handle_WhenRepositoryThrowsException_PropagatesException()
        {
            // Arrange
            var query = new BOMDLNDetailQuery { ClientId = 123 };
            var expectedException = new Exception("Database error");

            _mockRepository.Setup(r => r.SqlQueryRawAsync(It.IsAny<string>(), It.IsAny<object[]>()))
                .ThrowsAsync(expectedException);

            // Act & Assert
            var thrownException = await Assert.ThrowsAsync<Exception>(() =>
                _handler.Handle(query, CancellationToken.None));

            thrownException.Should().Be(expectedException);
        }

        [Fact]
        public async Task Handle_WhenMapperThrowsException_PropagatesException()
        {
            // Arrange
            var query = new BOMDLNDetailQuery { ClientId = 123 };
            var entities = new List<BOMDLNDetailModel> { new BOMDLNDetailModel() };
            var expectedException = new Exception("Mapping error");

            _mockRepository.Setup(r => r.SqlQueryRawAsync(It.IsAny<string>(), It.IsAny<object[]>()))
                .ReturnsAsync(entities);

            _mockMapper.Setup(m => m.Map<IEnumerable<BOMDLNDetailDTO>>(It.IsAny<IEnumerable<BOMDLNDetailModel>>()))
                .Throws(expectedException);

            // Act & Assert
            var thrownException = await Assert.ThrowsAsync<Exception>(() =>
                _handler.Handle(query, CancellationToken.None));

            thrownException.Should().Be(expectedException);
        }
    }
}
