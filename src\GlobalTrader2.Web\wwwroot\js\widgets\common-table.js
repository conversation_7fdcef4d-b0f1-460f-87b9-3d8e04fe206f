﻿$(() => {
    setUpResizeHandlers();
    addEventHandlersToTable();
})
function calculateFirstXRowsHeight(tableId, numberOfRows) {
    const headerHeight = $(`#${tableId} thead tr`).eq(0).height();
    const rowNumbers = $(`#${tableId} tbody tr`).length < numberOfRows ? $(`#${tableId} tbody tr`).length : numberOfRows
    let firstXRowsHeight = 0;

    for (let i = 0; i < rowNumbers; i++) {
        firstXRowsHeight = firstXRowsHeight + $(`#${tableId} tbody tr`).eq(i).height();
    }

    const tableHeight = headerHeight + firstXRowsHeight;

    return tableHeight;
}

function handleTableResize(tableId) {
    const dataTable = $("#" + tableId).DataTable();
    const visibleRowCount = dataTable.page.info().recordsDisplay;
    const calculatedHeight = calculateFirstXRowsHeight(tableId, visibleRowCount);
    const containerHeight = $("#" + tableId).parent().outerHeight();

    if (calculatedHeight > containerHeight && visibleRowCount <= 10) {
        document.querySelectorAll('.resize-handle:not([init-isolated="true"])').forEach((resizeHandle) => {
            const associatedTableId = resizeHandle.getAttribute('data-resize-table');
            if (tableId === associatedTableId) {
                setUpResizeHandler(resizeHandle);
            }
        });
    }
}

function setUpResizeHandlers() {
    const resizeHandles = document.querySelectorAll('.resize-handle:not([init-isolated="true"])');

    resizeHandles.forEach((e) => setUpResizeHandler(e));
}

function setUpResizeHandler(element, numberOfRowToShow) {
    const tableId = element.getAttribute('data-resize-table');
    let tableHeight = element.getAttribute('data-table-height');
    if (!tableHeight) {//if data-table-height is not set, then calculate dynamically
        tableHeight = calculateFirstXRowsHeight(tableId, !numberOfRowToShow ? 10 : numberOfRowToShow);
    }

    const table = $(`#${tableId}`);
    const thead = $(`#${tableId} thead`);
    const tableContainer = table.parent();
    const tableContainerParent = tableContainer.parent();

    let startY, startHeight;

    // Add the resize handle to the table
    $(element).insertAfter(tableContainer);

    // Set style for the table container
    tableContainer.css('position', 'relative');
    tableContainer.css('overflow-y', 'auto');

    // Set style for the table container parent
    tableContainerParent.css('flex-direction', 'column');

    // Set height of container to be the same as the header height
    tableContainer.css('min-height', thead.height() + 'px');

    // Set height for container to the height get from resizeHandle
    tableContainer.css('height', tableHeight + 'px');

    // Handle the mousedown event on the resize handle
    $(element).on('mousedown', function (e) {
        e.preventDefault(); // Prevent default action
        startY = e.pageY;
        startHeight = tableContainer.height();
        $(document).on('mousemove', mouseMoveHandler);
        $(document).on('mouseup', mouseUpHandler);
    });
    function mouseMoveHandler(e) {
        const newHeight = Math.max(startHeight + (e.pageY - startY), thead.height());
        tableContainer.css('height', newHeight + 'px');
    }

    // Remove event listeners when mouse is released
    function mouseUpHandler() {
        $(document).off('mousemove', mouseMoveHandler);
        $(document).off('mouseup', mouseUpHandler);
    }

}

function addEventHandlersToTable() {
    $(document).on('mouseenter', 'table tbody tr', function () {
        $(this).addClass('hover-row');
    });

    $(document).on('mouseleave', 'table tbody tr', function () {
        $(this).removeClass('hover-row');
    });

    $(document).on('click', 'table tbody tr', function (e) {
        $(e.target).parents('table').children('.selected-row').removeClass('selected-row')
        $(this).addClass("selected-row");
    });
}

function setUpTableAccessibility(tableSelector) {
    const table = $(tableSelector);

    table.on("keydown", "tr", function (e) {
        if (e.key === "Enter") {
            e.preventDefault();
            const $row = $(e.currentTarget);

            if (!$row.hasClass("selected")) {
                $row.addClass("selected");
                table.DataTable().row($row).select();
            }
        }
    });

    table.find("tbody tr").each(function () {
        $(this).attr("tabindex", "0");
    });
}   

function adjustHeightResizeDatatable(tableSelector) {
    const table = $(tableSelector);

    let totalHeight = 0;
    $(tableSelector).find('tr').slice(0, 10 + 1).each(function () {
        totalHeight += $(this).outerHeight(true); // true includes margin
    });

    table.parent("div.dt-layout-cell.dt-layout-full").css('height', `${totalHeight}px`)
}
function customAdjustHeightResizeDatatable(tableSelector, totalRows) {
    const table = $(tableSelector);

    let totalHeight = 0;
    $(tableSelector).find('tr').slice(0, totalRows + 1).each(function () {
        totalHeight += $(this).outerHeight(true); // true includes margin
    });

    table.parent("div.dt-layout-cell.dt-layout-full").css('height', `${totalHeight}px`)
}