import { EventEmitter } from '../../../../../../../components/base/event-emmiter.js?v=#{BuildVersion}#'
import { SearchSelectComponent } from '../../../../../../../components/search-select/search-select.component.js?v=#{BuildVersion}#';
import { ECCNCodeSearchSelectComponent } from './search-select/eccn-code-search-select.component.js?v=#{BuildVersion}#'
import { ManufactureSearchSelectComponent } from './search-select/manufacturer-search-select.component.js?v=#{BuildVersion}#'
import { ProductSearchSelectComponent } from './search-select/product-search-select.component.js?v=#{BuildVersion}#'
import { PartNoSearchSelectComponent } from './search-select/part-no-search-select.component.js?v=#{BuildVersion}#'
import { LinesTabService } from '../lines.service.js?v=#{BuildVersion}#';
import { FormBase } from './base/form-base.js';
export class EditSoLineComponent extends FormBase {
    constructor(soGeneralInfo) {
        super();
        this.$dialog = $('#edit-sales-order-line-dialog');
        this.$form = $('#edit-sales-order-line-form');
        this.$title = this.$dialog.find('.dialog-title');
        this.eventEmitter = new EventEmitter();

        this._soGeneralInfo = soGeneralInfo;
        this._currency = {
            cost: $('#edit-sales-order-line-cost-currency'),
            price: $('#edit-sales-order-line-price-currency'),
        }

        this._datepicker = {
            datePromised: $('#edit-sales-order-line-date-promised'),
            dateRequired: $('#edit-sales-order-line-date-required-input'),
            poDeliveryDate: $('#edit-sales-order-line-po-delivery-date'),
        };
        this._service = {
            service: $('#edit-sales-order-line-service'),
            description: $('#edit-sales-order-line-service-description'),
            cost: $('#edit-sales-order-line-cost-input'),
        };

        this._nonService = {
            rohs: $('#edit-sales-order-line-rohs'),
            dateCode: $('#edit-sales-order-line-date-code'),
            quantityShipped: $('#edit-sales-order-line-qty-shipped'),
            quantityAllocated: $('#edit-sales-order-line-qty-allocated'),
            quantityBackOrder: $('#edit-sales-order-line-qty-backorder'),
            manufacturer: $('#edit-sales-order-line-manufacturer-search-select'),
            product: $('#edit-sales-order-line-product-search-select'),
            package: $('#edit-sales-order-line-package-search-select'),
            isShipAsap: $('#edit-sales-order-line-ship-asap'),
            shippingInstructions: $('#edit-sales-order-line-shipping-instructions'),
            customerPartNo: $('#edit-sales-order-line-cust-part-no'),
            partNo: $('#edit-sales-order-line-part-no-search-select'),
            msl: $('#edit-sales-order-line-msl'),
            contractNo: $('#edit-sales-order-line-contract-no'),
            as6081: $('#edit-sales-order-line-inhouse-as6081-testing'),
            printHazWar: $('#edit-sales-order-line-print-hazardous-warning'),
            productSource: $('#edit-sales-order-line-product-source'),
            eccnCode: $('#edit-sales-order-line-eccn-code-search-select'),
        };

        this._dateRequired = {
            input: $('#edit-sales-order-line-date-required-input'),
            span: $('#edit-sales-order-line-date-required'),
        };

        this._price = {
            input: $('#edit-sales-order-line-price-input'),
            span: $('#edit-sales-order-line-price'),
        };

        this._quantity = {
            input: $('#edit-sales-order-line-quantity-input'),
            span: $('#edit-sales-order-line-quantity'),
        };

        this._partNo = {
            input: $('#edit-sales-order-line-part-no-search-select'),
            hiddenInput: $('#edit-sales-order-line-part-no-search-select-value'),
            span: $('#edit-sales-order-line-part-no'),
        };
        this._partNoSearchSelect = null;

        this._eccnCode = {
            input: $('#edit-sales-order-line-eccn-code-search-select'),
            hiddenInput: $('#edit-sales-order-line-eccn-code-search-select-value'),
        }
        this._eccnCodeSearchSelect = null;

        this._manufacturer = {
            input: $('#edit-sales-order-line-manufacturer-search-select'),
            hiddenInput: $('#edit-sales-order-line-manufacturer-search-select-value'),
        }
        this._manufacturerSearchSelect = null;

        this._product = {
            input: $('#edit-sales-order-line-product-search-select'),
            hiddenInput: $('#edit-sales-order-line-product-search-select-value'),
        }
        this._productSearchSelect = null;

        this._package = {
            input: $('#edit-sales-order-line-package-search-select'),
            hiddenInput: $('#edit-sales-order-line-package-search-select-value'),
        }
        this._packageSearchSelect = null;

        this._$rohs = $('#edit-sales-order-line-rohs');
        this._$promiseReason = $('#edit-sales-order-line-promise-reason');
        this._$productSource = $('#edit-sales-order-line-product-source');
        this._$msl = $('#edit-sales-order-line-msl');
        this._$poDeliveryDate = $('#edit-sales-order-line-po-delivery-date');
        this._$lineNotes = $('#edit-sales-order-line-printed-notes');
        this._$customerName = $('#edit-sales-order-line-customer-name');
        this._$salesOrderNumber = $('#edit-sales-order-line-sales-order-number');
        this._$eccnCodeError = $('#edit-sales-order-line-eccn-code-error');

        this._saveChangedMessage = window.localizedStrings.saveChangedMessage;
        this._save = window.localizedStrings.save;
        this._cancel = window.localizedStrings.cancel;
        this._isDialogInit = false;
        this._isRestrictedEdit = false;
        this._isFromHub = false;
        this._isIPOCreated = false;
        this._isProductHazardous = false;
        this._previousPromiseDate = null;
        this._promiseReasonNo = null;
        this._quantityShipped = 0;
        this._previousFormData = null;
        this._previousEccnCodeLabel = null;
        this._eccnCodeLabel = null;
        this._blankEccnCodeLabel = null;
        this._email = null;
        this._detailsData = null;
        this._salesOrderLineId = null;
        this._isService = null;
        this._isAS9120 = null;
        this._poECCNCode = null;
    }

    init() {
        if (this.$dialog.length === 0) {
            return;
        }

        if (this._isDialogInit) {
            return;
        }

        this._initDialog();
        this._initFields();
        this._setupBehaviors();
        this._setupFormValidation();
        this._isDialogInit = true;
    }

    async open(selectedRow, detailsData, mainInfoSalesOrderData) {
        if (!selectedRow) {
            return;
        }
        if (selectedRow.serviceNo > 0) {
            this.$title.text(`${editSoLineLocalizedStrings.serviceTitle}`);
        }
        else {
            this.$title.text(`${editSoLineLocalizedStrings.nonServiceTitle}`);
        }
        this.$dialog.parent().find(`.ui-dialog-buttonset button:has(img[alt="${window.localizedStrings.save}"])`).prop('disabled', true);
        this.$dialog.dialog("open");
        this.$dialog.dialog("setLoading", true);

        await this._onFormShown(selectedRow, detailsData, mainInfoSalesOrderData);
        
        this.$dialog.parent().find(`.ui-dialog-buttonset button:has(img[alt="${window.localizedStrings.save}"])`).prop('disabled', false);
        this.$dialog.dialog("setLoading", false);
        this.$dialog[0].scrollTop = 0;
        if (this._isService) {
            this._service.description.focus();
        }
        else if (!this._isRestrictedEdit) {
            this._partNo.input.focus();
        }
        else {
            this._nonService.rohs.focus();
        }
    }

    _initDialog() {
        this.$dialog.dialog({
            autoOpen: false,
            height: "auto",
            width: "35vw",
            maxHeight: $(window).height(),
            modal: true,
            buttons: [
                {
                    id: "save-btn",
                    text: this._save,
                    class: 'btn btn-primary',
                    html: `<img src="/img/icons/save.svg" alt="${this._save}"/>${this._save}`,
                    click: async () => {
                        if (this.$form.valid()) {
                            this.$dialog.parent().find(`.ui-dialog-buttonset button:has(img[alt="${window.localizedStrings.save}"])`).prop('disabled', true);
                            this.$dialog.find(".form-error-summary").hide();

                            const response = await this._submitForm();
                            this.$dialog.dialog("setLoading", false);
                            this.$dialog.parent().find(`.ui-dialog-buttonset button:has(img[alt="${window.localizedStrings.save}"])`).prop('disabled', false);

                            if (response?.success) {
                                showToast('success', window.localizedStrings.saveChangedMessage);

                                if (this._eccnCodeLabel == null) {
                                    this._eccnCodeLabel = this._blankEccnCodeLabel;
                                }
                                this.eventEmitter.trigger("saveSuccess", this._salesOrderLineId, this._eccnCodeLabel !== this._previousEccnCodeLabel);
                                this.$dialog.dialog("close");
                            } else {
                                showToast('error', response?.title);
                            }
                        } else {
                            $(".form-error-summary").show();
                        }
                    }
                },
                {
                    text: this._cancel,
                    class: 'btn btn-danger',
                    html: `<img src="/img/icons/slash.svg" id="cancel-btn" alt="${this._cancel}" />${this._cancel}`,
                    click: () => {
                        this.$dialog.dialog("close");
                    }
                }
            ],
            open: () => {
                this.$dialog.find('.ui-dialog-titlebar-close').remove();
            },
            close: () => {
                this._onFormClose();
            }
        });
    }

    _initFields() {
        // Date Picker
        this.$form.find('.datepicker').each((index, element) => {
            $(element).datepicker2();
        });

        // AutoSearch
        this._partNoSearchSelect = new PartNoSearchSelectComponent(this._partNo.input[0].id, this._partNo.hiddenInput[0].id, 'single', 'keyword', '/auto-search/part-no', 3);
        this._eccnCodeSearchSelect = new ECCNCodeSearchSelectComponent(this._eccnCode.input[0].id, this._eccnCode.hiddenInput[0].id, 'single', 'keyword', '/orders/customer-requirements/auto-search-part-eccn', 2);
        this._manufacturerSearchSelect = new ManufactureSearchSelectComponent(this._manufacturer.input[0].id, this._manufacturer.hiddenInput[0].id, 'single', 'keyword', '/manufacturers/auto-search', 2);
        this._productSearchSelect = new ProductSearchSelectComponent(this._product.input[0].id, this._product.hiddenInput[0].id, 'single', 'keyword', '/products/auto-search', 2);
        this._packageSearchSelect = new SearchSelectComponent(this._package.input[0].id, this._package.hiddenInput[0].id, 'single', 'keyword', '/packages/auto-search', 2);
        // Dropdown
        this._$rohs.dropdown({
            serverside: false,
            endpoint: '/lists/rohs-statuses',
            textKey: 'description',
            valueKey: 'rohsStatusId',
            placeholderValue: "",
        });

        this._$promiseReason.dropdown({
            serverside: false,
            endpoint: '/lists/promise-reasons',
            params: {
                section: 'PS'
            },
            placeholderValue: "",
        });

        this._$productSource.dropdown({
            serverside: false,
            endpoint: '/lists/product-sources',
            placeholderValue: "",
        });

        this._$msl.dropdown({
            serverside: false,
            endpoint: '/lists/msls',
            valueKey: 'name',
            placeholderValue: "",
        });
    }

    async _submitForm() {
        const data = GlobalTrader.FormHelper.convertFormDataToOject(this.$form);

        if (this._isService) {
            data['custPartNo'] = data['serviceDescription'];
        }
        data['isReasonChanged'] = this._previousPromiseDate !== this._datepicker.datePromised.val();
        data['isFormChanged'] = !this._isObjectEqual(this._previousFormData, data);
        const extraPromiseReasonData = this._$promiseReason.dropdown('getSelectedExtraData');
        data['email'] = extraPromiseReasonData?.email ?? null;
        data['eccnCodeLabel'] = this._eccnCodeLabel;
        data['promiseReasonString'] = extraPromiseReasonData?.name ?? null;
        data['salesOrderNumber'] = this._detailsData.salesOrderNumber;
        data['previousPromiseDate'] = this._previousPromiseDate;
        data['salesOrderId'] = this._soGeneralInfo.SalesOrderId;
        data['soSerialNo'] = this._detailsData.soSerialNo;
        data['inactive'] = this._detailsData.inactive;
        data['taxable'] = this._detailsData.taxable;

        this.$dialog.dialog("setLoading", true);
        const header = { "RequestVerificationToken": this.$form.find(':input[name="__RequestVerificationToken"]').val() }
        return await LinesTabService.updateSalesOrderLineAsync(this._salesOrderLineId, data, header);
    }

    _onFormClose() {
        this.$dialog.dialog("close");
        this.$form[0].reset();
        this.$form.validate().resetForm();
        this.$dialog.find(".form-error-summary").hide();
        this.$form.find('.is-invalid').removeClass("is-invalid");

        this._partNoSearchSelect.resetSearchSelect(false);
        this._manufacturerSearchSelect.resetSearchSelect(false);
        this._eccnCodeSearchSelect.resetSearchSelect(false);
        this._productSearchSelect.resetSearchSelect(false);
        this._packageSearchSelect.resetSearchSelect(false);
    }

    async _onFormShown(selectedRow, detailsData, mainInfoSalesOrderData) {
        this._isRestrictedEdit = selectedRow.isPosted || (selectedRow.isAllocated && selectedRow.serviceNo <= 0);
        this._isFromHub = selectedRow.isIPO;
        this._promiseReasonNo = selectedRow.promiseReasonNo;
        this._quantityShipped = selectedRow.shipped;
        this._salesOrderLineId = selectedRow.lineId;
        this._isService = selectedRow.serviceNo > 0;
        this._isAS9120 = this._soGeneralInfo.IsAS9120;

        this._detailsData = detailsData;
        this._previousEccnCodeLabel = detailsData.eccnCode;
        this._previousPromiseDate = detailsData.datePromised;
        this._isProductHazardous = detailsData.isProdHaz;
        this._isIPOCreated = detailsData.isIpoExist;
        this._blankEccnCodeLabel = detailsData.blankECCNCode;
        this._poECCNCode = detailsData.poECCNCode;

        this._showFieldsBasedOnConditions(selectedRow);
        this._bindFields(selectedRow, detailsData, mainInfoSalesOrderData);
        this._previousFormData = GlobalTrader.FormHelper.convertFormDataToOject(this.$form);
    }

    _showFieldsBasedOnConditions(selectedRow) {
        const isService = selectedRow.serviceNo > 0;
        Object.entries(this._service).forEach(([key, $element]) => {
            this._showField($element, isService);
        });
        Object.entries(this._nonService).forEach(([key, $element]) => {
            this._showField($element, !isService);
        });

        this._showField(this._partNo.input, !isService && !this._isRestrictedEdit);
        this._showField(this._partNo.span, this._isRestrictedEdit);

        this._showField(this._price.input, !this._isRestrictedEdit);
        this._showField(this._price.span, this._isRestrictedEdit);

        this._showField(this._quantity.input, !this._isRestrictedEdit);
        this._showField(this._quantity.span, this._isRestrictedEdit);

        this._showField(this._dateRequired.input, !(this._isRestrictedEdit && !salesOrderLinesSection.canEditDateRequired));
        this._showField(this._dateRequired.span, this._isRestrictedEdit && !salesOrderLinesSection.canEditDateRequired);

        this._showField(this._nonService.productSource, this._isAS9120 && !isService);
        this._showField(this._$poDeliveryDate, this._isFromHub && !this._isIPOCreated);
        this._showField(this._$promiseReason, this._promiseReasonNo > 0);

        this._enableCheckBox(this._nonService.printHazWar, this._isProductHazardous);
    }

    async _bindFields(selectedRow, detailsData, mainInfoSalesOrderData) {
        const isService = selectedRow.serviceNo > 0;
        if (isService) {
            this._setFieldValue(this._service.cost, detailsData.cost);
            this._setFieldValue(this._currency.cost, detailsData.clientCurrency);
        }
        this._setFieldValue(this._quantity.input, detailsData.quantity);
        this._setFieldValue(this._quantity.span, detailsData.quantity);
        this._setFieldValue(this._nonService.quantityShipped, detailsData.shipped);
        this._setFieldValue(this._nonService.quantityAllocated, detailsData.allocated);
        this._setFieldValue(this._nonService.quantityBackOrder, detailsData.backOrder);
        this._setFieldValue(this._$customerName, mainInfoSalesOrderData.customerName);
        this._setFieldValue(this._$salesOrderNumber, mainInfoSalesOrderData.salesOrderNumber);
        this._setFieldValue(this._currency.price, mainInfoSalesOrderData.currencyCode);
        this._setFieldValue(this._service.service, GlobalTrader.StringHelper.setCleanTextValue(detailsData.part));
        this._setFieldValue(this._service.description, GlobalTrader.StringHelper.setCleanTextValue(detailsData.customerPart));
        this._setFieldValue(this._price.input, detailsData.priceVal);
        this._setFieldValue(this._price.span, detailsData.priceVal + ` ${mainInfoSalesOrderData.currencyCode}`);
        this._setFieldValue(this._nonService.customerPartNo, GlobalTrader.StringHelper.setCleanTextValue(detailsData.customerPart));
        this._setFieldValue(this._nonService.dateCode, GlobalTrader.StringHelper.setCleanTextValue(detailsData.dateCd));
        this._setFieldValue(this._nonService.isShipAsap, detailsData.shipASAP);
        this._setFieldValue(this._nonService.shippingInstructions, GlobalTrader.StringHelper.replaceBRTags((detailsData.instructions)));
        this._setFieldValue(this._nonService.rohs, detailsData.rohs);
        this._setFieldValue(this._$lineNotes, GlobalTrader.StringHelper.replaceBRTags(detailsData.lineNotes));
        this._setFieldValue(this._nonService.productSource, detailsData.productSource);
        this._setFieldValue(this._nonService.contractNo, detailsData.contractNo);
        this._setFieldValue(this._nonService.msl, detailsData.mslLevel);
        this._setFieldValue(this._nonService.printHazWar, detailsData.isPrintHaz);
        this._nonService.as6081.text(detailsData.aS6081 ? 'Yes' : 'No');
        this._setFieldValue(this._datepicker.datePromised, detailsData.datePromisedRawValue);
        this._setFieldValue(this._datepicker.dateRequired, detailsData.dateRequiredRawValue);
        this._setFieldValue(this._dateRequired.span, detailsData.requiredDate);
        this._setFieldValue(this._datepicker.poDeliveryDate, detailsData.poDelDate);
        this._setFieldValue(this._$promiseReason, this._promiseReasonNo);

        this._setFieldValue(this._partNo.span, detailsData.part);
        if (this._checkExistedValue(detailsData.part)) {
            this._setFieldValue(this._partNoSearchSelect, {
                value: detailsData.part,
                label: detailsData.part
            });
        }

        if (this._checkExistedValue(detailsData.mfr) && this._checkExistedValue(detailsData.mfrNo)) {
            this._setFieldValue(this._manufacturerSearchSelect, {
                label: GlobalTrader.StringHelper.setCleanTextValue(detailsData.mfr),
                value: detailsData.mfrNo
            });
        }
        if (this._checkExistedValue(detailsData.product) && this._checkExistedValue(detailsData.productNo)) {
            this._setFieldValue(this._productSearchSelect, {
                label: GlobalTrader.StringHelper.setCleanTextValue(detailsData.product),
                value: detailsData.productNo
            });
        }
        let eccnList;
        eccnList = await this._getIHSEccnCodeByEccnCodeAsync(detailsData.eccnCode);
        if (eccnList != null && eccnList.length > 0) {
            const eccnCodeData = eccnList[0];
            if (eccnCodeData.eccnNo > 0) {
                eccnCodeData['label'] = eccnCodeData.eccnCode;
                eccnCodeData['value'] = eccnCodeData.eccnNo;
                this._setFieldValue(this._eccnCodeSearchSelect, eccnCodeData);
            }
        }

        if (this._checkExistedValue(detailsData.package) && this._checkExistedValue(detailsData.packageNo)) {
            this._setFieldValue(this._packageSearchSelect, {
                label: GlobalTrader.StringHelper.setCleanTextValue(detailsData.package),
                value: detailsData.packageNo
            });
        }
    }


    _setupBehaviors() {
        allowPositiveIntegerInput(`#${this._quantity.input[0].id}`);
        this._quantity.input.on('input', (e) => {
            const value = e.target.value;
            if (parseInt(value) >= parseInt(window.constants.maxInt32Value)) {
                e.target.value = window.constants.maxInt32Value;
            }
        });

        allowPositiveDecimalInput(`#${this._service.cost[0].id}`, true, 5);
        allowPositiveDecimalInput(`#${this._price.input[0].id}`, true, 5);

        this._partNo.input.on('input', (e) => {
            $(e.target).val(e.target.value.toUpperCase());
        });

        this._partNo.input.on('blur', async (e) => {
            const partNoValue = this._partNo.hiddenInput.val().trim();
            if (partNoValue.length > 0) {
                const eccnCodeList = await this._getIHSEccnCodeByPartNoAsync(partNoValue);
                if (eccnCodeList?.length > 0) {
                    const eccnCodeData = eccnCodeList[0];
                    eccnCodeData['label'] = eccnCodeData.eccnCode;
                    eccnCodeData['value'] = eccnCodeData.eccnNo;
                    this._setFieldValue(this._eccnCodeSearchSelect, eccnCodeData);
                }
            }
        });

        this._productSearchSelect.on("selectItem", (data) => {
            this._nonService.printHazWar.prop('checked', false);
            this._nonService.printHazWar.prop('disabled', !data.isHazarders);
        });

        this._eccnCodeSearchSelect.on("selectItem", (data) => {
            this._eccnCodeLabel = data.label;
            if (this._poECCNCode !== this._eccnCodeLabel) {
                this._$eccnCodeError.show();
            }
            else {
                this._$eccnCodeError.hide();
            }
        });

        this._eccnCodeSearchSelect.on("removeItem", (data) => {
            this._eccnCodeLabel = null;
            this._$eccnCodeError.hide();
        });

        this._datepicker.datePromised.on('change', (event) => {
            if (this._promiseReasonNo == 0) {
                this._showField(this._$promiseReason, this._previousPromiseDate != this._datepicker.datePromised.val());
            }
            this._$promiseReason.dropdown('reset');
        });
    }

    _setupFormValidation() {
        const self = this;
        const promiseDateMessage = salesOrderLinesSection.canEditPromiseDateAfterCheck ? localizedStrings.promiseNotInRangeOfPromiseMonth : localizedStrings.promiseNotInRangeOfThisMonth;

        $.validator.addMethod("promiseDateRange", function (value, element, param) {
            if (this.optional(element)) {
                return true;
            }
            if (self._soGeneralInfo.IsSoAuthorised && !salesOrderLinesSection.isAutoAuthorizeSo) {
                const currentDate = new Date();
                currentDate.setHours(0, 0, 0, 0);

                let promiseDate;
                const dateStr = self._datepicker.datePromised.val();
                if (dateStr) {
                    const [day, month, year] = dateStr.split('/').map(num => parseInt(num, 10));
                    promiseDate = new Date(year, month - 1, day);
                    promiseDate.setHours(0, 0, 0, 0);
                } else {
                    // Handle empty date case
                    return false;
                }

                // Check if date is valid
                if (isNaN(promiseDate.getTime())) {
                    return false;
                }

                if (promiseDate < currentDate) {
                    return false;
                }

                // Ensure the date is not before today
                if (!salesOrderLinesSection.canEditPromiseDateAfterCheck) {
                    // Date must be within current month
                    const lastDayOfMonth = new Date(
                        currentDate.getFullYear(),
                        currentDate.getMonth() + 1,
                        0
                    );
                    return promiseDate <= lastDayOfMonth;
                } else {
                    // Date must be within the month of the promise date
                    const lastDayOfPromiseMonth = new Date(
                        promiseDate.getFullYear(),
                        promiseDate.getMonth() + 1,
                        0
                    );

                    return promiseDate <= lastDayOfPromiseMonth;
                }
            }
            return true;
        }, promiseDateMessage);

        $.validator.addMethod("quantityCannotBeLessThanQuantityShipped", function (value, element, param) {
            return this.optional(element) || !(this._quantityShipped > value);
        }, localizedStrings.orderQuantityCannotBeLessThanShippedQuantity);

        $.validator.addMethod("restrictedManufacturer", function (value, element, param) {
            return !(self._manufacturerSearchSelect.isRestrictedManufacturer);
        }, '');

        this.$form.validate({
            ignore: [],
            invalidHandler: (event, validator) => {
                setTimeout(() => {
                    self._scrollToFirstInvalidField();
                }, 50);
            },
            rules: {
                partNo: {
                    required: {
                        depends: (element) => {
                            return !self._isService && !self._isRestrictedEdit;
                        }
                    },
                },
                quantity: {
                    required: {
                        depends: (element) => {
                            return !self._isRestrictedEdit;
                        }
                    },
                    min: {
                        param: 1,
                        depends: (element) => {
                            return !self._isRestrictedEdit;
                        }
                    },
                    quantityCannotBeLessThanQuantityShipped: {
                        depends: (element) => {
                            return !self._isRestrictedEdit;
                        }
                    },
                },
                cost: {
                    required: {
                        depends: (element) => {
                            return self._isService;
                        }
                    },
                    max: 2000000000,
                },
                price: {
                    required: {
                        depends: (element) => {
                            return !self._isRestrictedEdit;
                        }
                    },
                    max: 2000000000,
                },
                product: {
                    required: {
                        depends: (element) => {
                            return !self._isService;
                        }
                    },
                },
                datePromised: {
                    required: true,
                    promiseDateRange: true
                },
                promiseReason: {
                    required: {
                        depends: (element) => {
                            return self._previousPromiseDate != self._datepicker.datePromised.val();
                        }
                    },
                },
                dateRequired: {
                    required: {
                        depends: (element) => {
                            return !(self._isRestrictedEdit && !salesOrderLinesSection.canEditDateRequired);
                        }
                    },
                },
                productSource: {
                    required: {
                        depends: (element) => {
                            return self._isAS9120 && !self._isService;
                        }
                    },
                },
                poDeliveryDate: {
                    required: {
                        depends: (element) => {
                            return self._isFromHub;
                        }
                    }
                },
                msl: {
                    required: {
                        depends: (element) => {
                            return !self._isService;
                        }
                    },
                },
                manufacturer: {
                    restrictedManufacturer: true
                },
            },
            highlight: (element) => {
                const inputName = $(element).attr("name");
                const searchSelectNames = ['partNo', 'eccnCode', 'product', 'package'];

                if (searchSelectNames.includes(inputName)) {
                    this[`_${inputName}SearchSelect`].displaySearchSelectErrorBorder();
                } else if (inputName === 'manufacturer') {
                    $(element).parent().addClass("is-invalid");
                }
                else {
                    $(element).addClass("is-invalid");
                }
            },
            unhighlight: (element) => {
                const inputName = $(element).attr("name");
                const searchSelectNames = ['partNo', 'eccnCode', 'product', 'package'];

                if (searchSelectNames.includes(inputName)) {
                    this[`_${inputName}SearchSelect`].removeSearchSelectErrorBorder();
                } else if (inputName === 'manufacturer') {
                    $(element).parent().removeClass("is-invalid");
                }
                else {
                    $(element).removeClass("is-invalid");
                }
            },
            errorPlacement: function (error, element) {
                const $element = $(element);
                const inputName = $(element).attr("name");
                const specialNames = ['cost', 'price']

                if ($element.hasClass('datepicker') || $element.hasClass('dropdown') || specialNames.includes(inputName)) {
                    error.insertAfter($element.parent());
                } else if (inputName === 'manufacturer') {
                    return;
                }
                else {
                    error.insertAfter(element); // Default placement
                }
            }
        });
    }


    async _getIHSEccnCodeByEccnCodeAsync(eccnCode) {
        const response = await LinesTabService.getIHSEccnCodeByEccnCodeAsync(eccnCode);
        if (response?.success) {
            return response.data;
        } else {
            return null;
        }
    }

    async _getIHSEccnCodeByPartNoAsync(partNo) {
        const response = await LinesTabService.getIHSEccnCodeByPartNoAsync(partNo);
        if (response?.success) {
            return response.data;
        } else {
            return null;
        }
    }

    _isObjectEqual(obj1, obj2) {
        const changedFieldArray = ['quantity', 'price', 'serviceDescription', 'custPartNo', 'dateCode',
            'product', 'datePromised', 'dateRequired', 'package', 'partNo', 'rohs', 'printedNotes',
            'productSource', 'poDeliveryDate', 'contractNo', 'msl', 'printHazardousWarning'];
        if (obj1 === obj2) return true;
        if (obj1 == null || obj2 == null) return false;
        if (typeof obj1 !== 'object' || typeof obj2 !== 'object') return obj1 === obj2;

        const keys1 = Object.keys(obj1);
        const keys2 = Object.keys(obj2);

        for (let key of keys1) {
            if (!changedFieldArray.includes(key)) continue;
            if (!keys2.includes(key)) return false;
            if (!this._isObjectEqual(obj1[key], obj2[key])) return false;
        }

        return true;
    }

    _scrollToFirstInvalidField() {
        // Find the first invalid field
        const $firstInvalidField = this.$form.find('.is-invalid').first();

        if ($firstInvalidField.length > 0) {
            // Scroll to the invalid field within the dialog
            const dialogScrollTop = this.$dialog[0].scrollTop;
            const fieldOffsetTop = $firstInvalidField.offset().top;
            const dialogOffsetTop = this.$dialog.offset().top;
            const relativeTop = fieldOffsetTop - dialogOffsetTop + dialogScrollTop;

            // Scroll with some padding
            this.$dialog[0].scrollTop = Math.max(0, relativeTop - 50);

            // Focus the field if it's focusable
            if ($firstInvalidField.is('input, select, textarea')) {
                setTimeout(() => {
                    $firstInvalidField.focus();
                }, 100);
            }
        }
    }
}
