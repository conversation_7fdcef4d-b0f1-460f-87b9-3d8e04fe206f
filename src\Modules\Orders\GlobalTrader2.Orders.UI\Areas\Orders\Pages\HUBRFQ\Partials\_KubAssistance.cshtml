﻿@using GlobalTrader2.SharedUI.Interfaces

@inject IWebResourceManager WebResourceManager
@inject IViewLocalizer _localizer

@{
    WebResourceManager.AddScriptModule("/js/modules/orders/hubrfq/details/kub-assistance.js");
}

<div id="kub-assistant-container" class="kub-assistant-container">
    <div class="modal-dialog chat-wrapper d-none m-0">
        <div class="modal-content">
            <div class="modal-header py-1 px-2">
                <div class="modal-title fw-bold">@_localizer["KUB Assistant"]</div>
                <button type="button" class="btn-close" aria-label="Close"></button>
            </div>
            <div class="modal-body p-3 bg-white">
                <div class="kub-scroll-content">
                    <div id="kub-assistant-less-data">
                        <div class="kub-seleted-part-no">@_localizer["Part Number"]: <span id="kub-selected-part-no"></span></div>
                        <div class="row py-1">
                            <div class="col-7 fw-bold border-end"><span class="border-bottom pb-1">@_localizer["Customer Quotes"]</span></div>
                        </div>
                        <div class="row py-1">
                            <div class="col-7 fw-bold border-end">@_localizer["Latest Quote Price to Customer"]</div>
                            <div class="col-5 fw-bold"><span id="kub-latest-quote-price"></span></div>
                        </div>

                        <div id="kub-last-quotes-row" class="row py-1 kub-expandable-row">
                            <div class="col-7 fw-bold border-end">@_localizer["10 Most recent Quotes in the last 12 months (Rolling)"]</div>
                            <div class="col-5 fw-bold"><span id="kub-last-quotes-link" class="kub-view-details">@_localizer["View Details"]</span></div>
                        </div>
                        <div id="kub-last-quotes-content" class="row py-1 kub-detail-container d-none">
                            <table id="kub-last-quotes-table" class="table simple-table display responsive">
                                <thead>
                                    <tr>
                                        <th></th>
                                    </tr>
                                </thead>
                            </table>
                        </div>

                        <div class="row py-1">
                            <div class="col-7 fw-bold border-end">@_localizer["Latest Offer shared by HUB"]</div>
                            <div class="col-5 fw-bold"><span id="kub-latest-offer-by-hub"></span></div>
                        </div>

                        <div class="row py-1">
                            <div class="col-7 fw-bold border-end">@_localizer["Win/Loss Ratio of selected part for selected customer in last 12 months (Rolling)"]</div>
                            <div class="col-5 fw-bold"><span id="kub-win-loss-ratio"></span>&nbsp; @_localizer["Quotes Won"] </div>
                        </div>

                        <div id="kub-last-top-buy-price-6-months-row" class="row py-1 kub-expandable-row d-none">
                            <div class="col-7 fw-bold border-end">@_localizer["Top 3 Buy Price in last 6 months"]</div>
                            <div class="col-5 fw-bold">
                                <span id="kub-last-top-buy-price-6-months-link" class="kub-view-details">View Details</span>
                            </div>
                        </div>
                        <div id="kub-last-top-buy-price-6-months-content" class="row py-1 kub-detail-container d-none">
                            <table id="kub-last-top-buy-price-6-months-table" class="table simple-table display responsive">
                                <thead>
                                    <tr>
                                        <th></th>
                                    </tr>
                                </thead>
                            </table>
                        </div>
                    </div>
                    <div id="kub-assistant-more-data-wrapper" class="d-none">
                        <div class="row py-1">
                            <div class="col-7 fw-bold border-end"><span class="border-bottom pb-1">@_localizer["Requirements"]</span></div>
                        </div>
                        <div class="row py-1">
                            <div class="col-7 fw-bold border-end">@_localizer["Number of Customer Requirements of this part in the last 12 months (Rolling)"]</div>
                            <div class="col-5 fw-bold"><span id="kub-last-number-cus-req"></span> QTY</div>
                        </div>
                        <div id="kub-last-customer-requirements-row" class="row py-1 kub-expandable-row">
                            <div class="col-7 fw-bold border-end">@_localizer["Last 20 HUBRFQ for the selected part"]</div>
                            <div class="col-5 fw-bold">
                                <span id="kub-last-customer-requirements-link" class="kub-view-details">@_localizer["View Details"]</span>
                            </div>
                        </div>
                        <div id="kub-last-customer-requirements-content" class="row py-1 kub-detail-container d-none">
                            <table id="kub-last-customer-requirements-table" class="table simple-table display responsive">
                                <thead>
                                    <tr>
                                        <th></th>
                                    </tr>
                                </thead>
                            </table>
                        </div>
                        <div class="row py-1">
                            <div class="col-7 fw-bold border-end">@_localizer["Lastest HUBRFQ for the selected part"]</div>
                            <div class="col-5 fw-bold"><span id="kub-latest-requirement"></span></div>
                        </div>

                        <div class="row py-1">
                            <div class="col-7 fw-bold border-end"><span class="border-bottom pb-1">@_localizer["Sales"]</span></div>
                        </div>
                        <div class="row py-1">
                            <div class="col-7 fw-bold border-end">@_localizer["Price last invoiced to this Customer for the selected part in the last 12 months (Rolling)"]</div>
                            <div class="col-5 fw-bold"><span id="kub-price-last-invoiced"></span></div>
                        </div>
                        <div class="row py-1">
                            <div class="col-7 fw-bold border-end">@_localizer["Highest sales in price in the last 12 months (Rolling)"]</div>
                            <div class="col-5 fw-bold"><span id="kub-highest-sales"></span></div>
                        </div>
                        <div class="row py-1">
                            <div class="col-7 fw-bold border-end">@_localizer["Lowest sales price in the last 12 months (Rolling)"]</div>
                            <div class="col-5 fw-bold"><span id="kub-lowest-sales"></span></div>
                        </div>
                        <div class="row py-1">
                            <div class="col-7 fw-bold border-end">@_localizer["Number parts invoiced for the last 12 months"]</div>
                            <div class="col-5 fw-bold"><span id="kub-number-parts-invoiced"></span>&nbsp; QTY</div>
                        </div>
                        
                        <div class="row py-1">
                            <div class="col-7 fw-bold border-end"><span class="border-bottom pb-1">@_localizer["API Data"]</span></div>
                        </div>
                        <div class="row py-1">
                            <div class="col-7 fw-bold border-end">@_localizer["IHS Data"]</div>
                            <div class="col-5 fw-bold"><span id="kub-ihs-data"></span></div>
                        </div>
                        <div class="row py-1">
                            <div class="col-7 fw-bold border-end">@_localizer["Lytica Data"]</div>
                            <div class="col-5 fw-bold"><span id="kub-lytica-data"></span></div>
                        </div>

                        <div class="row py-1 stock-row d-none">
                            <div class="col-7 fw-bold border-end"><span class="border-bottom pb-1">@_localizer["Stock"]</span></div>
                        </div>
                        <div id="kub-stock-reverse-logistics-row" class="row py-1 kub-expandable-row stock-row d-none">
                            <div class="col-7 fw-bold border-end">@_localizer["Reverse Logistics 10 most recent offers for selected part"]</div>
                            <div class="col-5 fw-bold">
                                <span id="kub-stock-reverse-logistics-link" class="kub-view-details">View Details</span>
                            </div>
                        </div>
                        <div id="kub-stock-reverse-logistics-content" class="row py-1 kub-detail-container d-none">
                            <table id="kub-stock-reverse-logistics-table" class="table simple-table display responsive">
                                <thead>
                                    <tr>
                                        <th></th>
                                    </tr>
                                </thead>
                            </table>
                        </div>
                        <div id="kub-stock-strategic-stock-row" class="row py-1 kub-expandable-row stock-row d-none">
                            <div class="col-7 fw-bold border-end">@_localizer["Strategic Stock 10 most recent offers for selected part"]</div>
                            <div class="col-5 fw-bold">
                                <span id="kub-stock-strategic-stock-link" class="kub-view-details">View Details</span>
                            </div>
                        </div>
                        <div id="kub-stock-strategic-stock-content" class="row py-1 kub-detail-container d-none">
                            <table id="kub-stock-strategic-stock-table" class="table simple-table display responsive">
                                <thead>
                                    <tr>
                                        <th></th>
                                    </tr>
                                </thead>
                            </table>
                        </div>
                        <div id="kub-stock-20-recent-row" class="row py-1 kub-expandable-row stock-row d-none">
                            <div class="col-7 fw-bold border-end">@_localizer["Stock 20 most recent for selected part"]</div>
                            <div class="col-5 fw-bold">
                                <span id="kub-stock-20-recent-link" class="kub-view-details">View Details</span>
                            </div>
                        </div>
                        <div id="kub-stock-20-recent-content" class="row py-1 kub-detail-container d-none">
                            <table id="kub-stock-20-recent-table" class="table simple-table display responsive">
                                <thead>
                                    <tr>
                                        <th></th>
                                    </tr>
                                </thead>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer p-1">
                <div class="d-flex justify-content-between w-100">
                    <button class="btn btn-primary" id="kub-read-more-less-btn">
                        <img src="~/img/icons/plus.svg" alt="Add icon" width="18" height="18" />
                        <span class="lh-base">@_localizer["Read more"]</span>
                    </button>
                    <em id="kub-last-updated-date"></em>
                </div>
            </div>
        </div>
    </div>
</div>