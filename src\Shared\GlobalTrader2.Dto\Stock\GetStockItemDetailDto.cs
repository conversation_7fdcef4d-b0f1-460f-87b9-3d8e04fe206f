﻿namespace GlobalTrader2.Dto.Stock
{
    public class GetStockItemDetailDto
    {
        public int? StockId { get; set; }
        public string? FullPart { get; set; } = string.Empty;
        public string? Part { get; set; } = string.Empty;
        public int? ManufacturerNo { get; set; }
        public string? DateCode { get; set; } = string.Empty;
        public int? PackageNo { get; set; }
        public int? WarehouseNo { get; set; }
        public int? ClientNo { get; set; }
        public string? QualityControlNotes { get; set; } = string.Empty;
        public int? PurchaseOrderNo { get; set; }
        public int? PurchaseOrderLineNo { get; set; }
        public int? QuantityInStock { get; set; }
        public int? QuantityOnOrder { get; set; }
        public string? Location { get; set; } = string.Empty;
        public int? ProductNo { get; set; }
        public decimal? ResalePrice { get; set; }
        public bool Unavailable { get; set; }
        public int? LotNo { get; set; }
        public decimal? LandedCost { get; set; }
        public string? SupplierPart { get; set; } = string.Empty;
        public byte? ROHS { get; set; }
        public int? PackageUnit { get; set; }
        public int? StockKeepingUnit { get; set; }
        public int? CustomerRMANo { get; set; }
        public int? CustomerRMALineNo { get; set; }
        public int? GoodsInLineNo { get; set; }
        public int? UpdatedBy { get; set; }
        public DateTime DLUP { get; set; }
        public string? FullSupplierPart { get; set; } = string.Empty;
        public int? CountryOfManufacture { get; set; }
        public string? PackageName { get; set; } = string.Empty;
        public string? PackageDescription { get; set; } = string.Empty;
        public string? WarehouseName { get; set; } = string.Empty;
        public string? ProductName { get; set; } = string.Empty;
        public string? ProductDescription { get; set; } = string.Empty;
        public string? ManufacturerName { get; set; } = string.Empty;
        public string? ManufacturerCode { get; set; } = string.Empty;
        public int? PurchaseOrderNumber { get; set; }
        public int? CustomerRMANumber { get; set; }
        public string? SupplierName { get; set; } = string.Empty;
        public string? LotName { get; set; } = string.Empty;
        public string? LotCode { get; set; } = string.Empty;
        public int? SupplierNo { get; set; }
        public int? Buyer { get; set; }
        public string? BuyerName { get; set; } = string.Empty;
        public int? QuantityAllocated { get; set; }
        public int? QuantityAvailable { get; set; }
        public int? GoodsInNo { get; set; }
        public decimal? GoodsInPrice { get; set; }
        public decimal? GoodsInShipInCost { get; set; }
        public int? GoodsInNumber { get; set; }
        public int? GoodsInCurrencyNo { get; set; }
        public DateTime StockDate { get; set; }
        public string? ROHSStatus { get; set; } = string.Empty;
        public string? CountryOfManufactureName { get; set; } = string.Empty;
        public decimal? PurchasePrice { get; set; }
        public string? CurrencyCode { get; set; } = string.Empty;
        public string? PartMarkings { get; set; } = string.Empty;
        public int? CountingMethodNo { get; set; }
        public string? CountingMethodDescription { get; set; } = string.Empty;
        public string? StockStartDate { get; set; } = string.Empty;
        public decimal? OriginalLandedCost { get; set; }
        public DateTime? FirstStockProvisionDate { get; set; }
        public DateTime? LastStockProvisionDate { get; set; }
        public DateTime? ManualStockSplitDate { get; set; }
        public bool? IsManual { get; set; }
        public int? DivisionNo { get; set; }
        public int? InternalPurchaseOrderNumber { get; set; }
        public int? InternalPurchaseOrderId { get; set; }
        public string? IPOSupplierName { get; set; } = string.Empty;
        public int? IPOSupplier { get; set; }
        public decimal? ClientLandedCost { get; set; }
        public decimal? ClientPurchasePrice { get; set; }
        public int? GIQty { get; set; }
        public string? MSLLevel { get; set; } = string.Empty;
        public int? CurrencyNo { get; set; }
        public bool? IsProdHazardous { get; set; }
        public bool? IsOrderViaIPOonly { get; set; }
        public bool? IsRestrictedProduct { get; set; }
        public string? GeneralInspectionNotes { get; set; } = string.Empty;
        public int? ActeoneTestStatus { get; set; }
        public int? IsopropryleStatus { get; set; }
        public string? ActeoneTest { get; set; } = string.Empty;
        public string? Isopropryle { get; set; } = string.Empty;
        public string? HICStatusName { get; set; } = string.Empty;
        public bool? ReqSerialNo { get; set; }
        public bool? IsLotCodesReq { get; set; }
        public int? BakingLevelAdded { get; set; }
        public bool? isLotOnHold { get; set; }
        public bool? IsImport { get; set; }
        public int? ImportSupplierNo { get; set; }
        public string? ImportupplierName { get; set; } = string.Empty;
        public string? importCompanyType { get; set; } = string.Empty;
        public string? CompanyType { get; set; } = string.Empty;
        public decimal? ClientUPLiftPrice { get; set; }
        public bool AS6081 { get; set; }
        public int? StatusNo { get; set; }
        public string? StockLogDetail { get; set; } = string.Empty;
        public string? StockLogChangeNotes { get; set; } = string.Empty;
        public int? StockLogReasonNo { get; set; }
        public bool? UpdateShipments { get; set; }
        public string? DivisionName { get; set; } = string.Empty;
        public int? SalesOrderNo { get; set; }
        public string? CustomerPO { get; set; } = string.Empty;
        public int? CustomerNo { get; set; }
        public string? CustomerName { get; set; } = string.Empty;
        public bool? AllowPurchasePriceView { get; set; }
        public bool? HasSTO { get; set; }
        public string? WarningText { get; set; } = string.Empty;
        public bool IsHasCountryMessage { get; set; }
        public string Price { get; set; } = string.Empty;
        public string LandedCostString { get; set; } = string.Empty;
    }
}
