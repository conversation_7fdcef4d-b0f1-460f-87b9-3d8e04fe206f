﻿@using GlobalTrader2.SharedUI.Models
@using Microsoft.AspNetCore.Mvc.Localization
@model List<BreadCrumbMenuModel>
@inject IViewLocalizer _localizer

@if (Model != null)
{
    @foreach (var menu in Model)
    {
        <div class="breadcrumb-button-wrapper" id="@menu.Id" style="@(menu.Childs.Any() ? "" : "display:none")">
            <div class="breadcrumb-button-options">
                @foreach (var item in menu.Childs)
                {
                    if (item.Title == "SeparateLine")
                    {
                        <div class="transactions-separator"></div>
                    }
                    else
                    {
                        string itemId = !string.IsNullOrEmpty(item.Id)? $"id={item.Id}" : string.Empty;
                        <a @itemId class="breadcrumb-button-option" href="@(string.IsNullOrWhiteSpace(item.CtaUri) ? "#" : item.CtaUri)">@_localizer[item.Title]</a>
                    }
                }
            </div>
            <a class="breadcrumb-button-label h-22" href="@(string.IsNullOrWhiteSpace(menu.CtaUri) ? "#" : menu.CtaUri)" style="text-decoration: none">
                <img src="/img/icons/chevron.svg" alt="chevron" />
                @_localizer[menu.Title]
            </a>
        </div>
    }
}