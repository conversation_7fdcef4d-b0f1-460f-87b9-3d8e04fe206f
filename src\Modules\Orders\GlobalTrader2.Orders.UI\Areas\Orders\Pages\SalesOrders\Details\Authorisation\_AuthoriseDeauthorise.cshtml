@using GlobalTrader2.Dto.SalesOrderLine
@using GlobalTrader2.Orders.UI.Areas.Orders.Pages.SalesOrders.Details
@using GlobalTrader2.SharedUI.Interfaces

@inject IStringLocalizer<GlobalTrader2.SharedUI.CommonResources> _commonLocalizer
@inject IViewLocalizer _localizer
@inject IStringLocalizer<GlobalTrader2.SharedUI.MessageResources> _messageLocalizer


<div class="dialog-container" id="authorise-dialog" title="@_commonLocalizer["Authorisation Information"]" style="display: none;">
	<div class="dialog-description">
		<div class="d-flex justify-content-between">
			<h5 id="authorise-deauthorise-title">@_localizer["AuthoriseTitle"]</h5>
		</div>
		<div class="line"></div>
		<span id="authorise-deauthorise-description"></span>
	</div>
	<div class="form-error-summary" style="display: none;">
		<img src="~/img/icons/x-octagon.svg" alt="Add icon" />
		<div>
			<p>@_messageLocalizer["There were some problems with your form."]</p>
			<p>@_messageLocalizer["Please check below and try again."]</p>
		</div>
	</div>
	<form class="row common-form" id="authorise-deauthorise-form" method="post">
		@Html.AntiForgeryToken()
		<div class="row form-control-wrapper mb-1">
			<div class="col-3">
				<label for="close-sales-order-so-number" class="form-label">
					@_localizer["Sales Order"]
				</label>
			</div>
			<div class="col-9">
				<span data-field="SalesOrderNumber"> </span>
			</div>
		</div>
		<div class="row form-control-wrapper mb-1">
			<div class="col-3">
				<label for="close-sales-order-customer" class="form-label">
					@_localizer["Customer"]
				</label>
			</div>
			<div class="col-9">
				<span data-field="CompanyName"> </span>
			</div>
		</div>
		<div class="row form-control-wrapper mb-1" id="authorise-notify-salesperson-wrapper">
			<div class="col-3">
				<label for="notify-salesperson" class="form-label d-inline-block">
					@_localizer["Notify Salesperson?"]
				</label>
			</div>
			<div class="col-6 d-flex align-items-center gap-2">
				<input type="checkbox" id="authorise-notify-salesperson" name="notify" class="form-control p-0 form-check-input check-sm" checked>
			</div>
		</div>
		<div class="row form-control-wrapper">
			<div class="col-3">
				<label for="comment" class="form-label d-inline-block">
					@_localizer["Comment"]
				</label>
			</div>
			<div class="col-7">
				<textarea id="comment" name="comment" class="form-control form-textarea" rows="4" maxlength="60000" style="height: auto;"></textarea>
			</div>

		</div>
	</form>
</div>
<script>
			const localizedTitles = {
			authoriseTitle: "@_localizer["AuthoriseTitle"]",
			deauthoriseTitle: "@_localizer["DeauthoriseTitle"]",
			onCompanyStopAuthoriseTitle: "@_localizer["OnCompanyStopAuthoriseTitle"]",
			};
</script>
