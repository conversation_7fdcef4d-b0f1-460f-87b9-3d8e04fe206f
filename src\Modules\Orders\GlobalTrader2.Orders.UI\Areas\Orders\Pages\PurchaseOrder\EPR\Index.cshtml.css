﻿table {
    font-size: 12px;
    width: 100%;
}

table th, table td {
    border: 1px #D9D8D8 solid;
    padding: 5px;
}

table th {
    background-color: #D9D8D8;
}

.form-control {
    background-color: #FFFFC2;
}

.form-check-input {
    border: 1px solid var(--border) !important;
    height: 20px !important;
    width: 20px !important;
}

.form-check-input:checked {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23000' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M4.5 8.5l3 3 4.5-6'/%3e%3c/svg%3e") !important;
}

.form-input {
    height: 20px !important;
}

.form-textarea {
    resize: none;
}

.link-button {
    background: none;
    border: none;
    margin: 3px 3px;
    cursor: pointer;
    display: inline;
    color: #0000ff;
    text-decoration: underline;
    padding: 0;
}

.link-button:hover {
    text-decoration: underline;
}
