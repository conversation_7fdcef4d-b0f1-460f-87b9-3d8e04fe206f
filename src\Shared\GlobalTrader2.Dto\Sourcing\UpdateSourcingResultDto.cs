﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GlobalTrader2.Dto.Sourcing
{
    public class UpdateSourcingResultDto
    {
        public int SourcingResultId { get; set; }
        public string? Part { get; set; }
        public int? ManufacturerNo { get; set; }
        public string? DateCode { get; set; }
        public int? ProductNo { get; set; }
        public int? PackageNo { get; set; }
        public int? Quantity { get; set; }
        public double? Price { get; set; }
        public int? OfferStatusNo { get; set; }
        public int? SupplierNo { get; set; }
        public byte? ROHS { get; set; }
        public string? Notes { get; set; }
        public int? UpdatedBy { get; set; }
        public double? SupPrice { get; set; }
        public double? EstimatedShippingCostValue { get; set; }
        public DateTime? DeliveryDate { get; set; }
        public bool? PUHUB { get; set; }
        public string? SPQ { get; set; }
        public string? LeadTime { get; set; }
        public string? ROHSStatus { get; set; }
        public string? FactorySealed { get; set; }
        public string? MSL { get; set; }
        public string? SupplierTotalQSA { get; set; }
        public string? SupplierLTB { get; set; }
        public string? SupplierMOQ { get; set; }
        public int? RegionNo { get; set; }
        public int? CurrencyNo { get; set; }
        public int? LinkMultiCurrencyNo { get; set; }
        public int? MSLLevelNo { get; set; }
        public int? SupplierWarranty { get; set; }
        public bool? IsTestingRecommended { get; set; }
        public int? PriorityNo { get; set; }
        public int? IHSCountryOfOriginNo { get; set; }
        public string? ChangedFields { get; set; }
        public bool? PartWatchMatchHUBIPO { get; set; }
        public int? TypeOfSupplier { get; set; }
        public int? ReasonForSupplier { get; set; }
        public int? RiskOfSupplier { get; set; }
        public int? CountryNo { get; set; }
        public string? SellPriceLessReason { get; set; }
    }
}
