@using GlobalTrader2.SharedUI.Areas.Common.Pages.Shared.Components.SelectionMenu
@using GlobalTrader2.SharedUI.Constants
@using GlobalTrader2.SharedUI.Enums
@using GlobalTrader2.SharedUI.Helper
@using GlobalTrader2.SharedUI.Services
@using GlobalTrader2.SharedUI.ViewModels.Bases
@using Microsoft.AspNetCore.Mvc.Localization
@using Microsoft.Extensions.Localization
@using GlobalTrader2.Dto.SalesOrderLine

@inject IStringLocalizer<GlobalTrader2.SharedUI.CommonResources> _commonLocalizer
@inject IViewLocalizer _localizer
@model ExportApprovalStatusLineViewModel;
@{
    var sectionBoxClasses = HtmlHelperExtensions.GetSectionBoxClass();
    var headerClasses = HtmlHelperExtensions.GetHeaderClass();
    var contentClasses = HtmlHelperExtensions.GetContentClass();
}

<div id="export-approval-status-box" class="@sectionBoxClasses mb-3">
    <h3 class="@headerClasses">
        <span class="section-box-title">
            @_commonLocalizer["Export Approval Status"]
        </span>
        <span class="section-box-button-group" style="display: none">
            <span class="d-flex flex-wrap gap-2">
                @if(Model.CanSelect){
                    <button class="btn btn-primary" id="export-so-line-select-btn" data-select-count="0">
                        <span class="lh-base">
                            <span class="badge bg-white text-primary top-0" id="export-so-line-select-count">0</span> 
                            <span id="export-so-line-select-text"> @_localizer["Select All"]</span>
                        </span>
                    </button>
                }

                @if (Model.CanEdit)
                {
                    <button class="btn btn-primary" id="edit-export-approval-btn" disabled>
                        <img src="~/img/icons/edit-3.svg" alt="@_localizer["Edit Export Approval"]" width="18" height="18" />
                        <span id="edit-export-approval-btn-text" class="lh-base">@_localizer["Edit Export Approval"]</span>
                    </button>
                }
                @if (Model.CanSendApproval)
                {
                    <button class="btn btn-primary" id="send-export-approval-btn" disabled>
                        <img src="~/img/icons/check-circle.svg" alt="@_localizer["Approval"]" width="18" height="18" />
                        <span class="lh-base">@_localizer["Approval"]</span>
                    </button>
                }
                @if (Model.CanRequestApproval)
                {
                    <button class="btn btn-primary" id="request-export-approval-btn" disabled>
                        <img src="~/img/icons/edit-3.svg" alt="@_localizer["Request Approval"]" width="18" height="18" />
                        <span class="lh-base">@_localizer["Request Approval"]</span>
                    </button>
                }
                @if (Model.CanViewPdfWarning)
                {
                    <span id="request-approval-disabled-reason" class="d-none ihspartstatusdoc" textContent="@_localizer["Request Approval Disable Reason"]"
                          width="18" height="18" title="@_localizer["Upload Export Control Document under PDF document"]">
                    </span> 
                }
                @if (Model.CanEdit)
                {
                    <button class="btn btn-primary" id="edit-all-export-approval-btn" disabled>
                        <img src="~/img/icons/edit-3.svg" alt="@_localizer["Edit All Export Approval"]" width="18" height="18" />
                        <span class="lh-base">@_localizer["Edit All Export Approval"]</span>
                    </button>
                }
            </span>
        </span>
    </h3>
    <div class="@contentClasses">
        <div id="export-approval-status-wrapper">
            <div class="d-flex justify-content-between align-items-start border-bottom">
                <div id="nav-tabs-wrapper" role="tab" style="width:100%">
                    <div class="d-flex align-items-center gap-2 alert alert-warning d-none" id="select-error-message-box">
                        <img src="~/img/icons/x-octagon.svg" alt="" width="18" height="18" />
                        <span id="select-error-message" class="text-red fw-bold"></span>
                    </div>
                    <ul class="nav nav-tabs border-0 justify-content-start" id="export-approval-status-tabs">
                        <li class="nav-item">
                            <button id="all-so-line-tab" class="nav-link" data-bs-toggle="tab" data-bs-target="#all-export-sol" type="button" role="tab"
                                    aria-controls="all-export-sol" aria-selected="true" data-view-level="@((int)ExportApprovalStatusTab.All)">
                                @_localizer["All"] 
                            </button>
                        </li>
                        <li class="nav-item">
                            <button id="approved-so-line-tab" class="nav-link" data-bs-toggle="tab" data-bs-target="#approved-export-sol" type="button" role="tab"
                                    aria-controls="approved-export-sol" aria-selected="false" data-view-level="@((int)ExportApprovalStatusTab.Approved)">
                                @_localizer["Approved"] 
                            </button>
                        </li>
                        <li class="nav-item">
                            <button id="waiting-so-line-tab" class="nav-link" data-bs-toggle="tab" data-bs-target="#waiting-export-sol" type="button" role="tab"
                                    aria-controls="waiting-export-sol" aria-selected="false" data-view-level="@((int)ExportApprovalStatusTab.Waiting)">
                                @_localizer["Awaiting"]
                            </button>
                        </li>
                    </ul>
                </div>
            </div>
            <div class="tab-content">
                <div class="tab-pane fade active show" id="all-export-sol" role="tabpanel" aria-labelledby="all-export-sol-tab">
                    <div class="table-container" id="all-export-sol-container">
                        <div id="all-export-sol-box" class="position-relative" data-loading="false">
                            <table id="all-export-sol-table" class="table simple-table display responsive nowrap">
                                <tr>
                                    <th></th>
                                </tr>
                            </table>
                        </div>
                    </div> 
                </div>
                <div class="tab-pane fade" id="approved-export-sol" role="tabpanel" aria-labelledby="approved-export-sol">
                    <div class="table-container" id="approved-export-sol-container">
                        <div id="approved-export-sol-box" class="position-relative" data-loading="false">
                            <table id="approved-export-sol-table" class="table simple-table display responsive nowrap">
                                <thead>
                                    <tr>
                                        <th></th>
                                    </tr>
                                </thead>
                                <tbody></tbody>
                            </table>
                        </div>
                    </div> 
                </div>
                <div class="tab-pane fade" id="waiting-export-sol" role="tabpanel" aria-labelledby="waiting-export-sol">
                    <div class="table-container" id="approved-export-sol-container">
                        <div id="waiting-export-sol-box" class="position-relative" data-loading="false">
                            <table id="waiting-export-sol-table" class="table simple-table display responsive nowrap">
                                <thead>
                                    <tr>
                                        <th></th>
                                    </tr>
                                </thead>
                                <tbody></tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    const exportApprovalStatusLineLocalizer = {
        button : {
            editExportApproval : "@_localizer["Edit Export Approval"]",
            viewExportApproval : "@_localizer["View Export Approval"]"
        },
        errorMessage : {
            exportDetailsNotFilled : "@_localizer["Export Details Not Filled Message"]",
            allocationNotDone : "@_localizer["Allocation Not Done Message"]"
        }
    };
</script>