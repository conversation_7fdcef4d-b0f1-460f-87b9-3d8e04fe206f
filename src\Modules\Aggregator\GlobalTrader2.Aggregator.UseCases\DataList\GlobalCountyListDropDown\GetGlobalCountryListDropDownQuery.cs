﻿using GlobalTrader2.Dto.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GlobalTrader2.Aggregator.UseCases.DataList.GlobalCountyListDropDown
{
    public class GetGlobalCountryListDropDownQuery : IRequest<BaseResponse<List<DropDownDto>>>
    {
        public bool? IncludeSelected {  get; set; }
        public int ? ClientNo { get; set; }
    }
}
