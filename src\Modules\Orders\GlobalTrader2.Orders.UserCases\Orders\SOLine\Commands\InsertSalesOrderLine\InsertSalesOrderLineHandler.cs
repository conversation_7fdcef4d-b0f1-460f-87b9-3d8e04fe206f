namespace GlobalTrader2.Orders.UserCases.Orders.SOLine.Commands.InsertSalesOrderLine
{
    public class InsertSalesOrderLineHandler(IBaseRepository<AffectedRows> _repository)
        : IRequestHandler<InsertSalesOrderLineCommand, BaseResponse<int>>
    {
        public async Task<BaseResponse<int>> <PERSON>le(InsertSalesOrderLineCommand request, CancellationToken cancellationToken)
        {
            var salesOrderLineId = await InsertSalesOrderLine(request);
            if (request.QuoteLineNo > 0 && request.UpdatedBy.HasValue)
            {
                _ = UpdateQuoteClosedStatus(request.QuoteLineNo.Value, request.UpdatedBy.Value);
            }
            return new BaseResponse<int> { Data = salesOrderLineId, Success = true };
        }

        private async Task<int> InsertSalesOrderLine(InsertSalesOrderLineCommand request)
        {
            var output = new SqlParameter("@SalesOrderLineId", SqlDbType.Int) { Direction = ParameterDirection.Output };
            var parameters = new List<SqlParameter>()
            {
                new SqlParameter("@SalesOrderNo", SqlDbType.Int) { Value = request.SalesOrderNo },
                new SqlParameter("@Part", SqlDbType.NVarChar) { Value = request.Part ?? (object)DBNull.Value },
                new SqlParameter("@ManufacturerNO", SqlDbType.Int) { Value = request.ManufacturerNo ?? (object)DBNull.Value },
                new SqlParameter("@DateCode", SqlDbType.NVarChar) { Value = request.DateCode ?? (object)DBNull.Value },
                new SqlParameter("@PackageNo", SqlDbType.Int) { Value = request.PackageNo ?? (object)DBNull.Value },
                new SqlParameter("@Quantity", SqlDbType.Int) { Value = request.Quantity },
                new SqlParameter("@Price", SqlDbType.Float) { Value = request.Price },
                new SqlParameter("@DatePromised", SqlDbType.DateTime) { Value = request.DatePromised },
                new SqlParameter("@RequiredDate", SqlDbType.DateTime) { Value = request.RequiredDate },
                new SqlParameter("@Instructions", SqlDbType.NVarChar) { Value = request.Instructions ?? (object)DBNull.Value },
                new SqlParameter("@ProductNo", SqlDbType.Int) { Value = request.ProductNo ?? (object)DBNull.Value },
                new SqlParameter("@Taxable", SqlDbType.NVarChar) { Value = request.Taxable ?? (object)DBNull.Value },
                new SqlParameter("@CustomerPart", SqlDbType.NVarChar) { Value = request.CustomerPart ?? (object)DBNull.Value },
                new SqlParameter("@Posted", SqlDbType.Bit) { Value = request.Posted },
                new SqlParameter("@ShipASAP", SqlDbType.Bit) { Value = request.ShipASAP },
                new SqlParameter("@ServiceNo", SqlDbType.Int) { Value = request.ServiceNo ?? (object)DBNull.Value },
                new SqlParameter("@StockNo", SqlDbType.Int) { Value = request.StockNo ?? (object)DBNull.Value },
                new SqlParameter("@ROHS", SqlDbType.TinyInt) { Value = request.ROHS ?? (object)DBNull.Value },
                new SqlParameter("@Notes", SqlDbType.NVarChar) { Value = request.Notes ?? (object)DBNull.Value },
                new SqlParameter("@UpdatedBy", SqlDbType.Int) { Value = request.UpdatedBy ?? (object)DBNull.Value },
                new SqlParameter("@QuoteLineNo", SqlDbType.Int) { Value = request.QuoteLineNo ?? (object)DBNull.Value },
                new SqlParameter("@ProductSource", SqlDbType.TinyInt) { Value = request.ProductSource ?? (object)DBNull.Value },
                new SqlParameter("@SourcingResultNo", SqlDbType.Int) { Value = request.SourcingResultNo ?? (object)DBNull.Value },
                 new SqlParameter("@IsCreateSOClone", SqlDbType.Bit) { Value = request.IsCreateSOClone },
                new SqlParameter("@PODeliveryDate", SqlDbType.DateTime) { Value = request.PODeliveryDate ?? (object)DBNull.Value },
                new SqlParameter("@PrintHazardous", SqlDbType.Bit) { Value = request.PrintHazardous },
                new SqlParameter("@MSLLevel", SqlDbType.NVarChar) { Value = request.MSLLevel ?? (object)DBNull.Value },
                new SqlParameter("@ContractNo", SqlDbType.NVarChar) { Value = request.ContractNo ?? (object)DBNull.Value },
                new SqlParameter("@DocId", SqlDbType.Int) { Value = request.DocId ?? (object)DBNull.Value },
                new SqlParameter("@DocType", SqlDbType.Char) { Value = request.DocType ?? (object)DBNull.Value },
                 new SqlParameter("@EI_Required", SqlDbType.Int) { Value = request.EIRequired },
                 new SqlParameter("@EvidenceNotes", SqlDbType.NVarChar) { Value = request.EvidenceNotes ?? (object)DBNull.Value },
                 new SqlParameter("@TestingType", SqlDbType.Int) { Value = request.TestingType ?? (object)DBNull.Value },
                new SqlParameter("@ECCNCode", SqlDbType.VarChar) { Value = request.ECCNCode ?? (object)DBNull.Value },
                new SqlParameter("@ECCNNo", SqlDbType.Int) { Value = request.ECCNNo ?? (object)DBNull.Value },
                new SqlParameter("@AS6081", SqlDbType.Bit) { Value = request.AS6081 },
                new SqlParameter("@ServiceCostRef", SqlDbType.Float) { Value = request.ServiceCostRef ?? (object)DBNull.Value },
                output
            };

            await _repository.ExecuteSqlRawAsync(
                sql: $"{StoredProcedures.Insert_SalesOrderLine} " +
                    "@SalesOrderNo, @Part, @ManufacturerNO, @DateCode, @PackageNo, " +
                    "@Quantity, @Price, @DatePromised, @RequiredDate, @Instructions, " +
                    "@ProductNo, @Taxable, @CustomerPart, @Posted, @ShipASAP, " +
                    "@ServiceNo, @StockNo, @ROHS, @Notes, @UpdatedBy, " +
                    "@QuoteLineNo, @ProductSource, @SourcingResultNo, @IsCreateSOClone, " +
                    "@PODeliveryDate, @PrintHazardous, @MSLLevel, @ContractNo, " +
                    "@SalesOrderLineId OUTPUT, @DocId, @DocType, " +
                    "@EI_Required, @EvidenceNotes, @TestingType, " +
                    "@ECCNCode, @ECCNNo, @AS6081, @ServiceCostRef",
                parameters: parameters.ToArray());

            ArgumentNullException.ThrowIfNull(output.Value);
            int newSalesOrderLineId = (int)output.Value;
            return newSalesOrderLineId;
        }
        
        private async Task UpdateQuoteClosedStatus(int quoteId, int loginId)
        {
            var parameters = new SqlParameter[]
            {
                new SqlParameter("@QuoteLineId", SqlDbType.Int) { Value = quoteId },
                new SqlParameter("@ReasonNo", SqlDbType.Int) { Value = 1 },
                new SqlParameter("@UpdatedBy", SqlDbType.Int) { Value = loginId },
                new SqlParameter("@Reasons", SqlDbType.NVarChar) { Value = string.Empty },
                new SqlParameter("@RowsAffected", SqlDbType.Int) { Direction = ParameterDirection.Output },
            };
            await _repository.ExecuteSqlRawAsync($"{StoredProcedures.Update_QuoteLine_Close} @QuoteLineId, @ReasonNo, @UpdatedBy, @Reasons, @RowsAffected OUTPUT", parameters);
        }
    }
}