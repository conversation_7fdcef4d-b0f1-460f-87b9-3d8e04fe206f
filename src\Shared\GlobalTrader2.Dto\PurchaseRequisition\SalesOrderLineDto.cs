﻿namespace GlobalTrader2.Dto.PurchaseRequisition
{
    public class SalesOrderLineDto
    {
        public int SalesOrderLineId { get; set; } = 0;
        public int SalesOrderNo { get; set; } = 0;
        public int? SalesOrderNumber { get; set; }
        public string? FullPart { get; set; }
        public string? Part { get; set; }
        public int? ManufacturerNo { get; set; }
        public string? ManufacturerName { get; set; }
        public int? RestrictedMfrNo { get; set; }
        public bool? RestrictedMfrInactive { get; set; }
        public bool Inactive { get; set; }
        public string? DateCode { get; set; }
        public int? PackageNo { get; set; }
        public string? PackageName { get; set; }
        public string? PackageDescription { get; set; }
        public string? Quantity { get; set; }
        public int? QuantityNumber { get; set; }
        public string? Price { get; set; }
        public double? PriceDouble { get; set; }
        public string? PriceVal { get; set; }
        public DateTime? DatePromisedRawValue { get; set; }
        public DateTime? DateRequiredRawValue { get; set; }
        public string DatePromised { get; set; } = string.Empty;
        public string RequiredDate { get; set; } = string.Empty;
        public int? CurrencyNo { get; set; }
        public string? CurrencyCode { get; set; }
        public string? QuantityAllocated { get; set; }
        public int? QuantityAllocatedNumber { get; set; }
        public string? QuantityShipped { get; set; }
        public int? QuantityShippedNumber { get; set; }
        public string? BackOrderQuantity { get; set; }
        public string Instructions { get; set; } = string.Empty;
        public int? ProductNo { get; set; }
        public string? ProductName { get; set; }
        public string? ProductDescription { get; set; }
        public string? CustomerPart { get; set; }
        public bool Posted { get; set; }
        public bool ShipASAP { get; set; }
        public int? StockNo { get; set; }
        public int? SOSerialNumber { get; set; }
        public int? SOSerialNo { get; set; }
        public int? QuoteLineNo { get; set; }
        public string? FormattedDLUP { get; set; }
        public DateTime? DLUP { get; set; }
        public DateTime? PODeliveryDate { get; set; }
        public int SourcingResultUsedByOther { get; set; }
        public string? UpdateByName { get; set; }
        public byte? ROHS { get; set; }
        public FormatRoHSDto ROHSInfo { get; set; } = new();
        public int CompanyNo { get; set; }
        public int IPOApprovedBy { get; set; } = 0;
        public int IsIPOCreated { get; set; }
        public int IsIPOAndPOOpen { get; set; }
        public string? CompanyName { get; set; }
        public bool? CompanyOnStop { get; set; }
        public bool? IsIPO { get; set; }
        public string? LineNotes { get; set; }
        public int? PreferredWarehouseNo { get; set; } = 0;
        public string? Status { get; set; }
        public bool ServiceShipped { get; set; }
        public bool Closed { get; set; }
        public byte? ProductSource { get; set; }
        public string? ProductSourceName { get; set; }
        public bool ProductInactive { get; set; }
        public bool IsOrderViaIPOonly { get; set; }
        public bool IsRestrictedProduct { get; set; }
        public bool AS6081 { get; set; }
        public double? ProductDutyRate { get; set; }
        public double? Cost { get; set; } = 0;
        public string? ProductDutyCode { get; set; }
        public string? MSLLevel { get; set; }
        public string? ContractNo { get; set; }
        public bool IsProdHazardous { get; set; }
        public bool? PrintHazardous { get; set; }
        public DateTime? DateConfirmed { get; set; }
        public int? CustomerRequirementId { get; set; }
        public int? CustomerRequirementNumber { get; set; }
        public string? IHSCountryOfOrigin { get; set; }
        public string? LifeCycleStage { get; set; }
        public string IHSECCNCodeDefination { get; set; } = string.Empty;
        public string StockAvailableDetail { get; set; } = string.Empty;
        public int ClientNo { get; set; }
        public string? HTSCode { get; set; }
        public string? Packing { get; set; }
        public string? PackagingSize { get; set; }
        public string? Descriptions { get; set; }
        public string? IHSProduct { get; set; }
        public string? ECCNCode { get; set; }
        public int? ServiceNo { get; set; }
        public double? AveragePrice { get; set; }
        public string Taxable { get; set; } = string.Empty;
        public bool IsService
        {
            get
            {
                return ServiceNo > 0;
            }
        }
        public bool IsAllocated
        {
            get
            {
                if (IsService)
                {
                    return true;
                }
                else
                {
                    return QuantityAllocatedNumber >= (QuantityNumber - QuantityShippedNumber);
                }
            }
        }
        public bool IsShipped
        {
            get
            {
                if (IsService)
                {
                    return ServiceShipped;
                }
                else
                {
                    return QuantityShippedNumber >= QuantityNumber;
                }
            }
        }
        public string? CompanyAdvisoryNotes { get; set; }
        public string? ManufacturerAdvisoryNotes { get; set; }

        public string? WarningMessage { get; set; }
        public string? ECCNMessage { get; set; }
        public string? ECCNSubject { get; set; }
        public bool? ECCNClientNotify { get; set; }
        public string BlankECCNCode { get; set; } = string.Empty;
        public string? PoECCNCode { get; set; }
    }
}
