﻿import { SearchSelectComponent } from '../../../../../../../../components/search-select/search-select.component.js?v=#{BuildVersion}#';
import { ECCNCodeSearchSelectComponent } from '../search-select/eccn-code-search-select.component.js'
import { ManufactureSearchSelectComponent } from '../search-select/manufacturer-search-select.component.js'
import { ProductSearchSelectComponent } from '../search-select/product-search-select.component.js'
import { PartNoSearchSelectComponent } from '../search-select/part-no-search-select.component.js'
import { LinesTabService } from '../../lines.service.js';
import { FromSourceTypeConstant } from '../../configs/source-type.config.js';
import { FormBase } from '../base/form-base.js';

export class AddNewLineStep3Manager extends FormBase {
    constructor(soGeneralInfo) {
        super();
        this.$dialog = $("#add-lines-dialog");
        this.$form = $("#add-so-line-details-form");
        this.$loading = $("#add-so-line-details-loading-indicator");

        this._partNo = {
            input: $('#add-so-line-details-part-no-search-select'),
            hiddenInput: $('#add-so-line-details-part-no-search-select-value'),
        };
        this._partNoSearchSelect = null;

        this._eccnCode = {
            input: $('#add-so-line-details-eccn-code-search-select'),
            hiddenInput: $('#add-so-line-details-eccn-code-search-select-value'),
        }
        this._eccnCodeSearchSelect = null;

        this._manufacturer = {
            input: $('#add-so-line-details-manufacturer-search-select'),
            hiddenInput: $('#add-so-line-details-manufacturer-search-select-value'),
        }
        this._manufacturerSearchSelect = null;

        this._product = {
            input: $('#add-so-line-details-product-search-select'),
            hiddenInput: $('#add-so-line-details-product-search-select-value'),
        }
        this._productSearchSelect = null;

        this._package = {
            input: $('#add-so-line-details-package-search-select'),
            hiddenInput: $('#add-so-line-details-package-search-select-value'),
        }
        this._packageSearchSelect = null;

        this._serviceSource = {
            service: $('#add-so-line-details-service'),
            description: $('#add-so-line-details-service-description'),
            cost: $('#add-so-line-details-cost-input'),
        };

        this._nonServiceSource = {
            product: $('#add-so-line-details-product-search-select'),
            package: $('#add-so-line-details-package-search-select'),
            rohs: $('#add-so-line-details-rohs'),
            dateCode: $('#add-so-line-details-date-code'),
            manufacturer: $('#add-so-line-details-manufacturer-search-select'),
            isShipAsap: $('#add-so-line-details-ship-asap'),
            shippingInstructions: $('#add-so-line-details-shipping-instructions'),
            partNo: $('#add-so-line-details-part-no-search-select'),
            customerPartNo: $('#add-so-line-details-cust-part-no'),
            eccnCode: $('#add-so-line-details-eccn-code-search-select'),
            msl: $('#add-so-line-details-msl'),
            productSource: $('#add-so-line-details-product-source'),
            contractNo: $('#add-so-line-details-contract-no'),
            printHazWar: $('#add-so-line-details-print-hazardous-warning'),
            as6081: $('#add-so-line-as6081-select'),
        };

        this._as6081 = {
            select: $('#add-so-line-as6081-select'),
            span: $('#add-so-line-as6081-span'),
        }

        this._price = {
            input: $('#add-so-line-details-price-input'),
            span: $('#add-so-line-details-price'),
        };

        this._quantity = {
            input: $('#add-so-line-details-quantity-input'),
            span: $('#add-so-line-details-quantity'),
        };

        this._date = {
            $datePromised: $('#add-so-line-details-date-promised'),
            $dateRequired: $('#add-so-line-details-date-required'),
        }

        this._$salesOrderNumber = $('#add-so-line-details-sales-order-number');
        this._$customerName = $('#add-so-line-details-customer-name');
        this._$landedCost = $('#add-so-line-details-landed-cost');
        this._$poDeliveryDate = $('#add-so-line-details-po-delivery-date');
        this._currency = {
            cost: $('#add-so-line-details-cost-currency'),
            price: $('#add-so-line-details-price-currency'),
        }

        this._sourceSelected = null;
        this._soGeneralInfo = soGeneralInfo;
        this._canEditAS6081 = false;
        this._isFromHub = false;
        this._isFirstInit = true;
        this._taxRate = 0;
        this._linePostedValue = 0;
        this._creditBalance = 0;
        this._data = null;
        this._eccnCodeLabel = null;
    }

    initialize() {
        if (!this._isFirstInit) {
            return
        }

        this._initFields();
        this._setupBehaviors();
        this._setupFormValidation();
        this._isFirstInit = false;
    }

    async showForm(salesOrderMainInfoDto, getSourceDataPromise, sourceSelected) {
        this.setLoading(true);
        this._sourceSelected = sourceSelected;

        this._resetFields();
        this._showFieldsBasedOnConditions();
        this._bindDefaultFields(salesOrderMainInfoDto);

        if (sourceSelected !== FromSourceTypeConstant.NEW) {
            this._data = await getSourceDataPromise;
            this._handleSpecificSourceType(this._data);
            this._bindSourceFields(this._data);
        }

        this._taxRate = parseFloat(salesOrderMainInfoDto.TaxRate) || 0.0;
        this._linePostedValue = salesOrderMainInfoDto.IsAnyLinePosted ? 0 : parseFloat(salesOrderMainInfoDto.LinePostedValue) || 0;
        this._creditBalance = salesOrderMainInfoDto.CreditLimit - (salesOrderMainInfoDto.BalanceWithOpen + salesOrderMainInfoDto.InvoiceNotExported);

        this.setLoading(false);
        this.$dialog[0].scrollTop = 0;
        focusFirstField(this.$form);
    }

    validateForm() {
        return this.$form.valid() && this._validateCreditLimit();
    }

    async submitForm() {
        const formData = GlobalTrader.FormHelper.convertFormDataToOject(this.$form);
        formData['SalesOrderNo'] = this._soGeneralInfo.SalesOrderId;
        formData['ECCNCode'] = this._eccnCodeLabel;
        if (this._sourceSelected !== FromSourceTypeConstant.NEW) {
            formData['DocId'] = this._data.SourceItemId;
            switch (this._sourceSelected) {
                case FromSourceTypeConstant.SERVICE:
                    formData['Part'] = this._data.ServiceName;
                    formData['CustomerPart'] = formData['ServiceDescription'];
                    formData['ServiceNo'] = this._data.SourceItemId;
                    formData['DocType'] = 'SRV';
                    break;
                case FromSourceTypeConstant.STOCK:
                    formData['StockNo'] = this._data.SourceItemId;
                    formData['DocType'] = 'STK';
                    break;
                case FromSourceTypeConstant.QUOTE:
                    formData['QuoteLineNo'] = this._data.SourceItemId;
                    formData['SourcingResultNo'] = this._data.SourcingResultNo;
                    formData['DocType'] = 'QL';
                    break;
                case FromSourceTypeConstant.SOURCINGRESULT:
                    formData['DocType'] = 'SRC';
                    formData['SourcingResultNo'] = this._data.SourceItemId;
                    break;
                case FromSourceTypeConstant.CUSREQ:
                    formData['DocType'] = 'REQ';
                    break;
                case FromSourceTypeConstant.SO:
                    formData['DocType'] = 'SOL';
                    break;
            }
        }
        formData['AS6081'] = formData['AS6081'] === '1';
        const header = { "RequestVerificationToken": this.$form.find(':input[name="__RequestVerificationToken"]').val() }
        return await LinesTabService.createSalesOrderLineAsync(formData, header);
    }

    onFormClose() {
        this.$form[0].reset();
        this.$form.validate().resetForm();
        this.$dialog.find(".form-error-summary").hide();
        this.$form.find('.is-invalid').removeClass("is-invalid");

        this._partNoSearchSelect.resetSearchSelect(false);
        this._manufacturerSearchSelect.resetSearchSelect(false);
        this._eccnCodeSearchSelect.resetSearchSelect(false);
        this._productSearchSelect.resetSearchSelect(false);
        this._packageSearchSelect.resetSearchSelect(false);
        this.setLoading(false);
    }

    _validateCreditLimit() {
        if (this._sourceSelected === FromSourceTypeConstant.STOCK) {
            const quantity = parseFloat(this._quantity.input.val()) || 0;
            const price = parseFloat(this._price.input.val()) || 0;
            const totalPrice = quantity * price;
            const tax = totalPrice * this._taxRate;
            const total = totalPrice + tax + this._linePostedValue;

            if (total <= this._creditBalance) {
                return true;
            } else {
                alert(addSoLineMessages.overCreditLimitOfCompanyMessage); // Get message from localized strings
                return false;
            }
        }
        return true;
    }

    _initFields() {
        // Date Picker
        this.$form.find('.datepicker').each((index, element) => {
            $(element).datepicker2();
        });

        // AutoSearch
        this._eccnCodeSearchSelect = new ECCNCodeSearchSelectComponent(this._eccnCode.input[0].id, this._eccnCode.hiddenInput[0].id, 'single', 'keyword', '/orders/customer-requirements/auto-search-part-eccn', 2);
        this._manufacturerSearchSelect = new ManufactureSearchSelectComponent(this._manufacturer.input[0].id, this._manufacturer.hiddenInput[0].id, 'single', 'keyword', '/manufacturers/auto-search', 2);
        this._productSearchSelect = new ProductSearchSelectComponent(this._product.input[0].id, this._product.hiddenInput[0].id, 'single', 'keyword', '/products/auto-search', 2);
        this._partNoSearchSelect = new PartNoSearchSelectComponent(this._partNo.input[0].id, this._partNo.hiddenInput[0].id, 'single', 'keyword', '/auto-search/part-no', 3);
        this._packageSearchSelect = new SearchSelectComponent(this._package.input[0].id, this._package.hiddenInput[0].id, 'single', 'keyword', '/packages/auto-search', 2);

        // Dropdown
        this._nonServiceSource.productSource.dropdown({
            serverside: false,
            endpoint: '/lists/product-sources',
            placeholderValue: "",
        });

        this._nonServiceSource.rohs.dropdown({
            serverside: false,
            endpoint: '/lists/rohs-statuses',
            textKey: 'description',
            valueKey: 'rohsStatusId',
            placeholderValue: "",
        });

        this._nonServiceSource.as6081.dropdown({
            serverside: false,
            endpoint: '/lists/AS6081',
            placeholderValue: "",
        });

        this._nonServiceSource.msl.dropdown({
            serverside: false,
            endpoint: '/lists/msls',
            valueKey: 'name',
            placeholderValue: "",
        });
    }

    _bindDefaultFields(salesOrderMainInfoDto) {
        this._setFieldValue(this._$salesOrderNumber, salesOrderMainInfoDto.SalesOrderNumber);
        this._setFieldValue(this._$customerName, salesOrderMainInfoDto.CustomerName);
        this._setFieldValue(this._currency.price, salesOrderMainInfoDto.CurrencyCode);
        this._setFieldValue(this._date.$datePromised, new Date());
        this._setFieldValue(this._date.$dateRequired, new Date());
    }

    async _bindSourceFields(data) {
        const propertyNames = Object.keys(data);

        for (const propertyName of propertyNames) {
            await this._bindSingleField(propertyName, data);
        }
    }

    async _bindSingleField(propertyName, data) {
        const fieldHandlers = {
            'Part': () => this._bindPartField(data[propertyName], data),
            'EccnCode': () => this._bindEccnCodeField(data[propertyName]),
            'ManufacturerNo': () => this._bindManufacturerField(data),
            'ProductNo': () => this._bindProductField(data),
            'PackageNo': () => this._bindPackageField(data)
        };

        const handler = fieldHandlers[propertyName];
        if (handler) {
            await handler();
        } else {
            this._bindField(propertyName, data[propertyName]);
        }
    }

    _bindPartField(partValue, data) {
        if (partValue != null) {
            this._setFieldValue(this._partNoSearchSelect, {
                label: partValue,
                value: partValue,
            });
            if (data.EccnCode == null) {
                this._partNo.input.trigger('blur');
            }
        }
    }

    async _bindEccnCodeField(eccnCodeValue) {
        if (eccnCodeValue == null) {
            return;
        }

        const eccnList = await this._getIHSEccnCodeByEccnCodeAsync(eccnCodeValue);
        if (eccnList.length === 0) {
            return;
        }

        const eccnCodeData = eccnList[0];
        if (eccnCodeData.eccnNo > 0) {
            eccnCodeData['label'] = eccnCodeData.eccnCode;
            eccnCodeData['value'] = eccnCodeData.eccnNo;
            this._setFieldValue(this._eccnCodeSearchSelect, eccnCodeData);
        }
    }

    _bindManufacturerField(data) {
        if (data.Manufacturer != null && data.ManufacturerNo != null) {
            this._setFieldValue(this._manufacturerSearchSelect, {
                label: data.Manufacturer,
                value: data.ManufacturerNo,
            });
        }
    }

    _bindProductField(data) {
        if (data.Product != null && data.ProductNo != null) {
            this._setFieldValue(this._productSearchSelect, {
                label: GlobalTrader.StringHelper.setCleanTextValue(data.Product),
                value: data.ProductNo,
            });
        }
    }

    _bindPackageField(data) {
        if (data.Package != null && data.PackageNo != null) {
            this._setFieldValue(this._packageSearchSelect, {
                label: GlobalTrader.StringHelper.setCleanTextValue(data.Package),
                value: data.PackageNo,
            });
        }
    }

    _bindField(propertyName, propertyValue) {
        const $element = this.$form.find(`[name="${propertyName}"]`).first();
        if ($element.length && propertyValue != null) {
            if (propertyName === 'AS6081') {
                this._as6081.span.text(propertyValue ? 'Yes' : 'No');
            }
            this._setFieldValue($element, GlobalTrader.StringHelper.setCleanTextValue(propertyValue));
        }
    }

    async _handleSpecificSourceType(data) {
        switch (this._sourceSelected) {
            case FromSourceTypeConstant.CUSREQ: {
                this._showField(this._$poDeliveryDate, false);
                break;
            }
            case FromSourceTypeConstant.QUOTE:
            case FromSourceTypeConstant.SO:
                this._showField(this._$poDeliveryDate, data.IsIPO && !data.IsIPOExist);
                this._isFromHub = data.IsIPO;
                break;
            case FromSourceTypeConstant.SERVICE:
                this._setFieldValue(this._currency.cost, data.ClientCurrency);
                this._setFieldValue(this._as6081.select, false);
                break;
            case FromSourceTypeConstant.STOCK:
                this._setFieldValue(this._nonServiceSource.customerPartNo, '');
                break;
            case FromSourceTypeConstant.SOURCINGRESULT:
                this._showField(this._$poDeliveryDate, data.IsIPO);
                this._isFromHub = data.IsIPO;
                break;
            default:
                break;
        }
    }

    _setupBehaviors() {
        allowPositiveIntegerInput(`#${this._quantity.input[0].id}`);
        this._quantity.input.on('input', (e) => {
            const value = e.target.value;
            if (parseInt(value) >= parseInt(window.constants.maxInt32Value)) {
                e.target.value = window.constants.maxInt32Value;
            }
        });
        
        allowPositiveDecimalInput(`#${this._price.input[0].id}`, true, 5);
        allowPositiveDecimalInput(`#${this._serviceSource.cost[0].id}`, true, 5);

        this._partNo.input.on('input', (e) => {
            $(e.target).val(e.target.value.toUpperCase());
        });

        this._partNo.input.on('blur', async (e) => {
            const partNoValue = this._partNo.hiddenInput.val().trim();
            if (partNoValue.length > 0) {
                const eccnCodeList = await this._getIHSEccnCodeByPartNoAsync(partNoValue);
                if (eccnCodeList?.length > 0) {
                    const eccnCodeData = eccnCodeList[0];
                    eccnCodeData['label'] = eccnCodeData.eccnCode;
                    eccnCodeData['value'] = eccnCodeData.eccnNo;
                    this._setFieldValue(this._eccnCodeSearchSelect, eccnCodeData);
                }
            }
        });

        this._productSearchSelect.on("selectItem", (data) => {
            this._nonServiceSource.printHazWar.prop('checked', false);
            this._nonServiceSource.printHazWar.prop('disabled', !data.isHazarders);
        });

        this._eccnCodeSearchSelect.on("selectItem", (data) => {
            this._eccnCodeLabel = data.label;
        });

        this._eccnCodeSearchSelect.on("removeItem", (data) => {
            this._eccnCodeLabel = null;
        });
    }

    _showFieldsBasedOnConditions() {
        const isService = this._sourceSelected === FromSourceTypeConstant.SERVICE;
        this._showField(this._nonServiceSource.productSource, this._soGeneralInfo.IsAS9120);
        this._showField(this._$poDeliveryDate, false);
        this._enableCheckBox(this._nonServiceSource.printHazWar, false);
        Object.entries(this._nonServiceSource).forEach(([key, $element]) => {
            this._showField($element, !isService);
        });
        Object.entries(this._serviceSource).forEach(([key, $element]) => {
            this._showField($element, isService);
        });

        this._showField(this._$landedCost, this._sourceSelected === FromSourceTypeConstant.STOCK);
        switch (this._sourceSelected) {
            case FromSourceTypeConstant.NEW:
                this._canEditAS6081 = true;
                this._showField(this._as6081.select, true);
                this._showField(this._as6081.span, false);
                break;
            case FromSourceTypeConstant.SERVICE:
                this._canEditAS6081 = false;
                this._showField(this._as6081.select, false);
                this._showField(this._as6081.span, false);
                break;
            default:
                this._canEditAS6081 = false;
                this._showField(this._as6081.select, false);
                this._showField(this._as6081.span, true);
        }
    }

    async _setupFormValidation() {
        const self = this;
        let as6081AlertMessage = await this._getAS6081AlertMessageAsync();

        $.validator.addMethod("validateAS6081", function (value, element, param) {
            if (value == '2') {
                alert(as6081AlertMessage);
                return false;
            }
            return true;
        }, '');

        $.validator.addMethod("restrictedManufacturer", function (value, element, param) {
            return !(self._manufacturerSearchSelect.isRestrictedManufacturer);
        }, '');

        this.$form.validate({
            ignore: [],
            invalidHandler: (event, validator) => {
                setTimeout(() => {
                    self._scrollToFirstInvalidField();
                }, 50);
            },
            rules: {
                AS6081: {
                    required: {
                        depends: (element) => {
                            return self._canEditAS6081;
                        }
                    },
                    validateAS6081: {
                        depends: (element) => {
                            return self._canEditAS6081;
                        }
                    }
                },
                Part: {
                    required: {
                        depends: (element) => {
                            return self._sourceSelected !== FromSourceTypeConstant.SERVICE;
                        }
                    },
                },
                Quantity: {
                    required: true,
                    min: 1
                },
                Cost: {
                    required: {
                        depends: (element) => {
                            return self._sourceSelected === FromSourceTypeConstant.SERVICE;
                        }
                    },
                    min: 0,
                    max: 2000000000,
                },
                Price: {
                    required: true,
                    min: 0,
                    max: 2000000000,
                },
                ProductNo: {
                    required: {
                        depends: (element) => {
                            return self._sourceSelected !== FromSourceTypeConstant.SERVICE;
                        }
                    },
                    min: {
                        param: 1,
                        depends: (element) => {
                            return self._sourceSelected !== FromSourceTypeConstant.SERVICE;
                        }
                    },
                },
                DatePromised: 'required',
                DateRequired: 'required',
                ProductSource: {
                    required: {
                        depends: (element) => {
                            return self._sourceSelected !== FromSourceTypeConstant.SERVICE;
                        }
                    }
                },
                PoDeliveryDate: {
                    required: {
                        depends: (element) => {
                            return self._isFromHub;
                        }
                    }
                },
                Msl: {
                    required: {
                        depends: (element) => {
                            return self._sourceSelected !== FromSourceTypeConstant.SERVICE;
                        }
                    }
                },
                ManufacturerNo: {
                    restrictedManufacturer: {
                        depends: (element) => {
                            return self._sourceSelected !== FromSourceTypeConstant.SERVICE;
                        }
                    },
                },
            },
            highlight: (element) => {
                const inputName = $(element).attr("name");
                const searchSelectNames = ['Part', 'ECCNNo', 'ProductNo', 'PackageNo'];

                if (searchSelectNames.includes(inputName)) {
                    switch (inputName) {
                        case 'Part':
                            this._partNoSearchSelect.displaySearchSelectErrorBorder();
                            break;
                        case 'ECCNNo':
                            this._eccnCodeSearchSelect.displaySearchSelectErrorBorder();
                            break;
                        case 'ProductNo':
                            this._productSearchSelect.displaySearchSelectErrorBorder();
                            break;
                        case 'PackageNo':
                            this._packageSearchSelect.displaySearchSelectErrorBorder();
                            break;
                    }
                } else if (inputName === 'ManufacturerNo') {
                    $(element).parent().addClass("is-invalid");
                }
                else {
                    $(element).addClass("is-invalid");
                }
            },
            unhighlight: (element) => {
                const inputName = $(element).attr("name");
                const searchSelectNames = ['Part', 'ECCNNo', 'ProductNo', 'PackageNo'];

                if (searchSelectNames.includes(inputName)) {
                    switch (inputName) {
                        case 'Part':
                            this._partNoSearchSelect.removeSearchSelectErrorBorder();
                            break;
                        case 'ECCNNo':
                            this._eccnCodeSearchSelect.removeSearchSelectErrorBorder();
                            break;
                        case 'ProductNo':
                            this._productSearchSelect.removeSearchSelectErrorBorder();
                            break;
                        case 'PackageNo':
                            this._packageSearchSelect.removeSearchSelectErrorBorder();
                            break;
                    }
                } else if (inputName === 'ManufacturerNo') {
                    $(element).parent().removeClass("is-invalid");
                }
                else {
                    $(element).removeClass("is-invalid");
                }
            },
            errorPlacement: function (error, element) {
                const $element = $(element);
                const inputName = $(element).attr("name");
                const specialNames = ['Cost', 'Price']

                if ($element.hasClass('datepicker') || $element.hasClass('dropdown') || specialNames.includes(inputName)) {
                    error.insertAfter($element.parent());
                } else if (inputName === 'ManufacturerNo') {
                    return;
                }
                else {
                    error.insertAfter(element); // Default placement
                }
            }
        });
    }

    _resetFields() {
        this._isFromHub = false;
    }

    setLoading(isLoading) {
        if (isLoading) {
            this.$dialog.dialog("setLoading", true);
            this.$form.hide();
        } else {
            this.$dialog.dialog("setLoading", false);
            this.$form.show();
        }
    }

    _scrollToFirstInvalidField() {
        // Find the first invalid field
        const $firstInvalidField = this.$form.find('.is-invalid').first();

        if ($firstInvalidField.length > 0) {
            // Scroll to the invalid field within the dialog
            const dialogScrollTop = this.$dialog[0].scrollTop;
            const fieldOffsetTop = $firstInvalidField.offset().top;
            const dialogOffsetTop = this.$dialog.offset().top;
            const relativeTop = fieldOffsetTop - dialogOffsetTop + dialogScrollTop;

            // Scroll with some padding
            this.$dialog[0].scrollTop = Math.max(0, relativeTop - 50);

            // Focus the field if it's focusable
            if ($firstInvalidField.is('input, select, textarea')) {
                setTimeout(() => {
                    $firstInvalidField.focus();
                }, 100);
            }
        }
    }

    async _getIHSEccnCodeByPartNoAsync(partNo) {
        const response = await LinesTabService.getIHSEccnCodeByPartNoAsync(partNo);
        if (response?.success) {
            return response.data;
        } else {
            return null;
        }
    }

    async _getIHSEccnCodeByEccnCodeAsync(eccnCode) {
        const response = await LinesTabService.getIHSEccnCodeByEccnCodeAsync(eccnCode);
        if (response?.success) {
            return response.data;
        } else {
            return null;
        }
    }

    async _getAS6081AlertMessageAsync() {
        const response = await LinesTabService.getAS6081MessageAsync('PO');
        if (response?.success && response.data !== null) {
            return response.data;
        } else {
            return "";
        }
    }
}
