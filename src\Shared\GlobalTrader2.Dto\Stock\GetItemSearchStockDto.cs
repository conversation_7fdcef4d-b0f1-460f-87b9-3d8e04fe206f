﻿namespace GlobalTrader2.Dto.Stock
{
    public class GetItemSearchStockDto
    {
        public int ClientNo { get; set; }
        public int StockId { get; set; }
        public string Part { get; set; } = string.Empty;
        public string? SupplierPart { get; set; }
        public int QuantityInStock {  get; set; }
        public int QuantityOnOrder { get; set; }
        public int QuantityAvailable { get; set; }
        public int QuantityAllocated { get; set; }
        public decimal LandedCost { get; set; }
        public string FormatedLandedCost { get; set; } = string.Empty;
        public bool Unavailable { get; set; }
        public string SupplierName { get; set; } = string.Empty;
        public DateTime? PODeliveryDate { get; set; }
        public string? PODeliveryDateText
        {
            get => PODeliveryDate?.ToString("dd/MM/yyyy");
        }
        public int PurchaseOrderNumber { get; set; }
        public int CustomerRMANumber { get; set; }
        public DateTime CustomerRMADate { get; set; }
        public string WarehouseName { get; set; } = string.Empty;
        public string? Location { get; set; }
        public int? POSerialNo { get; set; }
        public long RowNum { get; set; }
        public int RowCnt { get; set; }
        public byte? ROHS { get; set; }
    }
}
