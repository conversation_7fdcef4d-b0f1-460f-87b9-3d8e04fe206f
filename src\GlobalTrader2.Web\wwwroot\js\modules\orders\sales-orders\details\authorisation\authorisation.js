﻿import { AuthorisationSectionService } from './authorisation-services.js'; 
import { RequestApprovalManager } from './request-approval/request-approval.js'; 
import { ReadyToShipManager } from './ready-to-ship/ready-to-ship.js';
import { AuthoriseDeauthoriseManager } from './authorise-deauthorise/authorise-deauthorise.js';
export class AuthorisationManager {

    constructor({ salesOrderId, refreshCallback = () => { }, onAuthoriseSuccess = () => { }, onRequestApprovalSuccess = () => { } })
    {
        this.salesOrderId = salesOrderId;
        this.companyId = $("#open-sales-order-table").data("company-id") || null ;
        this.$sectionBox = $("#authorisation-box");
        this.$openSalesOrderTable = $("#open-sales-order-table");
        this.$authzHistoryTable = $("#authorisation-history-order-table");
        this.data = null;
        this.authorisationData = null;
        this.$wrapper = $("#authorisation-information-content-wrapper");
        this.openSalesOrderData = null;
        this.authHistoryData = null;
        this.apiService = AuthorisationSectionService;
        this.$requestApprovalButton = $("#request-approval-dialog-button");
        this.requestApprovalData = null;
        this.canRequestApproval = false;
        this.allowAuthoriseOnStop = $("#request-approval-dialog-button").data("allow-authorise-on-stop") === "True" || false;
        this.displayReadyToShip = $("#ready-to-ship-button").data("display-ready-to-ship") === "True" || false;
        this.approvalTooltipContent = $("#approval-tooltip-content");
        this.$readyToShipButton = $("#ready-to-ship-button");
        this.$authoriseButton = $("#authorise-button");
        this.$deauthoriseButton = $("#deauthorise-button");
        this.readyToShipData = null;
        this.authoriseDeauthoriseData = null;
        this.refreshCallback = refreshCallback;
        this.onRequestApprovalSuccess = onRequestApprovalSuccess;
        this.onAuthoriseSuccess = onAuthoriseSuccess;
  
        
    }
    customLoading(id) {
        this.$sectionBox.section_box('setLoadingContentId', id);
        this.$sectionBox.section_box("option", "loading", true);
    }
    customLoaded() { 
        this.$sectionBox.section_box("option", "loading", false);
        this.$sectionBox.section_box('setLoadingContentId', null);
    }
    async initialize() {
        this.setupSectionBox();
        this.$sectionBox.section_box("option", "loading", true);
        await this.getAuthorisationData();
        this.enableAuthoriseDeauthoriseButton();
        this.checkRequestApprovalPermission();
        this.enabledReadyToShipButton();
        this.getRequestApprovalData();
        this.getReadyToShipData();
        this.getAuthoriseDeauthoriseData();
        await this.getAuthHistoryData();
        this.bindingSpanData();
        this.authoriseDeauthoriseManager = new AuthoriseDeauthoriseManager({
                authoriseData: this.authoriseDeauthoriseData,
                successCallback: () => {
                    this.refreshSectionBox();
                    this.onAuthoriseSuccess();
                }
            });
        this.authoriseDeauthoriseManager.initialize();
        this.requestApprovalDialogManager = new RequestApprovalManager(this.requestApprovalData, this.onRequestApprovalSuccess);
        this.requestApprovalDialogManager.initialize();
        this.readyToShipDialogManager = new ReadyToShipManager({
            readyToShipData: this.readyToShipData,
            successCallback: () => { this.refreshSectionBox(); }
            });
        this.readyToShipDialogManager.initialize();
        this.handleOpenDialogButton();
        this.setUpOpenSalesOrderTable();
        this.$sectionBox.section_box("option", "loading", false);
        this.setUpAuthzHistoryTable();
        this.customLoading('open-sales-order-table_wrapper');
        await this.getOpenSalesOrderData();
        this.customLoaded();
        this.$openSalesOrderTable.DataTable().clear().draw();
        this.$openSalesOrderTable.DataTable().rows.add(this.openSalesOrderData).draw();
        customAdjustHeightResizeDatatable("#open-sales-order-table",3);

    }
     setupSectionBox() {
        this.$sectionBox.section_box({
            loading: false,
            onRefreshClick: async (event, ui) => {
                await this.refreshSectionBox();
                if (this.refreshCallback) {
                    this.refreshCallback();
                }
                
            }
        });
    }
    async refreshSectionBox() {
        this.$sectionBox.section_box("option", "loading", true);
        await this.getAuthorisationData();

        await this.getAuthHistoryData();
        this.enableAuthoriseDeauthoriseButton();
        this.checkRequestApprovalPermission();    
        this.enabledReadyToShipButton();
        this.bindingSpanData();
        this.getAuthoriseDeauthoriseData();
        this.getRequestApprovalData();
        this.getReadyToShipData();
        this.authoriseDeauthoriseManager.reloadData(this.authoriseDeauthoriseData);
        this.requestApprovalDialogManager.reloadData(this.requestApprovalData);
        this.readyToShipDialogManager.reloadData(this.readyToShipData)
        this.$sectionBox.section_box("option", "loading", false);
        this.$authzHistoryTable.DataTable().clear().draw();
        this.$authzHistoryTable.DataTable().rows.add(this.authHistoryData).draw();
        this.customLoading('open-sales-order-table_wrapper');
        await this.getOpenSalesOrderData();
        this.customLoaded();
        this.$openSalesOrderTable.DataTable().clear().draw();
        this.$openSalesOrderTable.DataTable().rows.add(this.openSalesOrderData).draw();
        customAdjustHeightResizeDatatable("#open-sales-order-table",3);
    }
    getPropertyCaseInsensitive(obj, key) {
        key = key.toLowerCase();
        for (let prop in obj) {
            if (prop.toLowerCase() === key) {
                return obj[prop];
            }
        }
        return undefined; // not found
    }
     setUpOpenSalesOrderTable() {
        this.$openSalesOrderTable
            .ResizeDataTable({
                data: this.openSalesOrderData ?? [],
                info: false,
                responsive: true,
                select: false,
                language: {
                    emptyTable: `<i>${window.localizedStrings.noDataFound}</i>`,
                    zeroRecords: `<i>${window.localizedStrings.noDataFound}</i>`
                },
                paging: false,
                ordering: false,
                searching: false,
                scrollCollapse: true,
                resizeConfig: {
                    numberOfRowToShow: 3
                },
                headerCallback: (thead) => {
                    $(thead).find("th").addClass('text-start');
                },
                columnDefs: [
                    { targets: '_all', className: 'text-break' }

                ],
                columns: [
                    {
                        data: (row) => (
                            {
                                salesOrderId: row.salesOrderId,
                                salesOrderNumber: row.salesOrderNumber,
                            }
                        ),
                        title: 'No',
                        type: 'string',
                        render: (data, type, row) => {
                            return `<span><a class="dt-hyper-link" href="/Orders/SalesOrders/Details?so=${data.salesOrderId}">${data.salesOrderNumber}</a></span>`;
                        },
                    },
                    {
                        data: 'dateOrdered',
                        title: 'Date',
                        type: 'string'

                    },
                    {
                        data: 'value',
                        title: 'Order Value',
                        type: 'string'

                    },
                    {
                        data: 'amount',
                        title: 'Posted Lines Value',
                        type: 'string'

                    },
                
                ],
                rowId: 'salesOrderId',
            });
    }
     setUpAuthzHistoryTable() {
        this.$authzHistoryTable
            .ResizeDataTable({
                data: this.authHistoryData ?? [],
                info: false,
                responsive: true,
                select: false,
                language: {
                    emptyTable: `<i>${window.localizedStrings.noDataFound}</i>`,
                    zeroRecords: `<i>${window.localizedStrings.noDataFound}</i>`
                },
                paging: false,
                ordering: false,
                searching: false,
                scrollCollapse: true,
                resizeConfig: {
                    numberOfRowToShow: 3
                },
                headerCallback: (thead) => {
                    $(thead).find("th").addClass('text-start');
                },
                columnDefs: [
                    { targets: '_all', className: 'text-break' },
                    { width: '100px', targets: 0 },
                    { width: '100px', targets: 1 },
                    { width: '15%', targets: 2 }
                ],
                columns: [
                    {
                        data: 'action',
                        title: 'Action',
                        type: 'string',
                        
                    },
                    {
                        data: (row) => (
                            {
                                date: row.dateAuthorised,
                                time: row.timeAuthorised,
                            }
                        ),
                        title: 'Date',
                        type: 'string',
                        render: (data, type, row) => {
                            return `${data.date} ${data.time}`;
                        },

                    },
                    {
                        data: 'authorisedBy',
                        title: 'By',
                        type: 'string'

                    },
                    {
                        data: 'comment',
                        title: 'Comments',
                        type: 'string',
                        

                    },
                ],
                rowId: 'bOMManagerId',
            });
    }
    bindingSpanData() {
        const authData = this.authorisationData;
        if (authData) {
            this.$wrapper.find("span[data-field]").toArray().forEach(element => {
               
                const $el = $(element);
                const fieldName = $el.data('field');
                let value = this.getPropertyCaseInsensitive(authData, fieldName);
                $el.text(value);
            })
            if (authData.soAuthDisabledReason && authData.soAuthDisabledReason != "") {
                let keyword = "Attention Required";
                if (authData.isSoTermswarning) {
                     keyword = "SO Authorisation Request Prevented";
                }
              
                const toolTipText = GlobalTrader.StringHelper.setCleanTextValue(authData.soAuthDisabledReason, true).replace(
                    new RegExp(keyword, 'g'),
                    `<span class="attention-note">${keyword}</span>`
                );
                this.approvalTooltipContent.html(toolTipText);
                if (!toolTipText.includes("attention-note")) {
                    this.approvalTooltipContent.addClass("green-note");
                }
                $("#approval-tooltip-wrapper").show();
              
            }
            else if ((!authData.soAuthDisabledReason || authData.soAuthDisabledReason == "") && authData.isSoTermswarning) {
                this.approvalTooltipContent.html(`<span class="attention-note"> SO Authorisation Request Prevented </span>`);
                $("#approval-tooltip-wrapper").show();
            }
         }
        
    };
    async getAuthorisationData() {
        let response = await this.apiService.GetAuthorisationInfoAsync(this.salesOrderId);
        if (response.success) {
            this.authorisationData = response.data;
        }
    }
    async getOpenSalesOrderData() {
        if (this.companyId != null) {
            let response = await this.apiService.GetOpenSalesOrderAsync(this.companyId);
            if (response.success) {
                this.openSalesOrderData = response.data;
            }
        }
    }
    async getAuthHistoryData() {
        let response = await this.apiService.GetAuthorisationHistoryAsync(this.salesOrderId);
        if (response.success) {
            this.authHistoryData = response.data;
        }
    }
    handleOpenDialogButton() {
        this.$requestApprovalButton.on("click", () => {
            this.requestApprovalDialogManager.$dialog.dialog('open');
        })
        this.$readyToShipButton.on("click", () => {
            this.readyToShipDialogManager.$dialog.dialog('open');
        })
        this.$authoriseButton.on("click", () => {
            this.authoriseDeauthoriseManager.setAuthorise(true);
            this.authoriseDeauthoriseManager.$dialog.dialog('open');
        })
        this.$deauthoriseButton.on("click", () => {
            this.authoriseDeauthoriseManager.setAuthorise(false);
            this.authoriseDeauthoriseManager.$dialog.dialog('open');
        })
    }
    getRequestApprovalData() {
        this.requestApprovalData = {
            salesOrderId: this.salesOrderId,
            subject: this.authorisationData.requestSubject,
            body: this.authorisationData.requestBody,
        };
    }
    getReadyToShipData() {
        this.readyToShipData = {
            salesOrderId: this.salesOrderId,
            salesOrderNumber: this.authorisationData.salesOrderNumber,
            companyName: this.authorisationData.companyName,
        };
    }
    getAuthoriseDeauthoriseData() {
        this.authoriseDeauthoriseData = {
            salesOrderId: this.salesOrderId,
            salesOrderNumber: this.authorisationData.salesOrderNumber,
            companyName: this.authorisationData.companyName,
            companyOnStop: this.authorisationData.isCompanyStop,
        };
    }
    checkRequestApprovalPermission() {
        this.canRequestApproval = false;
        const authData = this.authorisationData;
        if (authData.isAllowAuthorised && authData.isNotCompleted && authData.isPODocExist && authData.isSoLinePosted && authData.isExportApprove && !authData.isSoTermswarning) {
            if (!this.authorisationData.isCompanyStop || (this.authorisationData.isCompanyStop && this.allowAuthoriseOnStop)) {
                this.canRequestApproval = true;
            }
         
        }
       
        this.enableRequestApprovalButton(this.canRequestApproval);
    }
    enableRequestApprovalButton(canRequestApproval) {
        this.$requestApprovalButton.prop('disabled', !canRequestApproval);
    }
    enabledReadyToShipButton() {
        if (this.authorisationData.isCompanyStop && this.displayReadyToShip) {
            this.$readyToShipButton.show();
        }
        else {
            this.$readyToShipButton.hide();
        }
        
        const enableReadyToShip = this.authorisationData.isAuthorised && !this.authorisationData.allowReadyToShipTicked;
        this.$readyToShipButton.prop('disabled', !enableReadyToShip);
        
    }
    enableAuthoriseDeauthoriseButton() {
        let enableAuthoriseButton = false;
        let enableDeauthoriseButton = false;
        const authData = this.authorisationData;
        const isAuthorised = authData.authorisedBy > 0;
        if (authData.isAllowAuthorised && !isAuthorised && authData.isNotCompleted && authData.isPODocExist && authData.isExportApprove) {
            if (!authData.isCompanyStop || (authData.isCompanyStop && this.allowAuthoriseOnStop)) {
                enableAuthoriseButton = true;
            }
        }
        if (isAuthorised && authData.isNotCompleted) {
            enableDeauthoriseButton = true;
        }
        this.$authoriseButton.prop('disabled', !enableAuthoriseButton);
        this.$deauthoriseButton.prop('disabled', !enableDeauthoriseButton);
    }
}