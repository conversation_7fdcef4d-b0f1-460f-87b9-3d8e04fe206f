﻿import { PurchaseOrderDetailsService } from '../purchase-order-details-services.js?v=#{BuildVersion}#';

export class EprDropdownMenu {
    constructor(purchaseOrderId, menu, button, container, createNewSelector = null, orderBy = null) {
        this.purchaseOrderId = purchaseOrderId;
        this.menu = `#${menu}`;
        this.$button = $(`#${button}`);
        this.container = `#${container}`;
        this.createNewSelector = createNewSelector ? `#${createNewSelector}` : null;
        this.orderBy = orderBy ?? null;
        this.additionalData = {
            purchaseOrderLines: null,
            purchaseOrderLineIds: null
        };

        this.eprList = null;
        this.$eprDropDownMenu = $(this.menu);
    }

    async initialize() {
        await this.getEprList();

        if (!this.eprList.length && !this.createNewSelector) {
            this.$button.prop("disabled", true);
            return;
        }

        this.renderEprList();

        this._handleDropdownClick();

        if (this.createNewSelector) {
            this._handleCreateEpr();
        }
    }

    async getEprList() {
        const response = await PurchaseOrderDetailsService.getEprListAsync(this.purchaseOrderId, this.orderBy);

        if (response.success) {
            this.eprList = response.data
        }
        else {
            window.showToast('danger', response.error);
        }
    }

    renderEprList() {
        this.$eprDropDownMenu.empty();

        let htmlCode = "";

        if (this.createNewSelector) {
            htmlCode += `
                <li class="dropdown-item">
                    <button id="add-new-epr" class="link-button text-center">
                        ${localizedStrings.newEpr}
                    </button>
                </li>
            `
        }

        this.eprList.forEach(epr => {
            const PURCHASE_ORDER_LINES = null;
            const PURCHASE_ORDER_LINE_IDS = null;

            htmlCode += `
                <li class="dropdown-item">
                    <div class="d-flex align-self-center justify-content-between">
                        <button class="link-button text-break text-wrap text-start"
                                onclick="$RGT_openEprWindow(${this.purchaseOrderId}, ${PURCHASE_ORDER_LINES}, ${PURCHASE_ORDER_LINE_IDS}, ${epr.eprId})">
                            ${epr.purchaseOrderNumber}-${epr.eprId}
                        </button>
                        <button class="link-button">
                            ${localizedStrings.log}
                        </button>
                    </div>
                </li>
            `
        });

        this.$eprDropDownMenu.html(htmlCode);
    }

    _handleDropdownClick() {
        $(this.container).on('mouseenter focusin', () => {
            $(this.menu).show();
        });

        $(this.container).on('mouseleave focusout', () => {
            setTimeout(() => {
                if (!$(document.activeElement).closest(this.container).length) {
                    $(this.menu).hide();
                }
            }, 100);
        });
    }

    _handleCreateEpr() {
        $(this.createNewSelector).on("click", () => {
            const purchaseOrderLines = this.additionalData.purchaseOrderLines;
            const purchaseOrderLineIds = this.additionalData.purchaseOrderLineIds;

            if (purchaseOrderLines === null || purchaseOrderLineIds === null) {
                alert("Please select line item to create EPR");
                return;
            }

            $RGT_openEprWindow(
                this.purchaseOrderId,
                this.additionalData.purchaseOrderLines,
                this.additionalData.purchaseOrderLineIds
            );
        });
    }

    _bindDataFromSource(additionalData) {
        this.additionalData = additionalData; // { purchaseOrderLines: string, purchaseOrderLineIds: string }
    }
}
