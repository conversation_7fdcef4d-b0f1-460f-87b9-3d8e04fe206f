using System.Text.Json.Serialization;
using GlobalTrader2.Dto.Converters.DateTime;

namespace GlobalTrader2.Dto.SalesOrderLine
{
    public class AddSalesOderLineRequest
    {
        public int SalesOrderNo { get; set; }
        public int SalesOrderClientNo { get; set; }
        public string? Part { get; set; }
        public int? ManufacturerNo { get; set; }
        public string? DateCode { get; set; }
        public int? PackageNo { get; set; }
        public int Quantity { get; set; }
        public double Price { get; set; }
        [JsonConverter(typeof(ShortDateWithCultureFormatterConverter))]
        public DateTime DatePromised { get; set; }
        [JsonConverter(typeof(ShortDateWithCultureFormatterConverter))]
        [JsonPropertyName("DateRequired")]
        public DateTime RequiredDate { get; set; }
        [JsonPropertyName("ShippingInstructions")]
        public string? Instructions { get; set; }
        public int? ProductNo { get; set; }
        public string? CustomerPart { get; set; }
        public bool ShipASAP { get; set; }
        public byte? ROHS { get; set; }
        [JsonPropertyName("LineNotes")]
        public string? Notes { get; set; }
        public int? UpdatedBy { get; set; }
        public byte? ProductSource { get; set; }
        [JsonConverter(typeof(ShortDateWithCultureFormatterConverter))]
        public DateTime? PODeliveryDate { get; set; }
        [JsonPropertyName("PrintHazardousWarning")]
        public bool PrintHazardous { get; set; }
        [JsonPropertyName("Msl")]
        public string? MSLLevel { get; set; }
        public string? ContractNo { get; set; }
        public int? ECCNNo { get; set; }
        public bool AS6081 { get; set; }
        [JsonPropertyName("Cost")]
        public double? ServiceCostRef { get; set; }
        public int? QuoteLineNo { get; set; }
        public int? ServiceNo { get; set; }
        public int? StockNo { get; set; }
        public int? DocId { get; set; }
        public int? SourcingResultNo { get; set; }
        public string? DocType { get; set; }
        public string? ECCNCode { get; set; }
    }
}