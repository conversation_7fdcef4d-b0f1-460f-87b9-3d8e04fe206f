﻿namespace GlobalTrader2.Aggregator.UseCases.FileUpload.Commands.DeleteSourcingImage
{
    public class DeleteSourcingImageHandler : IRequestHandler<DeleteSourcingImageCommand, BaseResponse<bool>>
    {
        private readonly IBaseRepository<SourcingImage> _repository;
        public DeleteSourcingImageHandler(IBaseRepository<SourcingImage> repository)
        {
            _repository = repository;
        }

        public async Task<BaseResponse<bool>> Handle(DeleteSourcingImageCommand request, CancellationToken cancellationToken)
        {
            var response = new BaseResponse<bool>();

            var sourcingImage = _repository.GetAsync(
                x => x.SourcingImageId == request.SourcingImageId).Result;

            if (sourcingImage == null)
            {
                return response;
            }

            await _repository.DeleteAsync(sourcingImage);

            response.Success = true;
            response.Data = true;

            return response;
        }
    }
}
