﻿using GlobalTrader2.Dto.Datatables;
using GlobalTrader2.Dto.Stock;
using GlobalTrader2.Orders.UserCases.Orders.ItemSearch.GetItemSearchStock;
using System.Globalization;

namespace GlobalTrader2.Orders.UI.Areas.Orders.Controllers
{
    [ApiController]
    [Authorize]
    [Route("api/stocks")]
    public class StockController : ApiBaseController
    {
        private readonly IMediator _mediator;

        public StockController(IMediator mediator)
        {
            _mediator = mediator;
        }

        [HttpPost("item-search")]
        public async Task<IActionResult> GetStockItemSearchAsync([FromBody] GetItemSearchStockRequest request, CancellationToken cancellation)
        {
            var query = new GetItemSearchStockQuery()
            {
                ClientId = request.GlobalLoginClientNo.HasValue && request.GlobalLoginClientNo.Value > 0 ? request.GlobalLoginClientNo.Value : ClientId,
                OrderBy = request.OrderBy,
                SortDir = request.SortDir,
                PageIndex = request.Index / request.Size,
                PageSize = request.Size,
                PartSearch = request.PartSearch != null ? StringHelper.RemovePunctuationRetainingPercentSigns(request.PartSearch) : null,
                LocationSearch = request.LocationSearch,
                PoNoHi = request.PoNoHi,
                PoNoLo = request.PoNoLo,
                Warehouse = request.Warehouse,
            };

            var result = await _mediator.Send(query, cancellation);

            var totalItems = result.Data?.FirstOrDefault()?.RowCnt ?? 0;

            var cultureInfo = new CultureInfo(Culture);

            foreach (var item in result.Data ?? Enumerable.Empty<GetItemSearchStockDto>())
            {
                item.FormatedLandedCost = Functions.FormatCurrency(item.LandedCost, cultureInfo, ClientCurrencyCode ?? string.Empty, 2, false);
            }

            var response = new DatatableResponse<IEnumerable<GetItemSearchStockDto>>()
            {
                Success = result.Success,
                Data = result.Data,
                Draw = request.Draw,
                RecordsTotal = totalItems,
                RecordsFiltered = totalItems,
            };

            return Ok(response);
        }
    }
}
