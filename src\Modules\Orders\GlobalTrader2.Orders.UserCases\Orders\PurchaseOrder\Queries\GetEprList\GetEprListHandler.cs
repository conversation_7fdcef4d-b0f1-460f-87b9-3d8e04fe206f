﻿namespace GlobalTrader2.Orders.UserCases.Orders.PurchaseOrder.Queries.GetEprList;

public class GetEprListHandler : IRequestHandler<GetEprListQuery, BaseResponse<List<Epr>>>
{
    private readonly IBaseRepository<Epr> _eprRepository;

    public GetEprListHandler(IBaseRepository<Epr> eprRespository)
    {
        _eprRepository = eprRespository;
    }

    public async Task<BaseResponse<List<Epr>>> Handle(GetEprListQuery request, CancellationToken cancellationToken)
    {
        var response = new BaseResponse<List<Epr>>();

        var orderBy = request.OrderBy?.ToLower() switch
        {
            "desc" => new Func<IQueryable<Epr>, IOrderedQueryable<Epr>>(q => q.OrderByDescending(x => x.DLUP)),
            _ => null
        };

        var result = await _eprRepository.ListAsync(
            filter: x => x.PurchaseOrderId == request.PurchaseOrderId, 
            orderBy: orderBy);

        if (result is null)
            throw new ArgumentException($"Cannot find list of EPR with the purchase order id {request.PurchaseOrderId}");

        response.Data = result.ToList();
        response.Success = true;

        return response;
    }
}
