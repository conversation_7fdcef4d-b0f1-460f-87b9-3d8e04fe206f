using GlobalTrader2.Orders.UseCases.Orders.POLine.Queries.GetPurchaseOrderLineAllocations;
using GlobalTrader2.Orders.UseCases.Orders.POLine.Queries.GetPurchaseOrderLineDetails;

namespace GlobalTrader2.Orders.UI.Areas.Orders.Controllers
{
    [ApiController]
    [Authorize]
    [Route("api/orders/purchase-orders/po-lines")]
    public class PurchaseOrderLinesController : ApiBaseController
    {
        private readonly IMediator _mediator;
        private readonly SessionManager _sessionManager;

        public PurchaseOrderLinesController(IMediator mediator, SessionManager sessionManager)
        {
            _mediator = mediator;
            _sessionManager = sessionManager;
        }

        [HttpGet("{purchaseOrderLineId}")]
        public async Task<IActionResult> GetPurchaseOrderLineDetails([FromRoute]int purchaseOrderLineId)
        {
            var result = await _mediator.Send(new GetPurchaseOrderLineDetailsQuery(purchaseOrderLineId));
            return Ok(result);
        }

        [HttpGet("{purchaseOrderLineId}/allocations")]
        public async Task<IActionResult> GetAllocationsByPoLineId([FromRoute] int purchaseOrderLineId)
        {
            var result = await _mediator.Send(new GetPurchaseOrderLineAllocationsQuery(purchaseOrderLineId, _sessionManager.IsPOHub, _sessionManager.ClientCurrencyCode));
            return Ok(result);
        }
    }
}