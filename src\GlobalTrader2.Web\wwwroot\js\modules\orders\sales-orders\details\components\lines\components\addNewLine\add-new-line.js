﻿import { StepperComponent } from "../../../../../../../../components/stepper/stepper.component.js?v=#{BuildVersion}#";
import { AddNewLineStep2Manager } from "./add-new-line-step-2.js?v=#{BuildVersion}#";
import { AddNewLineStep3Manager } from "./add-new-line-step-3.js?v=#{BuildVersion}#";
import { AddNewLineStep4Manager } from "./add-new-line-step-4.js?v=#{BuildVersion}#";
import { AddLineDetailModel } from '../../models/add-line-detail.model.js';
import { FromSourceTypeConstant } from '../../configs/source-type.config.js';

export class AddNewLineManager {
    constructor({ soGeneralInfo }) {
        this.stepper = null;
        this.selectedSource = "";
        this.addNewLineStep2Manager = null;
        this.addNewLineStep3Manager = null;
        this.addNewLineStep4Manager = null;
        this.dataForBinding = null;
        this.$dialog = $("#add-lines-dialog");
        this.setDefaultSelectedSource();
        this._soGeneralInfo = soGeneralInfo;
        this._creditData = null;
        this._isInitialized = false;
    }

    initialize() {
        if (this._isInitialized) return;
        this.setupDialog();
        this.initStepper();
        this.eventRegister();
        this._isInitialized = true;
    }

    setDefaultSelectedSource() {
        const $firstSource = $('#form-add-so-line-step1 input[type="radio"]').first();
        if ($firstSource.length && $firstSource.val() !== null && $firstSource.val() !== undefined) {
            $firstSource.prop('checked', true);
            this.selectedSource = $firstSource.val();
        }
    }

    initStepper() {
        this.stepper = new StepperComponent(document.getElementById('add-lines-stepper'), [
            { title: `Select Source`, clickToMove: true },
            { title: `Select Item`, clickToMove: true },
            { title: `Edit Details`, clickToMove: false },
            { title: `Lot Details`, clickToMove: false },
        ]);
        this.stepper.on("stepChange.ms", ({ step }) => {
            if (window.currentXhr && window.currentXhr.status != 0) {
                window.currentXhr.abort();
                window.currentXhr = undefined;
            }
            this.enableSaveButton(this.stepper.currentStep);
            this.enableContinueButton(this.stepper.currentStep);
            if (this.addNewLineStep3Manager != null) {
                this.addNewLineStep3Manager.onFormClose();
            }
            $('#add-lines-stepper .step-arrow[data-step="2"], #add-lines-stepper .step-arrow[data-step="3"]').removeClass('pe-none');
            if (this.selectedSource == FromSourceTypeConstant.NEW) {
                if (step == 1 || step == 3) {
                    $('#add-lines-stepper .step-arrow[data-step="2"]').removeClass('completed');
                    $('#add-lines-stepper .step-arrow[data-step="2"]').addClass('pe-none');
                }
            }
            else if (this.selectedSource == FromSourceTypeConstant.NEWLOT) {
                if (step == 1 || step == 4) {
                    $('#add-lines-stepper .step-arrow[data-step="2"], #add-lines-stepper .step-arrow[data-step="3"]').removeClass('completed');
                    $('#add-lines-stepper .step-arrow[data-step="2"], #add-lines-stepper .step-arrow[data-step="3"]').addClass('pe-none');
                }
            }
        });
        this.stepper.trigger("stepChange.ms", { step: this.stepper.currentStep });
    }

    enableSaveButton(currentStep) {
        $("#save-so-line-btn").prop('disabled', currentStep == 1 || currentStep == 2);
    }

    enableContinueButton(currentStep) {
        $("#continue-so-line-btn").toggleClass('d-none', currentStep == 2 || currentStep == 3);
    }

    async fillDataStep3(sourceSelected, sourceItemId) {
        let getSourceDataPromise = null;
        switch (sourceSelected) {
            case FromSourceTypeConstant.CUSREQ:
                getSourceDataPromise = this.GetRequirementLineForNew(sourceItemId);
                break;
            case FromSourceTypeConstant.SOURCINGRESULT:
                getSourceDataPromise = this.GetSourcingResultForNew(sourceItemId);
                break;
            case FromSourceTypeConstant.QUOTE:
                getSourceDataPromise = this.GetQuoteLineForNew(sourceItemId);
                break;
            case FromSourceTypeConstant.SO:
                getSourceDataPromise = this.GetSalesOrderLineForNew(sourceItemId);
                break;
            case FromSourceTypeConstant.STOCK:
                getSourceDataPromise = this.GetStockLineForNew(sourceItemId);
                break;
            case FromSourceTypeConstant.SERVICE:
                getSourceDataPromise = this.GetServiceLineForNew(sourceItemId);
                break;
            default:
                break;
        }
        this.addNewLineStep3Manager.showForm(this._soGeneralInfo, getSourceDataPromise, sourceSelected);
    }

    async GetRequirementLineForNew(sourceItemId) {
        const requirementLine = await GlobalTrader.ApiClient.getAsync(`/orders/sales-order/so-lines/customer-requirements/${sourceItemId}`,
            {
                SOCurrencyID: this._soGeneralInfo.CurrencyNo,
                SOCurrencyCode: this._soGeneralInfo.CurrencyCode,
                SODate: this._soGeneralInfo.SODateOrdered
            }
        );
        this.dataForBinding = new AddLineDetailModel();
        if (!requirementLine.success || !requirementLine.data) {
            return this.dataForBinding;
        }
        this.dataForBinding.SourceItemId = sourceItemId;
        this.dataForBinding.Part = requirementLine.data.part;
        this.dataForBinding.DateCode = GlobalTrader.StringHelper.setCleanTextValue(requirementLine.data.dateCode);
        this.dataForBinding.Price = requirementLine.data.price;
        this.dataForBinding.Quantity = requirementLine.data.quantity;
        this.dataForBinding.Manufacturer = requirementLine.data.manufacturerName;
        this.dataForBinding.ManufacturerNo = requirementLine.data.manufacturerNo;
        this.dataForBinding.ProductNo = requirementLine.data.productNo;
        this.dataForBinding.Product = requirementLine.data.productDescription;
        this.dataForBinding.PackageNo = requirementLine.data.packageNo;
        this.dataForBinding.Package = requirementLine.data.packageDescription;
        this.dataForBinding.ROHS = requirementLine.data.rohs;
        this.dataForBinding.LineNotes = requirementLine.data.requirementNotes;
        this.dataForBinding.ShippingInstructions = requirementLine.data.requirementNotes;
        this.dataForBinding.Msl = requirementLine.data.msl;
        this.dataForBinding.EccnCode = requirementLine.data.eccnCode;
        this.dataForBinding.AS6081 = requirementLine.data.aS6081;
        return this.dataForBinding;
    }

    async GetSourcingResultForNew(sourceItemId) {
        const sourcingResult = await GlobalTrader.ApiClient.getAsync(`/orders/sales-order/so-lines/sourcing-results/${sourceItemId}`,
            {
                SOCurrencyID: this._soGeneralInfo.CurrencyNo,
                SOCurrencyCode: this._soGeneralInfo.CurrencyCode,
                SODate: this._soGeneralInfo.SODateOrdered
            }
        );
        this.dataForBinding = new AddLineDetailModel();
        if (!sourcingResult.success || !sourcingResult.data) {
            return this.dataForBinding;
        }
        this.dataForBinding.SourceItemId = sourceItemId;
        this.dataForBinding.Part = sourcingResult.data.part;
        this.dataForBinding.CustomerPart = sourcingResult.data.customerPart;
        this.dataForBinding.DateCode = sourcingResult.data.dateCode;
        this.dataForBinding.Price = sourcingResult.data.formatPrice;
        this.dataForBinding.Quantity = sourcingResult.data.quantity;
        this.dataForBinding.Manufacturer = sourcingResult.data.manufacturerName;
        this.dataForBinding.ManufacturerNo = sourcingResult.data.manufacturerNo;
        this.dataForBinding.ProductNo = sourcingResult.data.productNo;
        this.dataForBinding.Product = sourcingResult.data.productDescription;
        this.dataForBinding.PackageNo = sourcingResult.data.packageNo;
        this.dataForBinding.Package = sourcingResult.data.packageDescription;
        this.dataForBinding.ROHS = sourcingResult.data.rohs;
        this.dataForBinding.ProductSource = sourcingResult.data.productSource;
        this.dataForBinding.PoDeliveryDate = sourcingResult.data.poDeliveryDate;
        this.dataForBinding.IsIPO = sourcingResult.data.isIPO;
        this.dataForBinding.LineNotes = sourcingResult.data.sourcingNotes;
        this.dataForBinding.ShippingInstructions = sourcingResult.data.sourcingNotes;
        this.dataForBinding.Msl = sourcingResult.data.mslLevel;
        this.dataForBinding.AS6081 = sourcingResult.data.aS6081;
        return this.dataForBinding;
    }

    async GetQuoteLineForNew(sourceItemId) {
        const quoteLineDetail = await GlobalTrader.ApiClient.getAsync(`/orders/sales-order/so-lines/quote-lines/${sourceItemId}`,
            {
                SOCurrencyID: this._soGeneralInfo.CurrencyNo,
                SOCurrencyCode: this._soGeneralInfo.CurrencyCode,
                SODate: this._soGeneralInfo.SODateOrdered
            }
        );
        this.dataForBinding = new AddLineDetailModel();
        if (!quoteLineDetail.success || !quoteLineDetail.data) {
            return this.dataForBinding;
        }
        this.dataForBinding.SourceItemId = sourceItemId;
        this.dataForBinding.Part = quoteLineDetail.data.part;
        this.dataForBinding.CustomerPart = quoteLineDetail.data.customerPart;
        this.dataForBinding.DateCode = quoteLineDetail.data.dateCode;
        this.dataForBinding.Price = quoteLineDetail.data.price;
        this.dataForBinding.Quantity = quoteLineDetail.data.quantity;
        this.dataForBinding.Manufacturer = quoteLineDetail.data.manufacturerName;
        this.dataForBinding.ManufacturerNo = quoteLineDetail.data.manufacturerNo;
        this.dataForBinding.ProductNo = quoteLineDetail.data.productNo;
        this.dataForBinding.Product = quoteLineDetail.data.productDescription;
        this.dataForBinding.PackageNo = quoteLineDetail.data.packageNo;
        this.dataForBinding.Package = quoteLineDetail.data.packageDescription;
        this.dataForBinding.ROHS = quoteLineDetail.data.rohs;
        this.dataForBinding.ProductSource = quoteLineDetail.data.productSource;
        this.dataForBinding.PoDeliveryDate = quoteLineDetail.data.poDelDate;
        this.dataForBinding.IsIPO = quoteLineDetail.data.isIPO;
        this.dataForBinding.IsIPOExist = quoteLineDetail.data.isIPOExist;
        this.dataForBinding.LineNotes = quoteLineDetail.data.lineNotes;
        this.dataForBinding.ShippingInstructions = quoteLineDetail.data.lineNotes;
        this.dataForBinding.Msl = quoteLineDetail.data.msl;
        this.dataForBinding.AS6081 = quoteLineDetail.data.aS6081;
        this.dataForBinding.SourcingResultNo = quoteLineDetail.data.sourcingResultNo;
        return this.dataForBinding;
    }

    async GetSalesOrderLineForNew(sourceItemId) {

        const salesOrderLineDetail = await GlobalTrader.ApiClient.getAsync(`orders/sales-order/so-lines/${sourceItemId}/info-for-add`,
            {
                SOCurrencyID: this._soGeneralInfo.CurrencyNo,
                SOCurrencyCode: this._soGeneralInfo.CurrencyCode,
                SODate: this._soGeneralInfo.SODateOrdered
            }
        );
        this.dataForBinding = new AddLineDetailModel();
        if (!salesOrderLineDetail.success || !salesOrderLineDetail.data) {
            return this.dataForBinding;
        }
        this.dataForBinding.SourceItemId = sourceItemId;
        this.dataForBinding.Part = salesOrderLineDetail.data.part;
        this.dataForBinding.DateCode = salesOrderLineDetail.data.dateCode;
        this.dataForBinding.CustomerPart = salesOrderLineDetail.data.customerPart;
        this.dataForBinding.Price = salesOrderLineDetail.data.price;
        this.dataForBinding.Quantity = salesOrderLineDetail.data.quantity;
        this.dataForBinding.Manufacturer = salesOrderLineDetail.data.manufacturerName;
        this.dataForBinding.ManufacturerNo = salesOrderLineDetail.data.manufacturerNo;
        this.dataForBinding.Product = salesOrderLineDetail.data.productDescription;
        this.dataForBinding.ProductNo = salesOrderLineDetail.data.productNo;
        this.dataForBinding.PackageNo = salesOrderLineDetail.data.packageNo;
        this.dataForBinding.Package = salesOrderLineDetail.data.packageDescription;
        this.dataForBinding.ROHS = salesOrderLineDetail.data.rohs;
        this.dataForBinding.ProductSource = salesOrderLineDetail.data.productSource;
        this.dataForBinding.PoDeliveryDate = salesOrderLineDetail.data.poDeliveryDate;
        this.dataForBinding.IsIPO = salesOrderLineDetail.data.isIPO;
        this.dataForBinding.IsIPOExist = salesOrderLineDetail.data.isIPOCreated;
        this.dataForBinding.Msl = salesOrderLineDetail.data.mslLevel;
        this.dataForBinding.AS6081 = salesOrderLineDetail.data.aS6081;
        this.dataForBinding.EccnCode = salesOrderLineDetail.data.eccnCode;
        return this.dataForBinding;
    }

    async GetStockLineForNew(sourceItemId) {

        const stockDetail = await GlobalTrader.ApiClient.getAsync(`orders/sales-order/so-lines/stocks/${sourceItemId}`,
            {
                SOCurrencyID: this._soGeneralInfo.CurrencyNo,
                SOCurrencyCode: this._soGeneralInfo.CurrencyCode,
                SODate: this._soGeneralInfo.SODateOrdered
            }
        );
        this.dataForBinding = new AddLineDetailModel();
        if (!stockDetail.success || !stockDetail.data) {
            return this.dataForBinding;
        }
        this.dataForBinding.SourceItemId = sourceItemId;
        this.dataForBinding.Part = stockDetail.data.part;
        this.dataForBinding.DateCode = stockDetail.data.dateCode;
        this.dataForBinding.Price = stockDetail.data.price;
        this.dataForBinding.LandedCost = stockDetail.data.landedCostString;
        this.dataForBinding.Quantity = stockDetail.data.quantityAvailable;
        this.dataForBinding.Manufacturer = stockDetail.data.manufacturerName;
        this.dataForBinding.ManufacturerNo = stockDetail.data.manufacturerNo;
        this.dataForBinding.Product = stockDetail.data.productDescription;
        this.dataForBinding.ProductNo = stockDetail.data.productNo;
        this.dataForBinding.PackageNo = stockDetail.data.packageNo;
        this.dataForBinding.Package = stockDetail.data.packageDescription;
        this.dataForBinding.ROHS = stockDetail.data.rohs;
        this.dataForBinding.Msl = stockDetail.data.mslLevel;
        this.dataForBinding.AS6081 = stockDetail.data.aS6081;
        return this.dataForBinding;
    }
    async GetServiceLineForNew(sourceItemId) {
        const serviceDetail = await GlobalTrader.ApiClient.getAsync(`orders/sales-order/so-lines/services/${sourceItemId}/`,
            {
                SOCurrencyID: this._soGeneralInfo.CurrencyNo,
                SOCurrencyCode: this._soGeneralInfo.CurrencyCode,
                SODate: this._soGeneralInfo.SODateOrdered
            }
        );
        this.dataForBinding = new AddLineDetailModel();
        if (!serviceDetail.success || !serviceDetail.data) {
            return this.dataForBinding;
        }
        this.dataForBinding.SourceItemId = sourceItemId;
        this.dataForBinding.ServiceDescription = serviceDetail.data.serviceDescription;
        this.dataForBinding.ServiceName = serviceDetail.data.serviceName;
        this.dataForBinding.Price = serviceDetail.data.priceString;
        this.dataForBinding.Quantity = serviceDetail.data.quantity;
        this.dataForBinding.Msl = serviceDetail.data.msl;
        this.dataForBinding.Cost = serviceDetail.data.costString;
        this.dataForBinding.ClientCurrency = serviceDetail.data.clientCurrencyCode;
        this.dataForBinding.AS6081 = false;
        return this.dataForBinding;
    }

    setCreditData(creditData) {
        this._creditData = creditData;
    }

    eventRegister() {
        $('input:radio[name=addSoLineSource]').click((event) => {
            this.selectedSource = event.target.value;
        });
    }
    setupDialog() {
        this.$dialog.dialog({
            maxHeight: $(window).height(),
            width: "80vw",
            close: () => {
                if (this.addNewLineStep3Manager != null) {
                    this.addNewLineStep3Manager.onFormClose();
                }
                this.selectedSource = this.setDefaultSelectedSource();
                this.stepper.updateStepper(1);
                this.$dialog.dialog("close");
            },
            buttons: [
                {
                    text: window.localizedStrings.save,
                    id: 'save-so-line-btn',
                    class: 'btn btn-primary fw-normal',
                    html: `<img src="/img/icons/save.svg" alt="${window.localizedStrings.save}">${window.localizedStrings.save}`,
                    click: async () => {
                        if (this.addNewLineStep3Manager?.validateForm()) {
                            this.$dialog.find(".form-error-summary").hide();
                            this.$dialog.parent().find(`.ui-dialog-buttonset button:has(img[alt="${window.localizedStrings.save}"])`).prop('disabled', true);
                            this.addNewLineStep3Manager?.setLoading(true);

                            const newSalesOrderLineIdResponse = await this.addNewLineStep3Manager.submitForm();
                            this.$dialog.parent().find(`.ui-dialog-buttonset button:has(img[alt="${window.localizedStrings.save}"])`).prop('disabled', false);
                            this.addNewLineStep3Manager?.setLoading(false);

                            if (!newSalesOrderLineIdResponse.success || !newSalesOrderLineIdResponse.data) {
                                showToast('danger', newSalesOrderLineIdResponse?.message || 'Bad Request');
                                return;
                            }
                            this.$dialog.trigger("addNewLineSuccess", newSalesOrderLineIdResponse.data);


                            this.$dialog.dialog("close");
                            showToast('success', window.localizedStrings.saveChangedMessage);

                            if (this.successCallback) this.successCallback();
                        }
                        else {
                            this.$dialog.find(".form-error-summary").show();
                        }
                    }
                },
                {
                    text: window.localizedStrings.cancel,
                    class: 'btn btn-danger fw-normal',
                    html: `<img src="/img/icons/slash.svg" alt="${window.localizedStrings.cancel}">${window.localizedStrings.cancel}`,
                    click: function () {
                        $(this).dialog("close");
                    },
                },
                {
                    text: window.localizedStrings.continue,
                    id: 'continue-so-line-btn',
                    class: 'btn btn-primary',
                    html: `<img src="/img/icons/arrow-right.svg" alt="Continue icon"/>${window.localizedStrings.continue}`,
                    click: () => {
                        this.continueClicked();
                    },
                },
            ]
        });
    }

    continueClicked() {
        switch (this.selectedSource) {
            case FromSourceTypeConstant.NEW:
                this.stepper.updateStepper(3);
                if (this.addNewLineStep3Manager == null) {
                    this.addNewLineStep3Manager = new AddNewLineStep3Manager(this._soGeneralInfo);
                    this.addNewLineStep3Manager.initialize();
                }
                this.dataForBinding = new AddLineDetailModel();
                this.dataForBinding.SourceSelected = FromSourceTypeConstant.NEW;
                this.addNewLineStep3Manager.showForm(this._soGeneralInfo, null, FromSourceTypeConstant.NEW);
                break;
            case FromSourceTypeConstant.CUSREQ:
            case FromSourceTypeConstant.QUOTE:
            case FromSourceTypeConstant.SO:
            case FromSourceTypeConstant.STOCK:
            case FromSourceTypeConstant.SERVICE:
                this.stepper.updateStepper(2);
                if (this.addNewLineStep2Manager == null) {
                    this.addNewLineStep2Manager = new AddNewLineStep2Manager();
                    this.addNewLineStep2Manager.initialize();
                    this.addNewLineStep2Manager.loadSection(this.selectedSource);
                    this.addNewLineStep2Manager.on('continue.clicked', (data) => {
                        this.stepper.onNextStep();
                        if (this.addNewLineStep3Manager == null) {
                            this.addNewLineStep3Manager = new AddNewLineStep3Manager(this._soGeneralInfo);
                            this.addNewLineStep3Manager.initialize();
                        }
                        this.fillDataStep3(data.sourceSelected, data.sourceItemId);
                    });
                }
                else {
                    this.addNewLineStep2Manager.loadSection(this.selectedSource);
                }
                break;
            case FromSourceTypeConstant.NEWLOT:
                this.stepper.updateStepper(4);
                if (this.addNewLineStep4Manager == null) {
                    this.addNewLineStep4Manager = new AddNewLineStep4Manager();
                    this.addNewLineStep4Manager.initialize();
                }
                break;
        }
    }

    openDialog() {
        this.$dialog.dialog("open");
        this.handleDialogOpen();
    }

    async handleDialogOpen() {
        this.$dialog.dialog("setLoading", true);
        this.$dialog.dialog("setLoading", false);
    }
}