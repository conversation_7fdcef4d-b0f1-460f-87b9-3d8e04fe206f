
using GlobalTrader2.Orders.UserCases.Orders.SOLine.Commands.EditSalesOrderLine;

namespace GlobalTrader2.Orders.UserCases.Test.Orders.SOLine.Commands.EdtiAllSalesOrderLines;

public class EditAllSalesOrderLinesHandlerTests
{
    [Fact]
    public async Task Handle_Should_Return_Success_Response_With_Result()
    {
        // Arrange
        var mockRepo = new Mock<IBaseRepository<AffectedRows>>();
        mockRepo.Setup(r => r.ExecuteSqlRawAsync(It.IsAny<string>(), It.IsAny<object[]>()))
                .ReturnsAsync(5); 

        var handler = new EditAllSalesOrderLinesHandler(mockRepo.Object);

        var request = new EditAllSalesOrderLinesCommand
        {
            SalesOrderNo = 1,
            DatePromised = DateTime.Today,
            UpdatedBy = 100,
            IsReasonChanged = true,
            PromiseReasonNo = 10
        };

        // Act
        var result = await handler.Handle(request, CancellationToken.None);

        // Assert
        Assert.True(result.Success);
        Assert.Equal(5, result.Data);
    }
}