using GlobalTrader2.Contacts.UseCases.Companies.Queries.Details.GetGSAInfo;
using GlobalTrader2.Dto.Contact;
using GlobalTrader2.Dto.SalesOrderLine;
using GlobalTrader2.Orders.UserCases.Orders.BOM.CompanyAdvisoryNote.CompanyAdvisoryNote.Queries;
using GlobalTrader2.Orders.UserCases.Orders.PurchaseOrder.Queries.GetPurchaseOrderForPage;
using GlobalTrader2.SharedUI;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Localization;

namespace GlobalTrader2.Orders.UI.Areas.Orders.Pages.PurchaseOrder.Details
{
    [SectionLevelAuthorize(SecurityFunction.OrdersSection_View)]
    public class IndexModel : BasePageModel
    {
        private readonly SessionManager _sessionManager;
        private readonly SettingManager _settingManager;
        private readonly IMediator _mediator;
        private readonly IStringLocalizer<Status> _statusLocalizer;



        [FromQuery(Name = "po")]
        public int? PurchaseOrderId { get; set; }
        public bool IsDifferentClient { get; set; }
        public bool IsGlobalSalesAccessMember { get; set; }
        public bool IsGSAEditPermission { get; set; } 
        public PurchaseOrderMainInfoPermissions PurchaseOrderMainInfoPermissions { get; set; } = new PurchaseOrderMainInfoPermissions();
        public PurchaseOrderGeneralInfo PurchaseOrderGeneralInfo { get; set; } = new PurchaseOrderGeneralInfo();
        public LinesSectionViewModel LinesSectionViewModel { get; set; } = new();
        public IndexModel(SessionManager sessionManager, IMediator mediator, SettingManager settingManager, SecurityManager securityManager, IHttpContextAccessor httpContextAccessor, IStringLocalizer<Status> statusLocalizer) : base(securityManager)
        {
            _sessionManager = sessionManager;
            _mediator = mediator;
            _statusLocalizer = statusLocalizer;
            _settingManager = settingManager;
            SiteSection = SiteSection.Orders;
            PageType = SitePage.Orders_PurchaseOrderDetail;
        }

        public async Task<IActionResult> OnGetAsync()
        {
            if (PurchaseOrderGeneralInfo.PurchaseOrderId <= 0) return Redirect(V2Paths.NotFound);
            if (!await CanViewPageAsync()) return Redirect(V2Paths.NotFound);
            AddBreadCrumbs();
            await _settingManager.UpdateRecentlyView(BreadCrumb, Request.QueryString.Value ?? string.Empty);
            var isGSA = !_sessionManager.IsGlobalUser && _sessionManager.IsGSA;
            var isGSAViewingOtherClient = isGSA && IsDifferentClient;
            SetUpLineSectionPermissions(isGSAViewingOtherClient);
            SetupMainInfoSection();
            return Page();
        }

        private void AddBreadCrumbs()
        {
            BreadCrumb.Add(Navigations.Home);
            BreadCrumb.Add(Navigations.Orders);
            BreadCrumb.Add(Navigations.PurchaseOrders);
            BreadCrumb.Add(Navigations.PurchaseOrderDetail(PurchaseOrderGeneralInfo.PurchaseOrderNumber.ToString()));
        }

        private async Task<bool> CanViewPageAsync()
        {
            if (_securityManager == null) return false;
            if (!_securityManager.CheckSectionLevelPermission(SecurityFunction.Orders_PurchaseOrder_View) && !_sessionManager.IsGSA) return false;

            if (PurchaseOrderGeneralInfo.ClientNo != _sessionManager.ClientID && IsGlobalSalesAccessMember)
            {
                IsGSAEditPermission = !_sessionManager.IsGSAViewPermission;
                return true;
            }
            if (_sessionManager.IsGlobalUser) return true;

            var tabPermission = await GetTabSecurityFunctionPermission(SecurityFunction.Orders_PurchaseOrders_View);
            var isTabAllow = CheckTabSecurityInDetailPage(new TabSecurityInDetailPageRequest
            {
                ListTabs = tabPermission.Where(x => x.Value).Select(x => (int)x.Key).ToList(),
                TeamID = PurchaseOrderGeneralInfo.TeamNo.GetValueOrDefault(),
                LoginTeamID = _sessionManager.LoginTeamID.GetValueOrDefault(),
                DivisionID = PurchaseOrderGeneralInfo.DivisionNo,
                LoginDivisionID = _sessionManager.LoginDivisionID.GetValueOrDefault(),
                ClientID = PurchaseOrderGeneralInfo.ClientNo,
                LoginClientID = _sessionManager.ClientID.GetValueOrDefault(),
                LoginID = PurchaseOrderGeneralInfo.Buyer,
                LoginUserID = _sessionManager.LoginID.GetValueOrDefault(),
            });

            if (!isTabAllow)
            {
                return false;
            }
            return true;


        }

        public override async Task OnPageHandlerExecutionAsync(PageHandlerExecutingContext context, PageHandlerExecutionDelegate next)
        {
            await SetupPurchaseOrderGeneralInfo();
            if (IsDifferentClient && !(_sessionManager.IsGlobalUser && PurchaseOrderGeneralInfo.IPOClientNo > 0))
            {
                IsDataHasOtherClient = true;
            }

            await base.OnPageHandlerExecutionAsync(context, next);
        }

        private async Task SetupPurchaseOrderGeneralInfo()
        {
            if (PurchaseOrderId == null || PurchaseOrderId <= 0) return;
            var purchaseOrderForPage = await _mediator.Send(new GetPurchaseOrderForPageQuery(PurchaseOrderId.Value));
            if (purchaseOrderForPage.Success && purchaseOrderForPage.Data != null)
            {
                IsDifferentClient = purchaseOrderForPage.Data.ClientNo != _sessionManager.ClientID;
                var getCompanyAdvisoryNote = await _mediator.Send(new GetCompanyAdvisoryNoteQuery { Id = purchaseOrderForPage.Data.CompanyNo.GetValueOrDefault() }, CancellationToken.None);

                var po = purchaseOrderForPage.Data;
                PurchaseOrderGeneralInfo.PurchaseOrderId = po.PurchaseOrderId;
                PurchaseOrderGeneralInfo.PurchaseOrderNumber = po.PurchaseOrderNumber.GetValueOrDefault();
                PurchaseOrderGeneralInfo.ClientNo = po.ClientNo.GetValueOrDefault();
                PurchaseOrderGeneralInfo.CompanyNo = po.CompanyNo.GetValueOrDefault();
                PurchaseOrderGeneralInfo.StatusNo = po.StatusNo.GetValueOrDefault();
                PurchaseOrderGeneralInfo.Status = _statusLocalizer[((PurchaseOrderStatus)po.StatusNo.GetValueOrDefault()).ToString()];
                PurchaseOrderGeneralInfo.Closed = po.Closed.GetValueOrDefault();
                PurchaseOrderGeneralInfo.CompanyName = po.CompanyName ?? string.Empty;
                PurchaseOrderGeneralInfo.CompanyNameForSearch = po.CompanyNameForSearch ?? string.Empty;
                PurchaseOrderGeneralInfo.IsPDFAvailable = po.IsPDFAvailable.GetValueOrDefault();
                PurchaseOrderGeneralInfo.TeamNo = po.TeamNo.GetValueOrDefault();
                PurchaseOrderGeneralInfo.DivisionNo = po.DivisionNo.GetValueOrDefault();
                PurchaseOrderGeneralInfo.Buyer = po.Buyer.GetValueOrDefault();
                PurchaseOrderGeneralInfo.IPOClientNo = po.IPOClientNo.GetValueOrDefault();
                PurchaseOrderGeneralInfo.IsPOHub = po.IsPOHub.GetValueOrDefault();
                PurchaseOrderGeneralInfo.ClientName = po.ClientName ?? string.Empty;
                PurchaseOrderGeneralInfo.ClientBaseCurrencyCode = po.ClientBaseCurrencyCode ?? string.Empty;
                PurchaseOrderGeneralInfo.IsPORPDFAvailable = po.IsPORPDFAvailable.GetValueOrDefault();
                PurchaseOrderGeneralInfo.ClientNameVisible = ( IsDifferentClient || PurchaseOrderGeneralInfo.IPOClientNo > 0);
       

                if (getCompanyAdvisoryNote.Success && getCompanyAdvisoryNote.Data != null)
                {
                    PurchaseOrderGeneralInfo.AdvisoryNotes = getCompanyAdvisoryNote.Data;
                }
                if (PurchaseOrderGeneralInfo.ClientNo != _sessionManager.ClientID)
                {
                    var getGSAInfo = await _mediator.Send(new GetGsaInfoQuery(_sessionManager.ClientID.GetValueOrDefault(), _sessionManager.LoginID.GetValueOrDefault(), PurchaseOrderGeneralInfo.CompanyNo));

                    if (getGSAInfo.Success && getGSAInfo.Data != null)
                    {
                        IsGlobalSalesAccessMember = getGSAInfo.Data.IsGsa;
                    }
                }
            }
        }

        private void SetupMainInfoSection()
        {
            if (_securityManager != null)
            {
                PurchaseOrderMainInfoPermissions.CanEdit = _securityManager.CheckSectionLevelPermission(SecurityFunction.Orders_PurchaseOrder_MainInfo_Edit);
                PurchaseOrderMainInfoPermissions.CanClose = _securityManager.CheckSectionLevelPermission(SecurityFunction.Orders_PurchaseOrder_MainInfo_Close);
                PurchaseOrderMainInfoPermissions.CanApprove = _securityManager.CheckSectionLevelPermission(SecurityFunction.Orders_PurchaseOrder_MainInfo_Approve);
                PurchaseOrderMainInfoPermissions.CanDisapprove = _securityManager.CheckSectionLevelPermission(SecurityFunction.Orders_PurchaseOrder_MainInfo_Disapprove);

                if (!HttpContext.Session.Get<bool>(SessionKey.IsGlobalUser) && IsDifferentClient && _sessionManager.IsGSA)
                {
                    PurchaseOrderMainInfoPermissions.CanEdit = IsGSAEditPermission;
                    PurchaseOrderMainInfoPermissions.CanClose = IsGSAEditPermission;
                    PurchaseOrderMainInfoPermissions.CanApprove = IsGSAEditPermission;
                    PurchaseOrderMainInfoPermissions.CanDisapprove = IsGSAEditPermission;
                    PurchaseOrderMainInfoPermissions.CanNotify = IsGSAEditPermission;
                }
            }

        }

        private void SetUpLineSectionPermissions(bool isOtherGSA)
        {
            if (isOtherGSA)
            {
                LinesSectionViewModel.IsReadOnly = true;
                return;
            }

            if (_securityManager != null)
            {
                LinesSectionViewModel.CanAdd = _securityManager.CheckPagePermission(SecurityFunction.Orders_PurchaseOrder_Lines_Add);
                LinesSectionViewModel.CanEdit = _securityManager.CheckPagePermission(SecurityFunction.Orders_PurchaseOrder_Lines_Edit);
                LinesSectionViewModel.CanPost = _securityManager.CheckPagePermission(SecurityFunction.Orders_PurchaseOrder_Lines_Post);
                LinesSectionViewModel.CanUnpost = _securityManager.CheckPagePermission(SecurityFunction.Orders_PurchaseOrder_Lines_Unpost);
                LinesSectionViewModel.CanDelete = _securityManager.CheckPagePermission(SecurityFunction.Orders_PurchaseOrder_Lines_Delete);
                LinesSectionViewModel.CanClose = _securityManager.CheckPagePermission(SecurityFunction.Orders_PurchaseOrder_Lines_Close);
                LinesSectionViewModel.CanViewEpr = !isOtherGSA;
            }
        }
    }
    public class PurchaseOrderGeneralInfo
    {
        public int PurchaseOrderId { get; set; }
        public int PurchaseOrderNumber { get; set; }
        public int ClientNo { get; set; }
        public int CompanyNo { get; set; }
        public int? StatusNo { get; set; }
        public string Status { get; set; } = string.Empty;
        public bool Closed { get; set; }
        public string CompanyName { get; set; } = string.Empty;
        public string CompanyNameForSearch { get; set; } = string.Empty;
        public bool IsPDFAvailable { get; set; }
        public int? TeamNo { get; set; }
        public int DivisionNo { get; set; }
        public int Buyer { get; set; }
        public int IPOClientNo { get; set; }
        public bool IsPOHub { get; set; }
        public string ClientName { get; set; } = string.Empty;
        public string ClientBaseCurrencyCode { get; set; } = string.Empty;
        public bool IsPORPDFAvailable { get; set; }
        public string? AdvisoryNotes { get; set; }
        public bool ClientNameVisible { get; set; }
    }

    public class PurchaseOrderMainInfoPermissions
    {
        public bool CanEdit { get; set; }
        public bool CanNotify { get; set; }
        public bool CanClose { get; set; }
        public bool CanApprove { get; set; }
        public bool CanDisapprove { get; set; }
    }
}
