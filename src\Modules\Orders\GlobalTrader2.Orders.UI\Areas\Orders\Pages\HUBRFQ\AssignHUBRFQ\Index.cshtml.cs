using GlobalTrader2.SharedUI.Areas.Common.Pages.Shared.Components.LiteDatatable;

namespace GlobalTrader2.Orders.UI.Areas.Orders.Pages.HUBRFQ.AssignHUBRFQ
{
    [SectionAuthorize(SecurityFunction.OrdersSection_View)]
    public class IndexModel : BasePageModel
    {
        private readonly SessionManager _sessionManager;

        public bool isGlobalLogin { get; set; } = false;
        public bool CanAdd { get; set; }
        public int CurrentTab { get; set; }
        public bool CanViewMy { get; set; } = true;
        public bool CanViewTeam { get; set; } = true;
        public bool CanViewDivision { get; set; } = true;
        public bool CanViewCompany { get; set; } = true;
        public IndexModel(SecurityManager securityManager, IMediator mediator, SessionManager sessionManager) : base(securityManager)
        {
            _sessionManager = sessionManager;
            isGlobalLogin = _sessionManager.IsGlobalUser;
            AddBreadCrumbs();
            SiteSection = SiteSection.Orders;
            PageType = SitePage.Orders_BOMBrowse;
        }
        private void AddBreadCrumbs()
        {
            BreadCrumb.Add(Navigations.Home);
            BreadCrumb.Add(Navigations.Orders);
            BreadCrumb.Add(Navigations.AssignHUBRFQ);
        }
        public IActionResult OnGet()
        {
            //Get visible tab list
            var listTabs = Enum.GetValues<ViewLevelList>();

            //set tab from preferences if it's not been set specifically
            var defaultPage = _sessionManager.DefaultListPageView;
            CurrentTab = (int)listTabs.First(x => x == defaultPage);

            return Page();
        }
    }
}
