﻿import { EventEmitter } from '../base/event-emmiter.js?v=#{BuildVersion}#'

export class MediaUploadDocumentDialog extends EventEmitter {
    constructor (dialog = {
        dialogSelector: "",
        formSelector: "",
        allowedFileExtensions: [],
    }) {
        super();
        this.$dialog = $(`#${dialog.dialogSelector}`);
        this.$form = $(`#${dialog.formSelector}`);

        this.allowedFileExtensions = dialog.allowedFileExtensions;
        this.init();
    }
    init() {
        const $form = this.$form;
        this.$dialog.dialog({
            width: "40vw",
            close: function () {
                $(this).find(".form-error-summary").hide();
                $form[0].reset();
                $form.validate().resetForm();
                $(this).find('.is-invalid').removeClass("is-invalid");
                $(this).find(".form-error-summary #FileSizeZero").remove();
            },
            buttons: [
                {
                    text: window.localizedStrings.save,
                    class: 'btn btn-primary fw-normal',
                    html: `<img src="/img/icons/save.svg" alt="${window.localizedStrings.save}">${window.localizedStrings.save}`,
                    click: async () => {
                        if ($form.valid()) {
                            this.$dialog.find(".form-error-summary").hide();
                            this.trigger("form:submit");
                        } else {
                            this.$dialog.find(".form-error-summary #FileSizeZero").remove();
                            this.$dialog.find(".form-error-summary").show();
                        };
                    }
                },
                {
                    text: window.localizedStrings.cancel,
                    class: 'btn btn-danger fw-normal',
                    html: `<img src="/img/icons/slash.svg" alt="${window.localizedStrings.cancel}">${window.localizedStrings.cancel}`,
                    click: () => {
                        this.trigger("form:close"); 
                        this.$dialog.dialog("close");
                    },
                },
            ]
        });
    }

    setLoading(isLoading = true) {
        this.$dialog.dialog("setLoading", isLoading);
        this.$dialog.parent().find(`.ui-dialog-buttonset button:has(img[alt="${window.localizedStrings.save}"])`).prop('disabled', isLoading);
    }

    initFormValidation(fileSizeMBLimit) {
        this.$form.validate({
            rules: {
                file: {
                    required: true,
                    extension: this.allowedFileExtensions,
                    filesizeMB: fileSizeMBLimit
                }
            },
            errorPlacement: function (error, element) {
                const inputName = $(element).attr("name");
                if (inputName === 'file') {
                    error
                        .appendTo(element.parent().next('div'));
                }
                else {
                    $(error).insertAfter(element);
                }
            },
        });
    }

    destroyFormValidation() {
        this.$form.validate().destroy();
    }

    bindingSectionName(sectionName) {
        this.$form.find('input[name="sectionName"]').val(sectionName);
    }
}