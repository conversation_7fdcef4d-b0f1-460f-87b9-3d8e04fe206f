﻿using GlobalTrader2.Dto.CustomerRequirement;
using GlobalTrader2.Dto.Sourcing;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GlobalTrader2.Orders.UserCases.Orders.BOM.HUBRFQSourcingResults.Query
{
    public class GetSourcingResultDetailsHandler : IRequestHandler<GetSourcingResultDetailsQuery, BaseResponse<SourcingResultsDetailsHUBRFQ>>
    {
        private readonly IBaseRepository<SourcingResultDetail> _baseRepository;

        public GetSourcingResultDetailsHandler(IBaseRepository<SourcingResultDetail> baseRepository)
        {
            _baseRepository = baseRepository;
        }

        public async Task<BaseResponse<SourcingResultsDetailsHUBRFQ>> Handle(GetSourcingResultDetailsQuery request, CancellationToken cancellationToken)
        {
            var response = new BaseResponse<SourcingResultsDetailsHUBRFQ>();

            string procedureName = StoredProcedures.Select_SourcingResult;
            var queryStr = $"{procedureName} @SourcingResultId";
            SqlParameter[] param =
            {
                new SqlParameter("@SourcingResultId", SqlDbType.Int) { Value = request.SourcingResultId }
            };
            var result = await _baseRepository.SqlQueryRawAsync(queryStr, param);

            if (result != null && result.Any())
            {
                var sourcingResult = result[0];

                response.Success = true;
                response.Data = new SourcingResultsDetailsHUBRFQ
                {
                    SourcingResultId = sourcingResult.SourcingResultId,
                    CurrencyNo = sourcingResult.CurrencyNo,
                    DateCode = sourcingResult.DateCode,
                    FullPart = sourcingResult.FullPart,
                    ManufacturerName = sourcingResult.ManufacturerName,
                    ManufacturerNo = sourcingResult.ManufacturerNo,
                    MSLLevelNo = sourcingResult.MSLLevelNo,
                    Notes = sourcingResult.Notes,
                    OfferStatusNo = sourcingResult.OfferStatusNo,
                    PackageDescription = sourcingResult.PackageDescription,
                    PackageNo = sourcingResult.PackageNo,
                    Part = sourcingResult.Part,
                    PartWatchMatch = sourcingResult.partWatchMatch ?? false,
                    Price = sourcingResult.Price ?? 0,
                    ProductDescription = sourcingResult.ProductDescription,
                    ProductInactive = sourcingResult.ProductInactive,
                    ProductNo = sourcingResult.ProductNo,
                    Quantity = sourcingResult.Quantity ?? 0,
                    ROHS = sourcingResult.ROHS,
                    SupplierName = sourcingResult.SupplierName,
                    SupplierNo = sourcingResult.SupplierNo,
                    CountryName = sourcingResult.CountryName,
                    CountryNo = sourcingResult.CountryNo,
                    CountryOfOriginNo = sourcingResult.CountryOfOriginNo,
                    CurrencyCode = sourcingResult.CurrencyCode,
                    CustomerRequirementNo = sourcingResult.CustomerRequirementNo,
                    DeliveryDate = sourcingResult.DeliveryDate,
                    EstimatedShippingCost = sourcingResult.EstimatedShippingCost,
                    FactorySealed = sourcingResult.FactorySealed,
                    IHSCountryOfOriginName = sourcingResult.IHSCountryOfOriginName,
                    IHSCountryOfOriginNo = sourcingResult.IHSCountryOfOriginNo,
                    IsTestingRecommended = sourcingResult.IsTestingRecommended,
                    LeadTime = sourcingResult.LeadTime,
                    MSL = sourcingResult.MSL,
                    NonPreferredCompany = sourcingResult.NonPreferredCompany,
                    ReasonForSupplierNo = sourcingResult.ReasonForSupplierNo,
                    regionNo = sourcingResult.regionNo,
                    RiskOfSupplierNo = sourcingResult.RiskOfSupplierNo,
                    ROHSStatus = sourcingResult.ROHSStatus,
                    SellPriceLessReason = sourcingResult.SellPriceLessReason,
                    SourcingTable = sourcingResult.SourcingTable,
                    SPQ = sourcingResult.SPQ,
                    SupplierLTB = sourcingResult.SupplierLTB,
                    SupplierMOQ = sourcingResult.SupplierMOQ,
                    SupplierNotes = sourcingResult.SupplierNotes,
                    SupplierTotalQSA = sourcingResult.SupplierTotalQSA,
                    SupplierWarranty = sourcingResult.SupplierWarranty,
                    TypeOfSupplierNo = sourcingResult.TypeOfSupplierNo,
                    UPLiftPrice = sourcingResult.UPLiftPrice,
                    SupplierPrice = sourcingResult.SupplierPrice
                };
            }

            return response;
        }
    }
}
