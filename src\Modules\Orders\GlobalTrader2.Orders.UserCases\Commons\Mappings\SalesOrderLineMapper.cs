using GlobalTrader2.Core.Helpers;
using GlobalTrader2.Dto.MailGroupMembers;
using GlobalTrader2.Dto.Manufacturers.Document;
using GlobalTrader2.Dto.PurchaseRequisition;
using GlobalTrader2.Dto.SalesOrder;
using GlobalTrader2.Dto.SalesOrderLine;
using GlobalTrader2.Orders.UserCases.Orders.SOLine.Commands.EditSalesOrderLine;
using System.Globalization;
using GlobalTrader2.Dto.Stock;
using GlobalTrader2.Orders.UserCases.Orders.SOLine.Commands.InsertSalesOrderLine;

namespace GlobalTrader2.Orders.UserCases.Commons.Mappings
{
    public class SalesOrderLineMapper : Profile
    {
        public SalesOrderLineMapper()
        {
            CreateMap<GetDetailsForLineCalculationsReadModel, SalesOrderDetailsForLineCalculationsDto>();
            CreateMap<GetAllSoLinesBySoIdReadModel, AllOpenClosedSalesOrderLineDetailsDto>();
            CreateMap<GetOpenSoLinesBySoIdReadModel, AllOpenClosedSalesOrderLineDetailsDto>();
            CreateMap<GetClosedSoLinesBySoIdReadModel, AllOpenClosedSalesOrderLineDetailsDto>();
            CreateMap<ExportApprovalSOLineReadModel, ExportApprovalSOLineDto>()
                .ForMember(des => des.LineId, opt => opt.MapFrom(src => src.SalesOrderLineId))
                .ForMember(des => des.LineNo, opt => opt.MapFrom(src => src.SOSerialNo))
                .ForMember(des => des.ManufacturerNo, opt => opt.MapFrom(src => src.ManufacturerNo))
                .ForMember(des => des.OgelRequiredOnSO, opt => opt.MapFrom(src => src.ISOGELRequired));
            CreateMap<SalesOrderLineModel, SalesOrderLineDto>();
            CreateMap<GetPromiseLogReadModel, GetPromiseLogDto>()
                .ForMember(des => des.PromiseReason, opt => opt.MapFrom(src => src.Name))
                .ForMember(des => des.UpdatedBy, opt => opt.MapFrom(src => src.EmployeeName))
                .ForMember(des => des.UpdatedDate, opt => opt.MapFrom(src => Functions.FormatDate(src.UpdatedDate, System.Globalization.CultureInfo.CurrentCulture)))
                .ForMember(des => des.PromiseDateOld, opt => opt.MapFrom(src => Functions.FormatDate(src.DatePromiseOld, System.Globalization.CultureInfo.CurrentCulture)));
            CreateMap<EditExportApprovalReadModel, EditExportApprovalDto>();
            CreateMap<ExportApprovalDataReadModel, ExportApprovalDataDto>();
            CreateMap<ApproveRejectExportApprovalReadModel, ApproveRejectExportApprovalDto>();
            CreateMap<OgelSelectSoNotifierReadModel, OgelSelectSoNotifierDto>();
            CreateMap<OgelEccnGroupMemberEmailReadModel, OgelEccnGroupMemberEmailDto>();
            CreateMap<GetAllSoLinesforEuuReadModel, GetAllSoLinesforEuuDto>()
                .ForMember(dest => dest.UploadDate, opt => opt.MapFrom(src => Functions.FormatDate(src.EUUPDFUploadDate, System.Globalization.CultureInfo.CurrentCulture)));
            CreateMap<SoLineEuuPdf, DocumentDto>()
                .ForMember(dest => dest.DocumentId, opt => opt.MapFrom(src => src.SoLineEuuPdfId))
                .ForMember(dest => dest.UpdatedByName, opt => opt.MapFrom(src => src.Login != null ? src.Login.EmployeeName : string.Empty))
                .ForMember(dest => dest.Caption, opt => opt.MapFrom(src => Functions.ReplaceLineBreaks(src.Caption ?? string.Empty)))
                .AfterMap((src, dest, context) =>
                {
                    var culture = context.Items["culture"]?.ToString();
                    dest.DateUploadString = src.DLUP != null ? Functions.FormatDate((DateTime)src.DLUP, false, true, CultureInfo.CurrentCulture) : "";
                });
            CreateMap<ItemsearchSalesOrderLineReadModel, GetItemsearchSalesOrderLineDto>()
                .ForMember(dest => dest.Price, opt => opt.MapFrom(src => Convert.ToDecimal(src.Price)));
            CreateMap<EditSalesOrderLineRequest, EditSalesOrderLineCommand>();
            CreateMap<ItemSearchStockReadModel, GetItemSearchStockDto>()
                .ForMember(dest => dest.LandedCost, opt => opt.MapFrom(src => Convert.ToDecimal(src.LandedCost)));
            CreateMap<StockItemDetailReadModel, GetStockItemDetailDto>()
                .ForMember(dest => dest.LandedCost, opt => opt.MapFrom(src => Convert.ToDecimal(src.LandedCost)))
                .ForMember(dest => dest.ClientLandedCost, opt => opt.MapFrom(src => Convert.ToDecimal(src.ClientLandedCost)))
                .ForMember(dest => dest.GoodsInShipInCost, opt => opt.MapFrom(src => Convert.ToDecimal(src.GoodsInShipInCost)))
                .ForMember(dest => dest.OriginalLandedCost, opt => opt.MapFrom(src => Convert.ToDecimal(src.OriginalLandedCost)))
                .ForMember(dest => dest.GIQty, opt => opt.MapFrom(src => Convert.ToDecimal(src.GIQty)))
                .ForMember(dest => dest.ClientPurchasePrice, opt => opt.MapFrom(src => Convert.ToDecimal(src.ClientPurchasePrice)))
                .ForMember(dest => dest.ClientUPLiftPrice, opt => opt.MapFrom(src => Convert.ToDecimal(src.ClientUPLiftPrice)))
                .ForMember(dest => dest.GoodsInPrice, opt => opt.MapFrom(src => Convert.ToDecimal(src.GoodsInPrice)))
                .ForMember(dest => dest.PurchasePrice, opt => opt.MapFrom(src => Convert.ToDecimal(src.PurchasePrice)))
                .ForMember(dest => dest.ResalePrice, opt => opt.MapFrom(src => Convert.ToDecimal(src.ResalePrice)));

            CreateMap<SalesOrderLineExportReadModel, SalesOrderLineExportDto>();
            CreateMap<AllOpenClosedSalesOrderLineDetailsDto, SalesOrderLineForSOReportDto>();
            CreateMap<SalesOrderLineReportPoStockReadModel, SalesOrderLineReportPoStockDto>();
            CreateMap<SalesOrderLineForSOReportDto, SalesOrderLineReportPoStockDto>();
            CreateMap<SalesOrderLineReportPoReadModel, SalesOrderLineReportPoDto>();
            CreateMap<SalesOrderLineReportManualStockReadModel, SalesOrderLineReportManualStockDto>();
            CreateMap<SalesOrderLineReportShippedReadModel, SalesOrderLineReportShippedDto>();
            CreateMap<AddSalesOderLineRequest, InsertSalesOrderLineCommand>();
        }
    }
}
