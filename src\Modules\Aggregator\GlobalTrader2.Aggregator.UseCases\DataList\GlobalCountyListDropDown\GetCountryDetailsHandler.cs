﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GlobalTrader2.Aggregator.UseCases.DataList.GlobalCountyListDropDown
{
    public class GetCountryDetailsHandler : IRequestHandler<GetCountryDetailsQuery, BaseResponse<GetCountryDetailsReadModel>>
    {
        private readonly IBaseRepository<GetCountryDetailsReadModel> _repository;

        public GetCountryDetailsHandler(IBaseRepository<GetCountryDetailsReadModel> repository)
        {
            _repository = repository;
        }

        public async Task<BaseResponse<GetCountryDetailsReadModel>> Handle(GetCountryDetailsQuery request, CancellationToken cancellationToken)
        {
            var procedureName = StoredProcedures.AS6081_CountryDetails;
            var queryStr = $"{procedureName} @SupplierNo";
            SqlParameter[] param =
            [
                new SqlParameter("@SupplierNo", SqlDbType.Int ) { Value = request.SupplierNo},
            ];

            var company = await _repository.SqlQueryRawAsync(queryStr, param);

            return new BaseResponse<GetCountryDetailsReadModel>
            {
                Success = true,
                Data = company.FirstOrDefault()
            };
        }
    }
}
