@using GlobalTrader2.SharedUI.Interfaces
@using Microsoft.AspNetCore.Mvc.Localization
@using Microsoft.Extensions.Localization
@inject IViewLocalizer _localizer
@inject IStringLocalizer<GlobalTrader2.SharedUI.CommonResources> _commonLocalizer
@inject IStringLocalizer<GlobalTrader2.SharedUI.MessageResources> _messageLocalizer

<div class="d-flex align-items-center justify-content-between w-100">
    <button type="button" class="btn btn-primary" id="select-all-button">
        <span class="badge bg-white text-primary top-0">0</span> @_localizer["Select All"]
    </button>

    <div>
        <div class="d-flex gap-4">
            <span class="form-label fw-bold">
                Assignment Type
            </span>
            <div class="d-flex gap-3">
                <div class="d-flex gap-2">
                    <input class="form-check-input check-md" type="radio" name="AssignmentType" id="AssignmentTypeUser" value="user" checked>
                    <label for="AssignmentTypeUser">User</label>
                </div>
                <div class="d-flex gap-2">
                    <input class="form-check-input check-md" type="radio" name="AssignmentType" id="AssignmentTypeGroup" value="group">
                    <label for="AssignmentTypeGroup">Group</label>
                </div>
            </div>
        </div>
        <div class="d-flex gap-2">
            <select class="form-select select-md me-2" id="edit-form-buyer" name="contactNo">
                <option value="">Select...</option>
            </select>
            <a href="#" class="select-menu-gtv2-refresh-button">
                <i class="fa-solid fa-arrows-rotate"></i>
            </a>
            <button type="button" class="btn btn-primary">
                <i class="fa-solid fa-user-check"></i> Assign
            </button>
        </div>
    </div>
</div>