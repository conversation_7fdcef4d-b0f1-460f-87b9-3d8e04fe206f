using FluentValidation.TestHelper;
using GlobalTrader2.Orders.UserCases.Orders.BOM.HUBRFQSourcingResults.Command;

namespace GlobalTrader2.Orders.Test.Orders.BOM.HUBRFQSourcingResults.Command
{
    public class UpdateSourcingResultsHandlerValidatorTest
    {
        private readonly UpdateSourcingResultsHandlerValidator _validator;

        public UpdateSourcingResultsHandlerValidatorTest()
        {
            _validator = new UpdateSourcingResultsHandlerValidator();
        }

        [Fact]
        public void Validator_ShouldReturnValidationFailure_WhenSourcingResultIdIsEmpty()
        {
            // Arrange
            var command = new UpdateSourcingResultCommand { SourcingResultId = 0 };

            // Act
            var result = _validator.TestValidate(command);

            // Assert
            result.ShouldHaveValidationErrorFor(x => x.SourcingResultId);
        }

        [Fact]
        public void Validator_ShouldReturnValidationFailure_WhenPartIsEmpty()
        {
            // Arrange
            var command = new UpdateSourcingResultCommand { Part = "" };

            // Act
            var result = _validator.TestValidate(command);

            // Assert
            result.ShouldHaveValidationErrorFor(x => x.Part);
        }

        [Fact]
        public void Validator_ShouldReturnValidationFailure_WhenPartIsNull()
        {
            // Arrange
            var command = new UpdateSourcingResultCommand { Part = null! };

            // Act
            var result = _validator.TestValidate(command);

            // Assert
            result.ShouldHaveValidationErrorFor(x => x.Part);
        }

        [Fact]
        public void Validator_ShouldReturnValidationFailure_WhenPartExceedsMaxLength()
        {
            // Arrange
            var command = new UpdateSourcingResultCommand 
            { 
                Part = "A".PadRight(31, 'X') // 31 characters, exceeds limit of 30
            };

            // Act
            var result = _validator.TestValidate(command);

            // Assert
            result.ShouldHaveValidationErrorFor(x => x.Part);
        }

        [Fact]
        public void Validator_ShouldReturnValidationFailure_WhenQuantityIsZero()
        {
            // Arrange
            var command = new UpdateSourcingResultCommand { Quantity = 0 };

            // Act
            var result = _validator.TestValidate(command);

            // Assert
            result.ShouldHaveValidationErrorFor(x => x.Quantity);
        }

        [Fact]
        public void Validator_ShouldReturnValidationFailure_WhenQuantityIsNegative()
        {
            // Arrange
            var command = new UpdateSourcingResultCommand { Quantity = -1 };

            // Act
            var result = _validator.TestValidate(command);

            // Assert
            result.ShouldHaveValidationErrorFor(x => x.Quantity);
        }

        [Fact]
        public void Validator_ShouldNotReturnValidationFailure_WhenQuantityIsNull()
        {
            // Arrange
            var command = new UpdateSourcingResultCommand { Quantity = null };

            // Act
            var result = _validator.TestValidate(command);

            // Assert
            result.ShouldNotHaveValidationErrorFor(x => x.Quantity);
        }

        [Fact]
        public void Validator_ShouldReturnValidationFailure_WhenPriceIsNegative()
        {
            // Arrange
            var command = new UpdateSourcingResultCommand { Price = -1.0 };

            // Act
            var result = _validator.TestValidate(command);

            // Assert
            result.ShouldHaveValidationErrorFor(x => x.Price);
        }

        [Fact]
        public void Validator_ShouldNotReturnValidationFailure_WhenPriceIsZero()
        {
            // Arrange
            var command = new UpdateSourcingResultCommand { Price = 0.0 };

            // Act
            var result = _validator.TestValidate(command);

            // Assert
            result.ShouldNotHaveValidationErrorFor(x => x.Price);
        }

        [Fact]
        public void Validator_ShouldReturnValidationFailure_WhenEstimatedShippingCostValueIsNegative()
        {
            // Arrange
            var command = new UpdateSourcingResultCommand { EstimatedShippingCostValue = -1.0 };

            // Act
            var result = _validator.TestValidate(command);

            // Assert
            result.ShouldHaveValidationErrorFor(x => x.EstimatedShippingCostValue);
        }

        [Fact]
        public void Validator_ShouldNotReturnValidationFailure_WhenEstimatedShippingCostValueIsZero()
        {
            // Arrange
            var command = new UpdateSourcingResultCommand { EstimatedShippingCostValue = 0.0 };

            // Act
            var result = _validator.TestValidate(command);

            // Assert
            result.ShouldNotHaveValidationErrorFor(x => x.EstimatedShippingCostValue);
        }

        [Fact]
        public void Validator_ShouldReturnValidationFailure_WhenSuplierPriceIsNegative()
        {
            // Arrange
            var command = new UpdateSourcingResultCommand { SuplierPrice = -1.0 };

            // Act
            var result = _validator.TestValidate(command);

            // Assert
            result.ShouldHaveValidationErrorFor(x => x.SuplierPrice);
        }

        [Fact]
        public void Validator_ShouldNotReturnValidationFailure_WhenSuplierPriceIsNull()
        {
            // Arrange
            var command = new UpdateSourcingResultCommand { SuplierPrice = null };

            // Act
            var result = _validator.TestValidate(command);

            // Assert
            result.ShouldNotHaveValidationErrorFor(x => x.SuplierPrice);
        }

        [Fact]
        public void Validator_ShouldNotReturnValidationFailure_WhenSuplierPriceIsZero()
        {
            // Arrange
            var command = new UpdateSourcingResultCommand { SuplierPrice = 0.0 };

            // Act
            var result = _validator.TestValidate(command);

            // Assert
            result.ShouldNotHaveValidationErrorFor(x => x.SuplierPrice);
        }

        [Fact]
        public void Validator_ShouldPass_WhenAllRequiredFieldsAreValid()
        {
            // Arrange
            var command = new UpdateSourcingResultCommand
            {
                SourcingResultId = 123,
                Part = "VALID-PART",
                Price = 10.50,
                EstimatedShippingCostValue = 5.25,
                Quantity = 100,
                SuplierPrice = 8.75,
                ManufacturerNo = 456,
                DateCode = "2023A",
                ProductNo = 789,
                PackageNo = 101,
                OfferStatusNo = 1,
                SupplierNo = 202,
                ROHS = 1,
                Notes = "Valid update notes",
                UpdatedBy = 303,
                DeliveryDate = DateTime.Now.AddDays(30),
                PUHUB = true,
                SPQ = "100",
                LeadTime = "30 days",
                ROHSStatus = "Compliant",
                FactorySealed = "Yes",
                MSL = "Level 3",
                SupplierTotalQSA = "1000",
                SupplierLTB = "500",
                SupplierMOQ = "100",
                RegionNo = 1,
                CurrencyNo = 1,
                LinkMultiCurrencyNo = 1,
                MslLevelNo = 3,
                SupplierWarranty = 12,
                IsTestingRecommended = true,
                PriorityNo = 1,
                IHSCountryOfOriginNo = 1,
                ChangedFields = "Price,Quantity",
                PartWatchMatchHUBIPO = false,
                TypeOfSupplier = 1,
                ReasonForSupplier = 1,
                RiskOfSupplier = 1,
                CountryNo = 1,
                SellPriceLessReason = "Market conditions"
            };

            // Act
            var result = _validator.TestValidate(command);

            // Assert
            result.ShouldNotHaveAnyValidationErrors();
        }

        [Fact]
        public void Validator_ShouldPass_WhenOnlyRequiredFieldsAreProvided()
        {
            // Arrange
            var command = new UpdateSourcingResultCommand
            {
                SourcingResultId = 123,
                Part = "VALID-PART",
                Price = 10.50,
                EstimatedShippingCostValue = 5.25
            };

            // Act
            var result = _validator.TestValidate(command);

            // Assert
            result.ShouldNotHaveAnyValidationErrors();
        }

        [Fact]
        public void Validator_ShouldPass_WhenQuantityIsPositive()
        {
            // Arrange
            var command = new UpdateSourcingResultCommand { Quantity = 50 };

            // Act
            var result = _validator.TestValidate(command);

            // Assert
            result.ShouldNotHaveValidationErrorFor(x => x.Quantity);
        }

        [Fact]
        public void Validator_ShouldPass_WhenPartIsAtMaxLength()
        {
            // Arrange
            var command = new UpdateSourcingResultCommand 
            { 
                Part = "A".PadRight(30, 'X') // Exactly 30 characters
            };

            // Act
            var result = _validator.TestValidate(command);

            // Assert
            result.ShouldNotHaveValidationErrorFor(x => x.Part);
        }

        [Fact]
        public void Validator_ShouldPass_WhenPriceIsPositive()
        {
            // Arrange
            var command = new UpdateSourcingResultCommand { Price = 99.99 };

            // Act
            var result = _validator.TestValidate(command);

            // Assert
            result.ShouldNotHaveValidationErrorFor(x => x.Price);
        }

        [Fact]
        public void Validator_ShouldPass_WhenEstimatedShippingCostValueIsPositive()
        {
            // Arrange
            var command = new UpdateSourcingResultCommand { EstimatedShippingCostValue = 15.75 };

            // Act
            var result = _validator.TestValidate(command);

            // Assert
            result.ShouldNotHaveValidationErrorFor(x => x.EstimatedShippingCostValue);
        }

        [Fact]
        public void Validator_ShouldPass_WhenSuplierPriceIsPositive()
        {
            // Arrange
            var command = new UpdateSourcingResultCommand { SuplierPrice = 25.50 };

            // Act
            var result = _validator.TestValidate(command);

            // Assert
            result.ShouldNotHaveValidationErrorFor(x => x.SuplierPrice);
        }
    }
}
