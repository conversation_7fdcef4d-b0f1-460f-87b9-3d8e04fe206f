﻿using GlobalTrader2.Aggregator.UseCases.Contacts.AllCompanies.Details.UploadCompanyCertificatePdf;
using GlobalTrader2.Aggregator.UseCases.Contacts.AllCompanies.Details.UploadCompanyPdf;
using GlobalTrader2.Aggregator.UseCases.DataList.DeleteExcelForManufacturer;
using GlobalTrader2.Aggregator.UseCases.DataList.DeletePdfForManufacturer;
using GlobalTrader2.Aggregator.UseCases.DataList.GetDocumentFileSize;
using GlobalTrader2.Aggregator.UseCases.DataList.GetExcelListForManufacturer;
using GlobalTrader2.Aggregator.UseCases.DataList.GetFile;
using GlobalTrader2.Aggregator.UseCases.DataList.GetListPdfForManufacturer;
using GlobalTrader2.Aggregator.UseCases.DataList.GetMaxDocumentCount;
using GlobalTrader2.Aggregator.UseCases.DataList.GetMedia;
using GlobalTrader2.Aggregator.UseCases.DataList.GetPdfUri;
using GlobalTrader2.Aggregator.UseCases.DataList.GetSourcingImage;
using GlobalTrader2.Aggregator.UseCases.DataList.UploadExcelDocument;
using GlobalTrader2.Aggregator.UseCases.DataList.UploadPdfDocument;
using GlobalTrader2.Aggregator.UseCases.FileUpload.Commands.DeleteSourcingImage;
using GlobalTrader2.Aggregator.UseCases.FileUpload.Commands.PostMultipleMediaDocument;
using GlobalTrader2.Aggregator.UseCases.FileUpload.Commands.UploadImportDataFile;
using GlobalTrader2.Aggregator.UseCases.Orders.SalesOrders.UploadCustomerOnlyPoPdf;
using GlobalTrader2.Aggregator.UseCases.Orders.SalesOrders.UploadSOExcelDocument;
using GlobalTrader2.Aggregator.UseCases.Orders.SalesOrders.UploadSoLineEuuPdf;
using GlobalTrader2.Contacts.UseCases.Companies.Commands.DeleteCompanyPdf;
using GlobalTrader2.Contacts.UseCases.Companies.Commands.Details.DeleteCompanyCertificatePdf;
using GlobalTrader2.Contacts.UseCases.Companies.Queries.Details.CheckCompanyInactive;
using GlobalTrader2.Contacts.UseCases.Companies.Queries.Details.GetCertificatePDFs;
using GlobalTrader2.Contacts.UseCases.Companies.Queries.Details.GetListPdfForCompany;
using GlobalTrader2.Core.Bases;
using GlobalTrader2.Core.Constants;
using GlobalTrader2.Core.Enums;
using GlobalTrader2.Core.Exceptions;
using GlobalTrader2.Core.Interfaces;
using GlobalTrader2.Dto.File;
using GlobalTrader2.Dto.Manufacturers.Document;
using GlobalTrader2.Orders.UserCases.Orders.SalesOrders.Commands.DeleteCustomerPoPdf;
using GlobalTrader2.Orders.UserCases.Orders.SalesOrders.Commands.DeleteSalesOrderExcel;
using GlobalTrader2.Orders.UserCases.Orders.SalesOrders.Commands.DeleteSoLineEuuPdf;
using GlobalTrader2.Orders.UserCases.Orders.SalesOrders.Queries.GetListExcelDocForSalesOrder;
using GlobalTrader2.Orders.UserCases.Orders.SalesOrders.Queries.GetListSOPaymentAttachment;
using GlobalTrader2.Orders.UserCases.Orders.SalesOrders.Queries.GetListStockImageByStockId;
using GlobalTrader2.Orders.UserCases.Orders.SalesOrders.Queries.GetPdfForSalesOrder;
using GlobalTrader2.Orders.UserCases.Orders.SOLine.Queries.GetPdfForEuuSoLine;
using GlobalTrader2.SharedUI.Authorization;
using GlobalTrader2.SharedUI.Bases;
using GlobalTrader2.SharedUI.Enums;
using GlobalTrader2.SharedUI.Models;
using GlobalTrader2.SharedUI.Services;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Quartz.Util;

namespace GlobalTrader2.Contacts.UI.Areas.Contact.Controllers.Manufacturers
{
    [Authorize]
    [ApiController]
    [Route("api/documents")]
    public class DocumentController : ApiBaseController
    {
        protected readonly SecurityManager _securityManager;
        private readonly IMediator _mediator;
        private readonly IBlobStorageService _blobStorageService;
        private readonly string _sectionNotFoundMessage = "Section name is not valid";
        public DocumentController(IMediator mediator, SecurityManager securityManager, IBlobStorageService blobStorageService)
        {
            _mediator = mediator;
            _securityManager = securityManager;
            _blobStorageService = blobStorageService;
        }

        [HttpGet("file-size")]
        public async Task<IActionResult> GetDocumentFileSizeAsync([FromQuery] int documentType)
        {
            if (!Enum.IsDefined(typeof(DocumentSizeByType), documentType))
                return BadRequest("Invalid document type.");

            var query = new GetDocumentFileSizeQuery(documentType);
            var result = await _mediator.Send(query);
            return Ok(result);
        }

        [HttpGet("max-file-count")]
        public async Task<IActionResult> GetMaxDocumentFileCountAsync([FromQuery] int settingItemType)
        {
            if (!Enum.IsDefined(typeof(SettingItem.List), settingItemType))
                return BadRequest("Invalid setting item's type.");

            var query = new GetMaxDocumentCountQuery(settingItemType, ClientId);
            var result = await _mediator.Send(query);
            return Ok(result);
        }

        [HttpGet("{id}/pdf-documents")]
        public async Task<IActionResult> GetListPdfDocumentsForSectionAsync([FromRoute] int id, [FromQuery] string sectionName)
        {
            var result = new BaseResponse<IEnumerable<DocumentDto>>();

            switch (sectionName.Split("_")[0].ToUpper())
            {
                case SectionNameConstant.Manufacturer:
                    var isAllowed = await CheckPermissionManufacturerPdf();
                    if (!isAllowed) return StatusCode(StatusCodes.Status403Forbidden);

                    result = await GetListPdfDocumentsForManufacturerAsync(id);
                    break;
                case SectionNameConstant.Company:
                    if (!await CheckViewCompanyPdfPermissionAsync()) return StatusCode(StatusCodes.Status403Forbidden);
                    result = await _mediator.Send(new GetListPdfForCompanyQuery(id, Culture));
                    break;
                case SectionNameConstant.CompanyCertificate:
                    if (!await CheckViewCompanyPdfPermissionAsync()) return StatusCode(StatusCodes.Status403Forbidden);
                    var companyId = sectionName.Split("_")[2];
                    if (companyId.IsNullOrWhiteSpace()) break;
                    //check permission view company detail and certificate tab
                    result = await _mediator.Send(new GetCertificatePDFsQuery(id, Int32.Parse(companyId), Culture));
                    break;
                case string secName when secName is SectionNameConstant.CustomerOnlyPO or SectionNameConstant.SalesOrderPdf or SectionNameConstant.SORDocument:
                    {
                        var salesOrderId = sectionName.Split("_")[2];
                        var filetype = secName switch
                        {
                            SectionNameConstant.CustomerOnlyPO => SectionNameConstant.CustomerOnlyPOFileType,
                            SectionNameConstant.SalesOrderPdf => SectionNameConstant.SalesOrderPdfFileType,
                            SectionNameConstant.SORDocument => SectionNameConstant.SORDocumentFileType,
                            _ => throw new ArgumentException("Invalid section name", nameof(sectionName))
                        };
                        result = await _mediator.Send(new GetPdfForSalesOrderQuery(Int32.Parse(salesOrderId), filetype, Culture));
                        break;
                    }
                case SectionNameConstant.SOPaymentFiles:
                    {
                        var salesOrderId = sectionName.Split("_")[2];
                        result = await _mediator.Send(new GetListSOPaymentAttachmentQuery(Int32.Parse(salesOrderId), Culture));
                        break;
                    }
                case SectionNameConstant.EUUDocument:
                    {
                        var salesOrderLineId = id;
                        result = await _mediator.Send(new GetPdfForEuuSoLineQuery
                        {
                            SalesOrderLineId = salesOrderLineId,
                            FileType = SectionNameConstant.EUUDocumentFileType,
                            Culture = Culture
                        });
                        break;
                    }
                default:
                    return NotFound(_sectionNotFoundMessage);
            }

            return Ok(result);
        }

        [HttpGet("{id}/excel-documents")]
        public async Task<IActionResult> GetExcelDocumentsForSectionAsync([FromRoute] int id, [FromQuery] string sectionName)
        {
            var response = new BaseResponse<IEnumerable<DocumentDto>>();
            string section = sectionName.Split('_')[0].ToUpper();
            switch (section)
            {
                case SectionNameConstant.ManufacturerExcel:
                    {
                        var manufacturerId = id;
                        var query = new GetExcelListForManufacturerQuery(manufacturerId, Culture);
                        response = await _mediator.Send(query);
                        break;
                    }
                case SectionNameConstant.SOExcelDocument:
                    {
                        var salesOrderId = sectionName.Split("_")[2];
                        response = await _mediator.Send(new GetListExcelDocForSalesOrderQuery(Int32.Parse(salesOrderId), SectionNameConstant.SOExcelDocument, Culture));
                        break;
                    }
                default:
                    return BadRequest(_sectionNotFoundMessage);
            }

            return Ok(response);
        }

        [HttpPost("file")]
        public async Task<IActionResult> GetFileAsync([FromBody] DocumentRequest request)
        {
            var query = new GetFileQuery(request.FilePath);
            var response = await _mediator.Send(query);
            if (response.Success && response.Data != null)
            {
                var filePathSplitArray = request.FilePath.Split('/');
                Response.Headers.TryAdd("Content-Disposition", $"attachment; filename=\"{filePathSplitArray[filePathSplitArray.Length - 1]}\"");
                return File(response.Data.Content!, response.Data.ContentType!);
            }
            return BadRequest("File not found.");
        }

        [HttpGet("pdf-documents/uri")]
        public async Task<IActionResult> GetPdfUrlAsync([FromQuery] string filePath)
        {
            var query = new GetPdfUriQuery(filePath);
            var result = await _mediator.Send(query);
            return Ok(result);
        }

        [HttpPost("{id}/uploadExcel")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> UploadExcelDocumentsAsync(int id, [FromForm] UploadDocumentRequest request)
        {
            if (request.File == null || request.File.Length == 0)
            {
                return BadRequest("Uploaded file is empty");
            }

            var response = new BaseResponse<bool>();
            using (var streamFile = request.File.OpenReadStream())
            {
                string sectionName = request.SectionName.Split("_")[0].ToUpper();
                string fileName = FormatDocumentFileName(request.File.FileName, request.SectionName, ClientId);
                switch (sectionName)
                {
                    case SectionNameConstant.ManufacturerExcel:
                        {
                            response = await _mediator.Send(new UploadManufacturerExcelDocumentCommand(id, UserId, ClientId, streamFile, fileName, request.Caption, request.SectionName));
                            break;
                        }
                    case SectionNameConstant.SOExcelDocument:
                        {
                            var salesOrderId = id;
                            response = await _mediator.Send(new UploadSOExcelDocumentCommand(streamFile, fileName, request.Caption, sectionName, salesOrderId, UserId, ClientId, SectionNameConstant.SOExcelDocument));
                            break;
                        }
                    default:
                        return NotFound(_sectionNotFoundMessage);
                }

                return Ok(response);
            }
        }

        [HttpPost("{id}/pdf-documents")]
        public async Task<IActionResult> UploadPdfDocumentsAsync([FromRoute] int id, [FromForm] UploadDocumentRequest request)
        {
            if (request.File == null || request.File.Length == 0)
            {
                return BadRequest("Uploaded file is empty");
            }

            var response = new BaseResponse<int>();
            using (var streamFile = request.File.OpenReadStream())
            {
                string sectionName = request.SectionName.Split("_")[0].ToUpper();
                string fileName = FormatDocumentFileName(request.File.FileName, request.SectionName, ClientId);
                switch (sectionName)
                {
                    case SectionNameConstant.Manufacturer:
                        if (!await CheckPermissionManufacturerPdf()) return StatusCode(StatusCodes.Status403Forbidden);
                        response = await _mediator.Send(new AddPdfManufacturerCommand(streamFile, fileName, request.Caption, sectionName, id, UserId, ClientId));
                        break;
                    case SectionNameConstant.Company:
                        {
                            if (!await CheckAddCompanyPdfPermission()) return StatusCode(StatusCodes.Status403Forbidden);
                            var companyId = request.SectionName.Split("_")[2];
                            if (companyId.IsNullOrWhiteSpace()) break;
                            await ValidateInactiveCompany(Int32.Parse(companyId), "Add Company Certificate Pdf", "Cannot add Company Certificate Pdf for an Inactive Company");
                            response = await _mediator.Send(new UploadCompanyPdfCommand(streamFile, fileName, request.Caption, sectionName, id, UserId, ClientId));
                            break;
                        }
                    case SectionNameConstant.CompanyCertificate:
                        {
                            if (!await CheckViewCompanyPdfPermissionAsync()) return StatusCode(StatusCodes.Status403Forbidden);
                            var companyId = request.SectionName.Split("_")[2];
                            if (companyId.IsNullOrWhiteSpace()) break;
                            await ValidateInactiveCompany(Int32.Parse(companyId), "Add Company Certificate Pdf", "Cannot add Company Certificate Pdf for an Inactive Company");
                            response = await _mediator.Send(new UploadCompanyCertificatePdfCommand(streamFile, fileName, request.Caption, sectionName, id, Int32.Parse(companyId), UserId, ClientId));
                            break;
                        }
                    case string secName when secName is SectionNameConstant.CustomerOnlyPO or SectionNameConstant.SalesOrderPdf:
                        {
                            var salesOrderId = request.SectionName.Split("_")[2];
                            var filetype = sectionName switch
                            {
                                SectionNameConstant.CustomerOnlyPO => SectionNameConstant.CustomerOnlyPOFileType,
                                SectionNameConstant.SalesOrderPdf => SectionNameConstant.SalesOrderPdfFileType,
                                _ => throw new ArgumentException("Invalid section name", sectionName)
                            };
                            response = await _mediator.Send(new UploadCustomerOnlyPoPdfCommand(streamFile, fileName, request.Caption, sectionName, id, UserId, ClientId, filetype));
                            break;
                        }
                    case SectionNameConstant.EUUDocument:
                        {
                            var salesOrderLineId = id;
                            var filetype = SectionNameConstant.EUUDocumentFileType;
                            fileName = fileName.Replace("SOLINEID", salesOrderLineId.ToString());
                            response = await _mediator.Send(new UploadSoLineEuuPdfCommand(streamFile, fileName, request.Caption, sectionName, salesOrderLineId, UserId, ClientId, filetype));
                            break;
                        }
                    default:
                        return NotFound(_sectionNotFoundMessage);
                }

                return Ok(response);
            }
        }

        [HttpPost("upload/sourcing-results-import-file")]
        [ValidateAntiForgeryToken]
        [ApiAuthorize(false, SecurityFunction.Orders_BOMDetail_Import_Export_SourcingResult)]
        public async Task<IActionResult> UploadSourcingResultsImportFileAsync([FromForm] UploadFileRequest request)
        {
            if (request.File == null || request.File.Length == 0)
            {
                return BadRequest("Uploaded file is empty");
            }

            string fileExtension = Path.GetExtension(request.File.FileName);
            string newFileName = $"{ClientId}_BOM_BomImport_{DateTime.Now.ToString("yyyyMMddHHmmssfff")}{fileExtension}";

            using (var stream = request.File.OpenReadStream())
            {
                var query = new UploadImportDataFileCommand()
                {
                    File = stream,
                    FolderName = BlobStorageSubFolders.Bom,
                    FileName = newFileName,
                };
                var response = await _mediator.Send(query);
                if (response.Data != null)
                {
                    response.Data.OriginalFileName = request.File.FileName;
                }

                return Ok(response);
            }
        }

        [HttpDelete("pdf-documents")]
        public async Task<IActionResult> DeletePdfDocumentsForSectionAsync([FromQuery] int id, string sectionName, string fileName)
        {
            if (string.IsNullOrEmpty(fileName) || string.IsNullOrEmpty(sectionName)) return BadRequest();

            var section = sectionName.Split('_')[0].ToUpper();
            var result = new BaseResponse<bool>();

            switch (section)
            {
                case SectionNameConstant.Manufacturer:
                    if (!await CheckPermissionManufacturerPdf()) return StatusCode(StatusCodes.Status403Forbidden);
                    result = await _mediator.Send(new DeletePdfForManufacturerCommand(id));
                    break;
                case SectionNameConstant.Company:
                    {
                        if (!await CheckViewCompanyPdfPermissionAsync()) return StatusCode(StatusCodes.Status403Forbidden);
                        var companyId = sectionName.Split("_")[2];
                        if (companyId.IsNullOrWhiteSpace()) break;
                        await ValidateInactiveCompany(Int32.Parse(companyId), "Delete Company Certificate Pdf", "Cannot delete Company Certificate Pdf for an Inactive Company");

                        result = await _mediator.Send(new DeleteCompanyPdfCommand(id));
                        break;
                    }
                case SectionNameConstant.CompanyCertificate:
                    {
                        if (!await CheckViewCompanyPdfPermissionAsync()) return StatusCode(StatusCodes.Status403Forbidden);
                        var companyId = sectionName.Split("_")[2];
                        if (companyId.IsNullOrWhiteSpace()) break;
                        await ValidateInactiveCompany(Int32.Parse(companyId), "Delete Company Certificate Pdf", "Cannot delete Company Certificate Pdf for an Inactive Company");

                        result = await _mediator.Send(new DeleteCompanyCertificatePdfCommand(id));
                        break;
                    }
                case string secName when secName is SectionNameConstant.CustomerOnlyPO or SectionNameConstant.SalesOrderPdf or SectionNameConstant.SORDocument:
                    result = await _mediator.Send(new DeleteSalesOrderPdfCommand(id));
                    break;
                case SectionNameConstant.EUUDocument:
                    {
                        result = await _mediator.Send(new DeleteSoLineEuuPdfCommand(id));
                        break;
                    }
                default:
                    return NotFound(_sectionNotFoundMessage);
            }
            if (!result.Success) return BadRequest("Delete fail");
            string blobName = $"{section}/{fileName}";
            await _blobStorageService.DeleteBlobAsync(BlobStorage.DocumentHeadersContainerName, blobName);

            return Ok(result);
        }

        [HttpDelete("excel-documents")]
        public async Task<IActionResult> DeleteExcelDocumentsForSectionAsync([FromQuery] int id, string sectionName, string fileName)
        {
            if (string.IsNullOrEmpty(fileName) || string.IsNullOrEmpty(sectionName)) return BadRequest();

            var section = sectionName.Split('_')[0].ToUpper();
            var result = new BaseResponse<bool>();

            switch (section)
            {
                case SectionNameConstant.ManufacturerExcel:
                    result = await _mediator.Send(new DeleteExcelForManufacturerCommand(id));
                    break;
                case SectionNameConstant.SOExcelDocument:
                    {
                        var salesOrderExcelId = id;
                        result = await _mediator.Send(new DeleteSalesOrderExcelCommand(salesOrderExcelId));
                        break;
                    }
                default:
                    return NotFound(_sectionNotFoundMessage);
            }
            if (!result.Success) return BadRequest("Delete fail");
            string blobName = $"{section}/{fileName}";
            await _blobStorageService.DeleteBlobAsync(BlobStorage.DocumentHeadersContainerName, blobName);

            return Ok(result);
        }


        [HttpGet("{id}/image-documents")]
        public async Task<IActionResult> GetImagesDocumentsForSectionAsync([FromRoute] int id, [FromQuery] string sectionName)
        {
            if (sectionName.IsNullOrWhiteSpace()) return BadRequest();
            var section = sectionName.Split('_')[0].ToUpper();
            switch (section)
            {
                case SectionNameConstant.HubRFQ:
                    {
                        var query = new GetSourcingImageQuery(id, Culture);
                        var response = await _mediator.Send(query);
                        return Ok(response);
                    }
                case SectionNameConstant.SOLine:
                    {
                        var query = new GetListStockImageByStockIdQuery(id, Culture);
                        var response = await _mediator.Send(query);
                        return Ok(response);
                    }
                default:
                    return BadRequest(_sectionNotFoundMessage);
            }
        }

        [HttpGet("media")]
        public async Task<IActionResult> GetMediaAsync([FromQuery] MediaDocumentRequest request)
        {
            var query = new GetMediaQuery
            {
                ImageSourceFrom = request.ImageSourceFrom,
                ImageName = request.ImageName,
                Type = request.Type,
            };
            var response = await _mediator.Send(query);
            if (response.Success && response.Data != null)
            {
                Response.Headers.TryAdd("Content-Disposition", $"inline; filename=\"{request.ImageName}\"");
                return File(response.Data.Content!, response.Data.ContentType!);
            }
            return BadRequest("File not found.");
        }

        [HttpPost("media")]
        public async Task<IActionResult> UploadMediaAsync([FromForm] UploadMultipleMediaDocumentRequest request)
        {
            if (!IsPOHub)
            {
                return Forbid();
            }
            var listFilesUpload = new List<DragDropFileUploadItem>();
            if (request.Files == null || request.Files.Count == 0)
            {
                return BadRequest();
            }

            for (int i = 0; i < request.Files.Count; i++)
            {
                var file = request.Files[i];
                listFilesUpload.Add(new DragDropFileUploadItem
                {
                    RefId = request.RefId,
                    File = file.OpenReadStream(),
                    FileName = GetStockImageFilename(file.FileName, (int)request.RefId!),
                    Caption = request.Caption[i] ?? string.Empty
                });
            }

            string sectionName = request.SectionName.Split("_")[0].ToUpper();

            var command = new PostMultipleMediaDocumentCommand
            {
                SectionName = sectionName,
                UpdateBy = UserId,
                GIId = request.GIId,
                FileUploadItems = listFilesUpload,
                RefId = request.RefId
            };

            var response = await _mediator.Send(command);

            return Ok(response);
        }

        [HttpDelete("media")]
        public async Task<IActionResult> DeleteMediaAsync([FromQuery] int id, string sectionName, string fileName)
        {
            var result = new BaseResponse<bool>();
            
            if (!IsPOHub)
            {
                return Forbid();
            }
            else if (string.IsNullOrEmpty(fileName) || string.IsNullOrEmpty(sectionName))
            {
                return BadRequest(result);
            }

            var section = sectionName.Split('_')[0].ToUpper();
            switch (section)
            {
                case SectionNameConstant.HubRFQ:
                    result = await _mediator.Send(new DeleteSourcingImageCommand(id));
                    break;
                default:
                    return NotFound(_sectionNotFoundMessage);
            }
            if (!result.Success) return BadRequest(result);

            string blobName = $"{BlobStorage.MediaContainerName}/{fileName}";
            await _blobStorageService.DeleteBlobAsync(BlobStorage.DocumentHeadersContainerName, blobName);

            result.Success = true;
            result.Data = true;
            return Ok(result);
        }



        private static string FormatDocumentFileName(string fileName, string sectionName, int clientId)
        {
            string ext = Path.GetExtension(fileName);
            return $"{clientId}_{sectionName.Split('_')[0].ToUpper()}_{sectionName.Split('_')[1].ToUpper()}_{DateTime.Now:yyyyMMddHHmmssfff}{ext}";
        }

        private async Task<BaseResponse<IEnumerable<DocumentDto>>> GetListPdfDocumentsForManufacturerAsync(int manufacturerId)
        {
            var query = new GetListPdfForManufacturerQuery(manufacturerId, Culture);
            return await _mediator.Send(query);
        }

        private async Task<bool> CheckPermissionManufacturerPdf()
        {
            var sectionLevelPermissionList = new List<SecurityFunction>()
                    {
                        SecurityFunction.Contact_ManufacturerDetail_View
                    };
            if (!_securityManager.CheckGeneralPermission(SecurityFunction.ContactSection_View) ||
                !await _securityManager.CheckFunctionPermissions(UserId, false, sectionLevelPermissionList))
            {
                return false;
            }
            return true;
        }

        private async Task<bool> CheckViewCompanyPdfPermissionAsync()
        {
            await _securityManager.LoadSectionLevelPermissions(UserId, (int)SiteSection.Contact, isDataOtherClient: false);

            if (!_securityManager.CheckGeneralPermission(SecurityFunction.ContactSection_View)) return false;

            if (_securityManager.CheckSectionLevelPermission(SecurityFunction.Contact_CompanyDetail_AllCompanies_View)
                || _securityManager.CheckSectionLevelPermission(SecurityFunction.Contact_CompanyDetail_Customer_View)
                || _securityManager.CheckSectionLevelPermission(SecurityFunction.Contact_CompanyDetail_Supplier_View)
                || _securityManager.CheckSectionLevelPermission(SecurityFunction.Contact_CompanyDetail_Prospects_View))
                return true;

            return false;
        }

        private async Task<bool> CheckAddCompanyPdfPermission()
        {
            var sectionLevelPermissionList = new List<SecurityFunction>()
            {
                SecurityFunction.Contact_CompanyDetail_PDFDocument_Add
            };
            if (!await _securityManager.CheckFunctionPermissions(UserId, false, sectionLevelPermissionList) ||
                !await CheckViewCompanyPdfPermissionAsync())
                return false;

            return true;
        }

        private async Task ValidateInactiveCompany(int companyId, string propertyMessage, string errorMessage)
        {
            var checkCompanyInactiveQuery = new CheckCompanyInactiveQuery(companyId);
            var companyInactiveResponse = await _mediator.Send(checkCompanyInactiveQuery);
            if (companyInactiveResponse.Success && companyInactiveResponse.Data)
            {
                throw new ValidationException(new List<BaseError>() {
                    new BaseError()
                    {
                        PropertyMessage = propertyMessage,
                        ErrorMessage = errorMessage
                    }
                });
            }
        }

        private static string GetStockImageFilename(string fileName, int stockNo)
        {
            string ext = Path.GetExtension(fileName);
            return $"{stockNo}_{DateTime.Now:yyyyMMddHHmmssfff}{Guid.NewGuid().ToString()}{ext}";
        }
    }
}
