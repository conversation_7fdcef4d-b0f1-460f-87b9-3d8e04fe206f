@page
@using GlobalTrader2.Orders.UI.Areas.Orders.Pages.Quotes.Details
@using GlobalTrader2.SharedUI.Helper
@using Microsoft.Extensions.Localization
@using GlobalTrader2.SharedUI.Services
@model IndexModel
@using GlobalTrader2.SharedUI.Areas.Common.Pages.Shared.Components.OrderSelectionMenu
@using GlobalTrader2.SharedUI.Areas.Common.Pages.Shared.Components.SourcingLinks
@using GlobalTrader2.SharedUI.Areas.Common.Pages.Shared.Components.OrderQuickJumpMenu

@inject IStringLocalizer<GlobalTrader2.SharedUI.CommonResources> _commonLocalizer
@inject IViewLocalizer _localizer
@inject SessionManager _sessionManager
@inject SettingManager _settingManager

@inject IViewLocalizer _localizer

@section LeftSidebar {
    @await Component.InvokeAsync(nameof(OrderQuickJumpMenu))
    @await Component.InvokeAsync(nameof(OrderSelectionMenu))
    @await Component.InvokeAsync(nameof(SourcingLinksMenu))
}

@{
    var sectionBoxClasses = HtmlHelperExtensions.GetSectionBoxClass();
    var headerClasses = HtmlHelperExtensions.GetHeaderClass();
    var contentClasses = HtmlHelperExtensions.GetContentClass();
    var accordionHeaderIconClass = HtmlHelperExtensions.GetAccordionHeaderIconClass();
    var col1Width = 4;
    var col2Width = 4;
    var labelWidth = 3;
    var valueWidth = 12 - labelWidth;
}

@section HeadBlock {
    @await Html.PartialAsync("Partials/_ThirdPartyStyleSheetsPartial")
}
@{
    ViewData["Title"] = "Quote " + Model.QuoteForPage.QuoteNumber;
}

<div id="quote-details-container" class="mb-3 page-content-container">
    <div class="mt-2">
        <span class="d-flex gap-2 justify-content-end">
            <button class="btn btn-primary" id="quote-details-add-new">
                <img src="/img/icons/plus.svg" alt="Add icon" width="18" height="18">
                <span class="lh-base">Add New Quote</span>
            </button>
        </span>
    </div>

    <div class="d-flex justify-content-between align-items-start">
        <div>

            <h2 class="page-primary-title pt-2">@_localizer["Quote"] @Model.QuoteForPage.QuoteNumber</h2>
            <div class="d-flex group-name align-items-center gap-1">
                <a id="company-detail-link" data-company-detail-id="@Model.QuoteForPage?.CompanyNo" href="/Contact/AllCompanies/Details?cm=@Model.QuoteForPage?.CompanyNo"> @Model.QuoteForPage?.CompanyName </a>
                </span>
                @if (!string.IsNullOrWhiteSpace(Model.AdvisoryNotes))
                {
                    <img width="14" height="14" src="~/img/icons/circle-exclamation-red.svg" title="@Model.AdvisoryNotes" alt="Company Advisory Notes" />
                }
            </div>
            <div>
                <b class="status-text me-2">@_localizer["Status"]</b>
                <span class="status-text me-2">
                    @Model.QuoteForPage.QuoteStatusName
                </span>
            </div>
            @if (Model.IsDifferentClient)
            {
                <span class="client-text highlight">
                    <b>
                        @Model.QuoteForPage.ClientName
                    </b>
                </span>

            }
        </div>

    </div>

    <div class="mb-3" id="quote-main-information-wrapper">
        <div id="quote-main-information-box" class="@sectionBoxClasses">
            <h3 class="@headerClasses">
                <span class="@accordionHeaderIconClass"></span>
                <span class="section-box-title">@_commonLocalizer["Main Information"]</span>
                <span class="section-box-button-group gap-2 flex-wrap">
                    
                    @if (Model.ShowEditQuoteMainInfoButton)
                    {
                        <button class="btn btn-primary" id="quote-main-information-edit-btn" title="Edit">
                            <img src="~/img/icons/plus.svg" alt="Edit" width="18" height="18" />
                            <span class="lh-base">@_commonLocalizer["Edit"]</span>
                        </button>
                    }
                    @if (Model.ShowCreateSOButton)
                    {
                        <button class="btn btn-primary" id="quote-main-info-createSO-btn" title="Create SO">
                            <img src="~/img/icons/plus.svg" alt="CreateSO" width="18" height="18" />
                            <span class="lh-base">@_commonLocalizer["Create SO"]</span>
                        </button>
                    }
                    @if (Model.ShowViewTreeButton)
                    {
                        <button class="btn btn-primary" id="quote-main-info-view-tree-btn" title="View Tree">
                            <img src="~/img/icons/plus.svg" alt="View Tree" width="18" height="18" />
                            <span class="lh-base">@_localizer["View Tree"]</span>
                        </button>
                    }
                    @if (Model.ShowCloseQuoteMainInfoButton)
                    {
                        <button class="btn btn-danger" id="quote-main-info-close-btn" disabled title="Close">
                            <img src="~/img/icons/slash.svg" alt="Close" width="18" height="18" />
                            <span class="lh-base">@_localizer["Close"]</span>
                        </button>
                    }
                </span>
            </h3>

            <div id="quote-main-information-content" class="w-100 @contentClasses">
                <div id="quote-main-info-wrapper" class="row">
                    <div id="quote-main-info-warning-section" class="alert alert-warning d-flex align-items-center gap-2 d-none" role="alert">
                        <span>
                            <img width="18" height="18" src="/img/icons/triangle-exclamation-solid.svg"
                                 alt="triangle exclamation solid icon">
                        </span>
                        <span>@_localizer["AS9120Warning"]</span>
                    </div>
                    <div class="form-error-summary d-none mb-2 align-items-center">
                        <img src="/img/icons/x-octagon.svg" alt="X-icon">
                        <p class="m-0"></p>
                    </div>
                    <div class="col-6">
                        <div class="row bg-info-hover">
                            <label for="customer" class="col-3 form-label fw-bold">@_localizer["Customer"]</label>
                            <div class="col-9 d-flex gap-2 align-items-top">
                                <a class="text-break dt-hyper-link" id="customer-link" href="">
                                    <span name="customer"></span>
                                </a>
                                <div class="d-flex align-items-center gap-1">
                                    <span id="customer-onstop-icon"></span>
                                    <span id="customer-advisory-icon"></span>
                                </div>                              
                            </div>
                        </div>
                        <div class="d-flex align-items-center bg-info-hover">
                            <label for="approved-customer" class="col-3 form-label fw-bold">@_localizer["Approved-Customer"]</label>
                            <input id="approved-customer" type="checkbox" name="approved-customer" class="form-check-input check-sm col-auto ms-2" disabled>
                        </div>
                        <div class="row bg-info-hover">
                            <label for="buyer" class="col-3 form-label fw-bold">@_localizer["Buyer"]</label>
                            <div class="col-9">
                                <a class="text-break dt-hyper-link" id="buyer-link" href="">
                                    <span name="buyer"></span>
                                </a>
                            </div>
                        </div>
                        <div class="row bg-info-hover">
                            <label for="salesperson" class="col-3 form-label fw-bold">@_localizer["Salesperson"]</label>
                            <span name="salesperson" class="col-9 text-break"></span>
                        </div>
                        <div class="row bg-info-hover">
                            <label for="division-sales" class="col-3 form-label fw-bold">@_localizer["Division-Sales"]</label>
                            <span name="division-sales" class="col-9 text-break"></span>
                        </div>
                        <div class="row bg-info-hover">
                            <label for="division-header" class="col-3 form-label fw-bold">@_localizer["Division-Header"]</label>
                            <span name="division-header" class="col-9 text-break"></span>
                        </div>
                        <div class="row bg-info-hover">
                            <label for="date-quoted" class="col-3 form-label fw-bold">@_localizer["Date-Quoted"]</label>
                            <span name="date-quoted" class="col-9 text-break"></span>
                        </div>
                        <div class="row bg-info-hover">
                            <label for="terms" class="col-3 form-label fw-bold">@_localizer["Terms"]</label>
                            <span name="terms" class="col-9 text-break"></span>
                        </div>
                        <div class="row bg-info-hover">
                            <label for="incoterms" class="col-3 form-label fw-bold">@_localizer["Incoterms"]</label>
                            <span name="incoterms" class="col-9 text-break"></span>
                        </div>
                        <div class="row bg-info-hover">
                            <label for="support-team-member-to-update" class="col-3 form-label fw-bold">@_localizer["Support-Team-Member-to-update"]</label>
                            <span name="support-team-member-to-update" class="col-9 text-break"></span>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="row bg-info-hover">
                            <label for="currency" class="col-3 form-label fw-bold">@_localizer["Currency"]</label>
                            <span name="currency" class="col-9 text-break"></span>
                        </div>
                        <div class="row bg-info-hover">
                            <label for="estimated-freight" class="col-3 form-label fw-bold">@_localizer["Estimated-Freight"]</label>
                            <span name="estimated-freight" class="col-9 text-break"></span>
                        </div>
                        <div class="row bg-info-hover">
                            <label for="notes-to-customer" class="col-3 form-label fw-bold">@_localizer["Note-to-customer"]</label>
                            <span name="notes-to-customer" class="col-9 text-break"></span>
                        </div>
                        <div class="row bg-info-hover">
                            <label for="internal-notes" class="col-3 form-label fw-bold">@_localizer["Internal-notes"]</label>
                            <span name="internal-notes" class="col-9 text-break"></span>
                        </div>
                        <div class="row bg-info-hover">
                            <label for="purchasing-notes" class="col-3 form-label fw-bold">@_localizer["Purchasing-notes"]</label>
                            <span name="purchasing-notes" class="col-9 text-break"></span>
                        </div>
                        <div class="d-flex align-items-center bg-info-hover">
                            <label for="source-of-supply-required" class="col-3 form-label fw-bold">@_localizer["Source-of-Supply-Required"]</label>
                            <input id="source-of-supply-required" type="checkbox" name="source-of-supply-required" class="form-check-input check-sm col-auto ms-2" disabled>
                        </div>
                        <div class="d-flex align-items-center bg-info-hover">
                            <label for="mark-as-important" class="col-3 form-label fw-bold">@_localizer["Important"]</label>
                            <input id="mark-as-important" type="checkbox" name="mark-as-important" class="form-check-input check-sm col-auto ms-2" disabled>
                        </div>
                        <div class="row bg-info-hover">
                            <label for="status" class="col-3 form-label fw-bold">@_localizer["Status"]</label>
                            <span name="status" class="col-9 text-break"></span>
                        </div>
                        <div class="row bg-info-hover">
                            <label for="inhouse-AS6081-part-included" class="col-3 form-label fw-bold">@_localizer["AS6081"]</label>
                            <span name="inhouse-AS6081-part-included" class="col-9 text-break"></span>
                        </div>
                    </div>
                    <div class="text-end">
                        <i name="last-updated"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div id="quote-lines-wrapper">
        <div id="quote-lines-box" class="@sectionBoxClasses">
            <h3 class="@headerClasses">
                <span class="@accordionHeaderIconClass"></span>
                <span class="section-box-title">Lines</span>
                <span class="section-box-button-group gap-2">
                    <button class="btn btn-primary" id="quote-lines-add-btn" title="Add">
                        <i class="fa-solid fa-plus"></i>
                        <span class="lh-base">@_commonLocalizer["Add"]</span>
                    </button>
                    <button class="btn btn-primary" id="quote-lines-calculator-btn">
                        <img src="~/img/icons/plus.svg" alt="Add icon" width="18" height="18" />
                        <span class="lh-base">Sales Calculator</span>
                    </button>
                </span>
            </h3>

            <div id="quote-lines-content" class="@contentClasses">
                <div id="quote-lines-loading">
                    <div id="order-quotes-details-lines-nav-tabs-wrapper" class="border-bottom">
                        <div class="nav nav-tabs border-0 justify-content-start" id="orderQuotesDetailsLinesTab" role="tablist">
                            <button class="nav-item nav-link active" id="open-tab"
                                    data-bs-toggle="tab" data-bs-target="#open"
                                    data-tabid="0" type="button" role="tab"
                                    aria-controls="open" aria-selected="true">
                                Open
                            </button>
                            <button class="nav-item nav-link" id="closed-tab"
                                    data-bs-toggle="tab" data-bs-target="#open"
                                    type="button" role="tab" aria-controls="open"
                                    aria-selected="false" data-tabid="1">
                                Closed
                            </button>
                            <button class="nav-item nav-link" id="all-tab"
                                    data-bs-toggle="tab" data-bs-target="#open"
                                    type="button" role="tab" aria-controls="open"
                                    aria-selected="false" data-tabid="2">
                                All
                            </button>
                        </div>
                    </div>

                    <div class="tab-content mt-3" id="orderQuotesDetailsLinesTabContent">
                        <div class="tab-pane fade show active" id="open" role="tabpanel" aria-labelledby="order-quotes-details-lines-tab">
                            <div id="quote-lines-results-table-container">
                                <table id="quote-lines-results-table" class="table simple-table display responsive nowrap">
                                    <thead>
                                        <tr>
                                            <th></th>
                                        </tr>
                                    </thead>
                                    <tbody></tbody>
                                </table>
                                <div id="quote-lines-results-resize" class="resize-handle d-none" data-resize-table="quote-lines-results-table"></div>
                            </div>

                            <div class="line-info border p-1 d-flex justify-content-end gap-3">
                                <div class="d-flex gap-1 align-items-center">
                                    <span class="fw-bold">Estimated Freight</span>
                                    <span id="lines-details-estimated-freight" class="bg-white p-1 rounded border border-primary text-primary"></span>
                                </div>
                                <div class="d-flex gap-1 align-items-center">
                                    <span class="fw-bold">Total</span>
                                    <span id="lines-details-total" class="bg-white p-1 rounded border border-primary text-primary"></span>
                                </div>
                            </div>

                            <div id="order-quotes-details-line-details-wrapper" class="d-none">
                                <div class="d-flex gap-2 align-items-center my-2">
                                    <span class="fw-bold">Line Details</span>
                                    <button class="btn btn-primary" id="lines-details-toggle-btn">
                                        <img src="~/img/icons/minus.svg" alt="Add icon" width="18" height="18" />
                                    </button>
                                </div>
                                <div class="container-fluid p-2" id="order-quotes-details-line-details">
                                    <div class="extra-margin">
                                        <a href="javascript:void(0)" id="previous-button"><img alt="left" src="/img/icons/angles-left.svg"></a>
                                        <span id="oqdLineDetail" class="fw-bold"></span>
                                        <a href="javascript:void(0)" id="next-button"><img alt="right" src="/img/icons/angles-right.svg"></a>
                                    </div>
                                    <div class="line my-5px"></div>
                                    <div class="row align-items-start">
                                        <div class="col-@col1Width" id="oqdLineDetail-column-1">
                                            <div class="row">
                                                <label for="company" class="col-@labelWidth form-label fw-bold">Part No</label>
                                                <span class="col-@valueWidth text-break pe-0" name="partNo"></span>
                                            </div>
                                        </div>
                                        <div class="col-@col2Width" id="oqdLineDetail-column-2">
                                            <div class="row">
                                                <label for="company" class="col-@labelWidth form-label fw-bold">Close Reason</label>
                                                <span class="col-@valueWidth text-break pe-0" name="closeReason"></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <fieldset class="collapsible-fieldset" style="border-color: #81dcd1">
                                    <legend id="legend-order-quotes-details" style="border-color: #81dcd1">
                                        <span class="d-flex align-items-center gap-1">
                                            <button class="btn btn-outline-primary fieldset-expand-button" aria-expanded="false" data-bs-toggle="collapse" data-bs-target="#order-quotes-details-collapsible">
                                                <img class="fieldset-expand-icon" src="~/img/icons/green-plus.svg" alt="Edit icon" width="18" height="18" />
                                                <img class="fieldset-collapse-icon" src="~/img/icons/green-minus.svg" alt="Edit icon" width="18" height="18" />
                                            </button>
                                            <span class="collapsible-title">Original Offer</span>
                                        </span>
                                    </legend>
                                    <div class="collapse" id="order-quotes-details-collapsible">
                                        <span class="d-flex flex-wrap gap-2">
                                            <button type="button" class="btn btn-primary" id="original-offer-edit-btn">
                                                <img src="/img/icons/edit.svg" alt="Edit icon" width="18" height="18">
                                                <span class="lh-base">Edit</span>
                                            </button>
                                        </span>
                                    </div>
                                </fieldset>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div id="original-offer-edit-dialog" class="dialog-container d-none" title="Lines" data-table-id="">
        <div id="original-offer-edit-filter"></div>
    </div>
</div>

@await Html.PartialAsync("AddQuoteLine/_AddQuoteLine")

@section Scripts {
    <script>
        var quoteMainInfoStateValue = {
            id: '@Model.QuoteId'
        }
        var stateValue = {
            quoteId: '@Model.QuoteId'
        }
        var localizedTitles = {
            quoteLinesResultsEmptyMsg: "@_localizer["Sorry, no data was found"]",
            lastUpdated: "@_commonLocalizer["Last updated"]",
        };
    </script>
    @await Html.PartialAsync("Partials/_DataTablesScriptsPartial")
    <script src="@_settingManager.GetCdnUrl("/js/modules/orders/sourcing/helpers/sourcing-section-box.helper.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/widgets/resize-data-table-extensions.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/widgets/common-table.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/lib/jquery-validation/dist/jquery.validate.js")"></script>
    <script src="@_settingManager.GetCdnUrl("/js/helper/datatables-detail-helper.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/helper/string-helper.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/widgets/drop-down.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/helper/page-url-function-helper.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/widgets/custom-date-picker.js")" asp-append-version="true"></script>

    <environment include="Development">
        <script src="/js/modules/orders/quotes/containers/quote-detail.js" type="module" asp-append-version="true"></script>
    </environment>
    <environment include="Development">
        <script src="/js/modules/orders/quotes/containers/add-line.js" type="module" asp-append-version="true"></script>
    </environment>


    <environment exclude="Development">
        <script src="/dist/js/orders-quote-detail.bundle.js" type="module" asp-append-version="true"></script>
    </environment>
    <environment exclude="Development">
        <script src="/dist/js/orders-quote-details-add-new-line.bundle.js" type="module" asp-append-version="true"></script>
    </environment>
    <script src="~/js/widgets/common-table.js" asp-append-version="true"></script>
    <script src="~/js/helper/datatables-detail-helper.js" asp-append-version="true"></script>
}