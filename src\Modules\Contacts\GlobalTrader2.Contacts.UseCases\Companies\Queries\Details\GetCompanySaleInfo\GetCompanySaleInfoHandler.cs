﻿using System.Data;
using AutoMapper;
using GlobalTrader2.Core.Bases;
using GlobalTrader2.Core.StoreName;
using GlobalTrader2.Core;
using MediatR;
using Microsoft.Data.SqlClient;
using GlobalTrader2.Dto.SalesOrder;
using GlobalTrader2.Core.Helpers;
using GlobalTrader2.Core.Domain.Entities;
using GlobalTrader2.Contacts.UseCases.Companies.Queries.Details.ConvertCurrency;


namespace GlobalTrader2.Contacts.UseCases.Companies.Queries.Details.GetCompanySaleInfo
{
    public class GetCompanySaleInfoHandler : IRequestHandler<GetCompanySaleInfoQuery, BaseResponse<CompanySaleInfoDto>>
    {
        private readonly IBaseRepository<Core.Domain.Entities.SalesInfo> _repository;
        private readonly IBaseRepository<ThisYearSalesOrderValueReadModel> _thisYearSaleOrderValueRepository;
        private readonly IBaseRepository<LastYearSalesOrderValueReadModel> _lastYearSaleOrderValueRepository;
        private readonly IBaseRepository<InvoiceNotExportedReadModel> _invoiceNotExportedRepository;
        private readonly IBaseRepository<SalesOrderForCompanyReadModel> _salesOrderOpenRepository;
        private readonly IBaseRepository<SalesOrderSummaryValuesReadModel> _salesOrderValueRepository;
        private readonly IBaseRepository<ConvertedValueBetweenTwoCurrenciesReadModel> _convertRepository; 
        private readonly IMapper _mapper;
        private readonly IMediator _mediator;
        public GetCompanySaleInfoHandler(IBaseRepository<Core.Domain.Entities.SalesInfo> repository,
            IBaseRepository<ThisYearSalesOrderValueReadModel> thisYearSaleOrderValueRepository,
            IBaseRepository<LastYearSalesOrderValueReadModel> lastYearSaleOrderValueRepository,
            IBaseRepository<InvoiceNotExportedReadModel> invoiceNotExportedRepository,
            IBaseRepository<SalesOrderForCompanyReadModel> salesOrderOpenRepository,
            IBaseRepository<SalesOrderSummaryValuesReadModel> salesOrderValueRepository,
            IBaseRepository<ConvertedValueBetweenTwoCurrenciesReadModel> convertRepository,
            IMapper mapper,
            IMediator mediator)
        {
            _mapper = mapper;
            _repository = repository;
            _thisYearSaleOrderValueRepository = thisYearSaleOrderValueRepository;
            _lastYearSaleOrderValueRepository = lastYearSaleOrderValueRepository;
            _invoiceNotExportedRepository = invoiceNotExportedRepository;
            _salesOrderOpenRepository = salesOrderOpenRepository;
            _salesOrderValueRepository = salesOrderValueRepository;
            _convertRepository = convertRepository;
            _mediator = mediator;
        }

        public async Task<BaseResponse<CompanySaleInfoDto>> Handle(GetCompanySaleInfoQuery request, CancellationToken cancellationToken)
        {
            var response = new BaseResponse<CompanySaleInfoDto>();
            var parameters = new List<SqlParameter>
                {
                    new SqlParameter("@CompanyId", SqlDbType.Int){Value = request.CompanyId},
                };
            var exportedInvoiceParameters = new List<SqlParameter>
                {
                    new SqlParameter("@CompanyNo", SqlDbType.Int){Value = request.CompanyId},
                };
            var entity = (await _repository.SqlQueryRawAsync(
            $"{StoredProcedures.Get_Company_SalesInfo} @CompanyId", parameters.ToArray())).AsEnumerable().FirstOrDefault();

            if (entity == null)
                throw new ArgumentNullException($"Not found Company {request.CompanyId}");

            var thisYearValue = (await _thisYearSaleOrderValueRepository.SqlQueryRawAsync(
                $"{StoredProcedures.Get_Summarise_ThisYear_SalesOrderValue} @CompanyId", parameters.ToArray())).AsEnumerable().FirstOrDefault();
                                   
            var lastYearValue = (await _lastYearSaleOrderValueRepository.SqlQueryRawAsync(
                $"{StoredProcedures.Get_Summarise_LastYear_SalesOrderValue} @CompanyId", parameters.ToArray())).AsEnumerable().FirstOrDefault();

            var totalNotExportedValue = (await _invoiceNotExportedRepository.SqlQueryRawAsync(
                $"{StoredProcedures.Get_Invoice_Not_Exported} @CompanyNo", exportedInvoiceParameters.ToArray())).AsEnumerable().FirstOrDefault();

            var dto = _mapper.Map<CompanySaleInfoDto>(entity);
            dto.CreditLimit = Functions.FormatConvertedCurrency(Convert.ToDecimal(entity.CreditLimit), request.CultureInfo, entity.SOCurrencyNo , entity.SOCurrencyCode ?? string.Empty, Convert.ToDecimal(entity.CreditLimit) / Convert.ToDecimal(entity.ExchangeRate), request.ClientCurrencyID, request.ClientCurrencyCode, 2);
            dto.ActualCreditLimit = Functions.FormatConvertedCurrency(Convert.ToDecimal(entity.ActualCreditLimit),request.CultureInfo, entity.SOCurrencyNo, entity.SOCurrencyCode ?? string.Empty, Convert.ToDecimal(entity.ActualCreditLimit) / Convert.ToDecimal(entity.ExchangeRate), request.ClientCurrencyID, request.ClientCurrencyCode, 2);
            dto.CreditLimitRaw = Functions.FormatCurrency(Convert.ToDecimal(entity.CreditLimit ?? 0), request.CultureInfo,string.Empty, 2, false);
            dto.ActualCreditLimitRaw = Functions.FormatCurrency(Convert.ToDecimal(entity.ActualCreditLimit ?? 0), request.CultureInfo, string.Empty, 2, false);
            dto.Balance= Functions.FormatConvertedCurrency(Convert.ToDecimal(entity.Balance),request.CultureInfo, entity.SOCurrencyNo, entity.SOCurrencyCode ?? string.Empty, Convert.ToDecimal(entity.Balance) / Convert.ToDecimal(entity.ExchangeRate), request.ClientCurrencyID, request.ClientCurrencyCode, 2);
            dto.Days1  = Functions.FormatConvertedCurrency(Convert.ToDecimal(entity.Days1)   , request.CultureInfo,entity.SOCurrencyNo, entity.SOCurrencyCode ?? string.Empty,Convert.ToDecimal( entity.Days1) / Convert.ToDecimal(entity.ExchangeRate), request.ClientCurrencyID, request.ClientCurrencyCode, 2);
            dto.Days30 = Functions.FormatConvertedCurrency(Convert.ToDecimal(entity.Days30)  , request.CultureInfo,entity.SOCurrencyNo,entity.SOCurrencyCode ?? string.Empty,Convert.ToDecimal( entity.Days30) / Convert.ToDecimal(entity.ExchangeRate), request.ClientCurrencyID, request.ClientCurrencyCode, 2);
            dto.Days60 = Functions.FormatConvertedCurrency(Convert.ToDecimal(entity.Days60)  , request.CultureInfo,entity.SOCurrencyNo,entity.SOCurrencyCode ?? string.Empty,Convert.ToDecimal(entity.Days60) / Convert.ToDecimal(entity.ExchangeRate), request.ClientCurrencyID, request.ClientCurrencyCode, 2);
            dto.Days90 = Functions.FormatConvertedCurrency(Convert.ToDecimal(entity.Days90)  , request.CultureInfo,entity.SOCurrencyNo,entity.SOCurrencyCode ?? string.Empty,Convert.ToDecimal(entity.Days90) / Convert.ToDecimal(entity.ExchangeRate), request.ClientCurrencyID, request.ClientCurrencyCode, 2);
            dto.Days120 = Functions.FormatConvertedCurrency(Convert.ToDecimal(entity.Days120), request.CultureInfo,entity.SOCurrencyNo,entity.SOCurrencyCode ?? string.Empty,Convert.ToDecimal(entity.Days120) / Convert.ToDecimal(entity.ExchangeRate), request.ClientCurrencyID, request.ClientCurrencyCode, 2);
            dto.CurrentMonth = Functions.FormatConvertedCurrency(Convert.ToDecimal(entity.CurrentMonth), request.CultureInfo,entity.SOCurrencyNo,entity.SOCurrencyCode ?? string.Empty,Convert.ToDecimal( entity.CurrentMonth) / Convert.ToDecimal(entity.ExchangeRate), request.ClientCurrencyID, request.ClientCurrencyCode, 2);
            dto.InsuredAmount = Functions.FormatConvertedCurrency(Convert.ToDecimal(entity.InsuredAmount), request.CultureInfo,entity.InsuredAmountCurrencyNo,entity.InsuredAmountCurrencyCode ?? string.Empty, Convert.ToDecimal(entity.InsuredAmount )/ Convert.ToDecimal(entity.ExchangeRate), request.ClientCurrencyID, request.ClientCurrencyCode, 2);
            dto.InsuredAmountRaw = Functions.FormatCurrency(entity.InsuredAmount ?? 0, request.CultureInfo, string.Empty, 2, false);
            dto.YearToDate = Functions.FormatCurrency(thisYearValue != null ? Convert.ToDecimal(thisYearValue.SalesOrderValueYTDInBase) : 0, request.CultureInfo, entity.SOCurrencyCode ?? string.Empty, 2, true);
            dto.LastYear = Functions.FormatCurrency(lastYearValue != null ? Convert.ToDecimal(lastYearValue.SalesOrderValueLastYearInBase) : 0, request.CultureInfo, entity.SOCurrencyCode ?? string.Empty, 2, true);
            dto.InvoiceNotExport = Functions.FormatCurrency(totalNotExportedValue != null ? Convert.ToDecimal(totalNotExportedValue.TotalNotExportedValue) : 0, request.CultureInfo, entity.SOCurrencyCode ?? string.Empty, 2, true);
            dto.InsuranceFileNo = string.IsNullOrEmpty(entity.InsuranceFileNo) ? string.Empty : entity.InsuranceFileNo.Trim();
            
            var summaryOpenSOLineValue = await GetBalanceWithOpenSalesOrder(request.CompanyId, entity.SOCurrencyNo);
            
            decimal dblBalanceWithOpenOrders = Convert.ToDecimal(entity.Balance ?? 0) + summaryOpenSOLineValue;
            dto.BalanceWithOpenSalesOrders = Functions.FormatCurrency(dblBalanceWithOpenOrders, request.CultureInfo, entity.SOCurrencyCode ?? string.Empty, 2, true);
            dto.LastUpdatedByText = entity.DLUP.ToString("dd MMMM yyyy HH:mm");
            dto.SOCurrencyDescription = Functions.FormatCurrencyDescription(entity.SOCurrencyDescription ?? string.Empty, entity.SOCurrencyCode ?? string.Empty);

            response.Data = dto;
            response.Success = true;
            return response;
        }

        private async Task<decimal> GetBalanceWithOpenSalesOrder(int CompanyId, int? soCurrencyNo) {
            decimal dblOpenSOTotal = 0M;
            DateTime currencyDate;
            var getSalesOrderOpenParamters = new List<SqlParameter>() {
                new SqlParameter("@CompanyId", System.Data.SqlDbType.Int){Value = CompanyId}
            };
            var openSalesOrder = await _salesOrderOpenRepository.SqlQueryRawAsync($"{StoredProcedures.Select_All_SalesOrder_Open_for_Company} @CompanyId", getSalesOrderOpenParamters.ToArray());
            if (!openSalesOrder.Any()) {
                return dblOpenSOTotal;
            }
            foreach (var so in openSalesOrder)
            {
                var getOpenLineSummaryValueParameter = new List<SqlParameter>() { new SqlParameter("@SalesOrderId", SqlDbType.Int) { Value = so.SalesOrderId} };
                var soTotal = (await _salesOrderValueRepository.SqlQueryRawAsync($"{StoredProcedures.Select_SalesOrder_OpenLineSumaryValues} @SalesOrderId", getOpenLineSummaryValueParameter.ToArray())).AsEnumerable().FirstOrDefault();
                currencyDate = so.CurrencyDate == null ? so.DateOrdered : (DateTime)so.CurrencyDate;
                if (soTotal?.CurrencyNo != soCurrencyNo)
                {
                    dblOpenSOTotal += await ConvertValueBetweenTwoCurrencies(soTotal?.TotalValue, Convert.ToInt32(soTotal?.CurrencyNo) , soCurrencyNo, currencyDate);
                }
                else
                {
                    dblOpenSOTotal += Convert.ToDecimal(soTotal?.TotalValue);
                }
            }

            return dblOpenSOTotal;
        }

        private async Task<decimal> ConvertValueBetweenTwoCurrencies(double? dblValueToConvert, int intFromCurrencyID, int? intToCurrencyID, DateTime dtmExchangeRateDate) { 
			double dbl = (dblValueToConvert == null) ? 0 : Convert.ToDouble(dblValueToConvert);
            if (!dbl.Equals(0.0))
            {
                var convertedValue = await _mediator.Send(new GetConvertedValueQuery(
                    dblValueToConvert, intFromCurrencyID, intToCurrencyID ?? 0, dtmExchangeRateDate));
                return Convert.ToDecimal(convertedValue);
            }
            return Convert.ToDecimal(dbl);
        }
    }
}
