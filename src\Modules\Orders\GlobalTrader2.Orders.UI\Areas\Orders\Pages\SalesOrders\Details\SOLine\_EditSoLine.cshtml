@inject IViewLocalizer _localizer
@inject IStringLocalizer<GlobalTrader2.SharedUI.CommonResources> _commonLocalizer
@inject IStringLocalizer<GlobalTrader2.SharedUI.MessageResources> _messageLocalizer

<div id="edit-sales-order-line-dialog" title="@_commonLocalizer["Lines"]" style="display:none" class="fs-12">
    <div class="dialog-description">
        <div class="d-flex justify-content-between">
            <div>
                <h5 class="text-uppercase dialog-title">&nbsp;</h5>
            </div>
            <span>
                <span class="me-1 required">*</span>@_commonLocalizer["denotes a required field"]
            </span>
        </div>
        <div class="line"></div>
        <span class="mb-1">
            @_localizer["Enter the changed details for the Line and press Save"]
        </span>
    </div>
    <div class="form-error-summary" style="display: none;">
        <img src="~/img/icons/x-octagon.svg" alt="X icon" />
        <div>
            <p>@_messageLocalizer["There were some problems with your form."]</p>
            <p>@_messageLocalizer["Please check below and try again."]</p>
        </div>
    </div>
    <form method="post" id="edit-sales-order-line-form" class="row common-form">
        @Html.AntiForgeryToken()
        <div class="col-12 form-control-wrapper">
            <p class="form-label">@_localizer["Sales Order"]</p>
            <span id="edit-sales-order-line-sales-order-number"></span>
        </div>
        <div class="col-12 form-control-wrapper">
            <p class="form-label">@_localizer["Customer"]</p>
            <span id="edit-sales-order-line-customer-name"></span>
        </div>
        <div class="col-12 form-control-wrapper">
            <p class="form-label">@_localizer["Service"]</p>
            <span id="edit-sales-order-line-service"></span>
        </div>
        <div class="col-12 form-control-wrapper">
            <label for="edit-sales-order-line-description" class="form-label">@_localizer["Description"]</label>
            <input type="text" class="form-control form-input" id="edit-sales-order-line-service-description" name="serviceDescription" maxlength="30">
        </div>
        <div class="col-12 form-control-wrapper">
            <p class="form-label">@_localizer["Qty Shipped"]</p>
            <span id="edit-sales-order-line-qty-shipped"></span>
        </div>
        <div class="col-12 form-control-wrapper">
            <p class="form-label">@_localizer["Qty Allocated"]</p>
            <span id="edit-sales-order-line-qty-allocated"></span>
        </div>
        <div class="col-12 form-control-wrapper">
            <p class="form-label">@_localizer["Qty BackOrder"]</p>
            <span id="edit-sales-order-line-qty-backorder"></span>
        </div>
        <div class="col-12 form-control-wrapper">
            <p class="form-label">@_localizer["Inhouse AS6081 testing required?*"]</p>
            <span id="edit-sales-order-line-inhouse-as6081-testing"></span>
        </div>
        <div class="col-12 form-control-wrapper">
            <p class="form-label">@_localizer["Part No"]</p>
            <span id="edit-sales-order-line-part-no"></span>
        </div>
        <div class="col-12 form-control-wrapper">
            <label for="edit-sales-order-line-part-no-search-select" class="form-label">@_localizer["Part No"]<span class="required"> *</span></label>
            <input type="text" class="form-control form-input" id="edit-sales-order-line-part-no-search-select" maxlength="30"/>
            <input type="hidden" id="edit-sales-order-line-part-no-search-select-value" name="partNo" maxlength="30"/>
        </div>
        <div class="col-12 form-control-wrapper">
            <label for="edit-sales-order-line-eccn-code" class="form-label">@_localizer["ECCN Code"]</label>
            <input type="text" class="form-control form-input" id="edit-sales-order-line-eccn-code-search-select" />
            <input type="hidden" id="edit-sales-order-line-eccn-code-search-select-value" name="eccnCode"/>
            <span class="text-break text-danger" id="edit-sales-order-line-eccn-code-error" style="display: none; margin-top: 3px;">
                @_localizer["There is a difference between SO Line's ECCN Code and PO Line's ECCN Code"]
            </span>
        </div>
        <div class="col-12 form-control-wrapper">
            <label for="edit-sales-order-line-rohs" class="form-label">@_localizer["RoHS"]</label>
            <select id="edit-sales-order-line-rohs" class="dropdown" name="rohs">
            </select>
        </div>
        <div class="col-12 form-control-wrapper">
            <label for="edit-sales-order-line-date-code" class="form-label">@_localizer["Date Code"]</label>
            <input type="text" class="form-control form-input" id="edit-sales-order-line-date-code" maxlength="5" name="dateCode">
        </div>
        <div class="col-12 form-control-wrapper">
            <p class="form-label">@_localizer["Quantity"]</p>
            <span id="edit-sales-order-line-quantity"></span>
        </div>
        <div class="col-12 form-control-wrapper">
            <label for="edit-sales-order-line-quantity" class="form-label">@_localizer["Quantity"]<span class="required"> *</span></label>
            <input type="text" class="form-control form-input" id="edit-sales-order-line-quantity-input" name="quantity" maxlength="10"/>
        </div>
        <div class="col-12 form-control-wrapper">
            <label for="edit-sales-order-line-cost" class="form-label">@_localizer["Cost"]<span class="required"> *</span></label>
            <div class="d-flex align-items-center gap-1">
                <input type="text" class="form-control form-input special-input" id="edit-sales-order-line-cost-input" name="cost"/>
                <span id="edit-sales-order-line-cost-currency"></span>
            </div>
        </div>
        <div class="col-12 form-control-wrapper">
            <p class="form-label">@_localizer["Price"]</p>
            <span id="edit-sales-order-line-price"></span>
        </div>
        <div class="col-12 form-control-wrapper">
            <label for="edit-sales-order-line-price" class="form-label">@_localizer["Price"]<span class="required"> *</span></label>
            <div class="d-flex align-items-center gap-1">
                <input type="text" class="form-control form-input special-input" id="edit-sales-order-line-price-input" name="price"/>
                <span id="edit-sales-order-line-price-currency"></span>
            </div>
        </div>
        <div class="col-12 form-control-wrapper">
            <label for="edit-sales-order-line-cust-part-no" class="form-label">@_localizer["Cust Part No"]</label>
            <input type="text" class="form-control form-input" id="edit-sales-order-line-cust-part-no" name="custPartNo" maxlength="30"/>
        </div>
        <div class="col-12 form-control-wrapper">
            <label for="edit-sales-order-line-manufacturer" class="form-label">@_localizer["Manufacturer"]</label>
            <input type="text" class="form-control form-input" id="edit-sales-order-line-manufacturer-search-select"/>
            <input type="hidden" id="edit-sales-order-line-manufacturer-search-select-value" name="manufacturer"/>
        </div>
        <div class="col-12 form-control-wrapper">
            <label for="edit-sales-order-line-product" class="form-label">@_localizer["Product"]<span
                    class="fw-bold ms-1 required"> *</span></label>
            <input type="text" class="form-control form-input" id="edit-sales-order-line-product-search-select"/>
            <input type="hidden" id="edit-sales-order-line-product-search-select-value" name="product"/>
        </div>
        <div class="col-12 form-control-wrapper">
            <label for="edit-sales-order-line-package" class="form-label">@_localizer["Package"]</label>
            <input type="text" class="form-control form-input" id="edit-sales-order-line-package-search-select"/>
            <input type="hidden" id="edit-sales-order-line-package-search-select-value" name="package"/>
        </div>
        <div class="col-12 form-control-wrapper">
            <label for="edit-sales-order-line-date-promised" class="form-label" >@_localizer["Date Promised"]<span class="required"> *</span></label>
            <span class="d-flex gap-1">
                <input type="text" class="form-control form-input datepicker" style="background: #fafaf4; cursor: pointer;" 
                    placeholder="@_commonLocalizer["DD/MM/YYYY"]" 
                    id="edit-sales-order-line-date-promised" 
                    name="datePromised"
                    readonly>
            </span>
        </div>
        <div class="col-12 form-control-wrapper">
            <label for="edit-sales-order-line-promise-reason" class="form-label">@_localizer["Promise Reason"]<span
                    class="fw-bold ms-1 required"> *</span></label>
            <select id="edit-sales-order-line-promise-reason" class="dropdown" name="promiseReason">
            </select>
        </div>
        <div class="col-12 form-control-wrapper">
            <label for="edit-sales-order-line-date-required" class="form-label" >@_localizer["Date Required"]<span class="required"> *</span></label>
            <span id="edit-sales-order-line-date-required"></span>
        </div>
        <div class="col-12 form-control-wrapper">
            <label for="edit-sales-order-line-date-required-input" class="form-label" >@_localizer["Date Required"]<span class="required"> *</span></label>
            <span class="d-flex gap-1">
                <input type="text" class="form-control form-input datepicker" style="background: #fafaf4; cursor: pointer;" 
                    placeholder="@_commonLocalizer["DD/MM/YYYY"]" 
                    id="edit-sales-order-line-date-required-input" 
                    name="dateRequired"
                    readonly>
            </span>
        </div>
        <div class="col-12 form-control-wrapper">
            <label for="edit-sales-order-line-ship-asap" class="form-label">@_localizer["Ship ASAP"]</label>
            <input type="checkbox" class="form-control form-check-input" id="edit-sales-order-line-ship-asap" name="shipAsap">
        </div>
        <div class="col-12 form-control-wrapper">
            <label for="edit-sales-order-line-product-source" class="form-label">@_localizer["Product Source"]<span
                    class="fw-bold ms-1 required"> *</span></label>
            <select id="edit-sales-order-line-product-source" class="dropdown" name="productSource">
            </select>
        </div>
        <div class="col-12 form-control-wrapper">
            <label for="edit-sales-order-line-shipping-instructions" class="form-label">@_localizer["Shipping Instructions to WH only"]</label>
            <textarea class="form-control form-textarea height-auto" style="height:auto" 
                id="edit-sales-order-line-shipping-instructions" 
                data-directive="maxLength" 
                name="shippingInstructions"
                maxlength="2000" rows="2"></textarea>
        </div>
        <div class="col-12 form-control-wrapper">
            <label for="edit-sales-order-line-printed-notes" class="form-label">@_localizer["Printed line notes to invoice"]</label>
            <textarea class="form-control form-textarea height-auto" style="height:auto" 
                id="edit-sales-order-line-printed-notes"
                data-directive="maxLength" 
                name="printedNotes"
                maxlength="2000" rows="2"></textarea>
        </div>
        <div class="col-12 form-control-wrapper">
            <label for="edit-sales-order-line-po-delivery-date" class="form-label" >@_localizer["PO Delivery Date"]<span class="required"> *</span></label>
            <span class="d-flex gap-1">
                <input type="text" class="form-control form-input datepicker" style="background: #fafaf4; cursor: pointer;" 
                    placeholder="@_commonLocalizer["DD/MM/YYYY"]" 
                    id="edit-sales-order-line-po-delivery-date" 
                    name="poDeliveryDate"
                    readonly>
            </span>
        </div>
        <div class="col-12 form-control-wrapper">
            <label for="edit-sales-order-line-msl" class="form-label">@_localizer["MSL"]<span
                    class="fw-bold ms-1 required"> *</span></label>
            <select id="edit-sales-order-line-msl" class="dropdown" name="msl">
            </select>
        </div>
        <div class="col-12 form-control-wrapper">
            <label for="edit-sales-order-line-contract-no" class="form-label">@_localizer["Contract No"]</label>
            <input type="text" class="form-control form-input" id="edit-sales-order-line-contract-no" name="contractNo" maxlength="50">
        </div>
        <div class="col-12 form-control-wrapper">
            <label for="edit-sales-order-line-print-hazardous-warning" class="form-label">@_localizer["Print hazardous warning"]</label>
            <input type="checkbox" class="form-control form-check-input" id="edit-sales-order-line-print-hazardous-warning" name="printHazardousWarning">
        </div>
    </form>
</div>

<script>
    const editSoLineLocalizedStrings = {
        nonServiceTitle: "@_localizer["Edit Sales Order Line"]",
        serviceTitle: "@_localizer["Edit Sales Order - Service Line"]",
    };
</script>