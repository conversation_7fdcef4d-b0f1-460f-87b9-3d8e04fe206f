﻿import { CheckboxTextInput } from "../../../../components/checkbox-text-input/checkbox-text-input.js?v=#{BuildVersion}#";
import { StepperEvent } from "../../../../components/stepper/constants/stepper-event.constant.js?v=#{BuildVersion}#";
import { StepperComponent } from "../../../../components/stepper/stepper.component.js?v=#{BuildVersion}#";
import { TableFilterComponent } from "../../../../components/table-filter/table-filter.component.js?v=#{BuildVersion}#";
import { SELECT_ITEM_FROM_REQUIREMENTS_SOURCING_FILTER_INPUTS, SELECT_ITEM_FROM_REQUIREMENTS_FILTER_INPUTS } from "../constants/add-line-filters.constants.js?v=#{BuildVersion}#";

$(async () => {
    const $quoteLineAddButton = $('#quote-lines-add-btn');
    const $searchSelectItemFromRequirementBtn = $('#search-select-item-from-requirement-btn');
    const $searchSelectItemFromRequirementSourcingResultBtn = $('#search-select-item-from-requirement-sourcing-result-btn');
    const $searchSelectItemFromServiceBtn = $('#search-select-item-from-service-search-btn');

    const $addQuoteLineDialog = $('#add-quote-line-dialog');

    const $selectItemFromRequirementContainer = $('#select-item-from-requirement-container');
    const $selectItemFromRequirementSourcingResultContainer = $('#select-item-from-requirement-sourcing-result-container');
    const $selectItemFromServiceContainer = $('#select-item-from-service-container');
    const $selectItemFromStockContainer = $('#select-item-from-stock-container');

    const sectionMap = {
        fromRequirements: $selectItemFromRequirementContainer,
        fromRequirementsSourcingResults: $selectItemFromRequirementSourcingResultContainer,
        fromService: $selectItemFromServiceContainer,
        fromStock: $selectItemFromStockContainer
    };

    let addQuoteLineDialog;
    let addQuoteLineStepper;
    let selectItemFromServiceSearchInput;
    let $saveBtn;
    let $cancelBtn;
    let $continueBtn;

    initDialogs();
    initStepper();
    initSearchInput();
    await initTableFilters();
    setupEventListeners();

    function setupEventListeners() {
        $quoteLineAddButton.on('click', (e) => {
            e.preventDefault();
            addQuoteLineDialog.dialog("open");
        })

        $searchSelectItemFromRequirementBtn.on('click', (e) => {
            e.preventDefault();
            onSearchSelectItemFromRequirement();
        })

        $searchSelectItemFromRequirementSourcingResultBtn.on('click', (e) => {
            e.preventDefault();
            onSearchSelectItemFromRequirementSourcingResult();
        })

        $searchSelectItemFromServiceBtn.on('click', (e) => {
            e.preventDefault();
            onSearchSelectItemFromService();
        })
    }

    function initDialogs() {
        addQuoteLineDialog = $addQuoteLineDialog.dialog({
            width: "73vw",
            buttons: [
                {
                    text: window.localizedStrings.save,
                    class: 'btn btn-primary btn-save fw-normal',
                    html: `<i class="fa-solid fa-check"></i>${window.localizedStrings.save}`,
                    click: () => {
                        $(this).dialog("close");
                    }
                },
                {
                    text: window.localizedStrings.cancel,
                    class: 'btn btn-danger btn-cancel fw-normal',
                    html: `<i class="fa-solid fa-ban"></i>${window.localizedStrings.cancel}`,
                    click: function () {
                        $(this).dialog("close");
                    },
                },
                {
                    text: window.localizedStrings.continue,
                    class: 'btn btn-primary btn-continue fw-normal',
                    html: `<i class="fa-solid fa-arrow-right"></i>${window.localizedStrings.continue}`,
                    click: function () {
                        addQuoteLineStepper.onNextStep();
                    },
                },
            ],
            open: function (event, ui) {
                $(this).removeClass('d-none');
                $saveBtn = $(this).parent().find('.btn-save');
                $cancelBtn = $(this).parent().find('.btn-cancel');
                $continueBtn = $(this).parent().find('.btn-continue');

                $saveBtn.prop("disabled", true);
            }
        })
    }

    function initStepper() {
        const steps = [
            { title: "Select Source", clickToMove: true },
            { title: "Select Item", clickToMove: true },
            { title: "Enter Details", clickToMove: true },
            { title: "Lot Details", clickToMove: true }
        ];

        addQuoteLineStepper = new StepperComponent(document.getElementById("add-quote-line-stepper-container"), steps);

        addQuoteLineStepper.on(StepperEvent.STEP_CHANGE, ({ step }) => handleStepChange(step));
    }

    async function initTableFilters() {
        const selectItemFromRequirementFilter = new TableFilterComponent('#select-item-from-requirement-filter', '', {
            inputConfigs: SELECT_ITEM_FROM_REQUIREMENTS_FILTER_INPUTS,
            showButtons: false,
            showTitle: false,
            wrapperClass: 'bg-none m-0 p-0'
        }); 

        const selectItemFromRequirementSourcingFilter = new TableFilterComponent('#select-item-from-requirement-sourcing-result-filter', '', {
            inputConfigs: SELECT_ITEM_FROM_REQUIREMENTS_SOURCING_FILTER_INPUTS,
            showButtons: false,
            showTitle: false,
            wrapperClass: 'bg-none m-0 p-0'
        });

        await selectItemFromRequirementFilter.init();
        await selectItemFromRequirementSourcingFilter.init();
    }

    function initSearchInput() {
        selectItemFromServiceSearchInput = new CheckboxTextInput($('#select-item-from-service-search-container'), '<b>Name</b>', 'nameSearch', 'nameSearchInput');
        selectItemFromServiceSearchInput.init();
    }

    function handleStepChange(currentStep) {
        if (currentStep === 1) {
            onLoadSelectSource();
        } else if (currentStep === 2) {
            onLoadSelectItem();
        } else if (currentStep === 3) {
            onLoadEnterDetails();
        } else if (currentStep === 4) {
            onLoadLotDetails();
        }
    }

    function onLoadSelectItem() {
        const selectSourceFormData = new FormData(document.getElementById('add-new-quote-line-select-source-form'));
        const selectedSource = selectSourceFormData.get("selectSource");

        switch (selectedSource) {
            default:
                break;
            case "newLineItem":
                $saveBtn.prop("disabled", false);
                $continueBtn.hide();
                addQuoteLineStepper.disableStep(2);
                addQuoteLineStepper.updateStepper(3);
                break;
            case "fromRequirements":
            case "fromRequirementsSourcingResults":
            case "fromService":
            case "fromStock":
                Object.entries(sectionMap).forEach(([key, $el]) => {
                    key === selectedSource ? $el.show() : $el.hide();
                });
                break;
            case "fromLot":
                $saveBtn.prop("disabled", false);
                $continueBtn.hide();
                addQuoteLineStepper.disableStep(2);
                addQuoteLineStepper.disableStep(3);
                addQuoteLineStepper.updateStepper(4);
                onLoadLotDetails();
                break;
        }
    }

    function onLoadEnterDetails() {

    }

    function onLoadLotDetails() {

    }

    function onLoadSelectSource() {
        $saveBtn.prop("disabled", true);
        $continueBtn.show();
    }

    function onSearchSelectItemFromService() {
        console.log("Search Select Item From Service clicked");
    }

    function onSearchSelectItemFromRequirement() {
        console.log("Search Select Item From Requirement clicked");
    }

    function onSearchSelectItemFromRequirementSourcingResult() {
        console.log("Search Select Item From Requirement Sourcing Result clicked");
    }
})