using AutoFixture;
using GlobalTrader2.Aggregator.UseCases.DataList.GlobalCountyListDropDown;
using GlobalTrader2.Core;
using GlobalTrader2.Core.Domain.Entities;
using Moq;
using System.Linq.Expressions;

namespace GlobalTrader2.Aggregator.Test.DataList.GlobalDetailsListDropDown
{
    public class GetCountryWarningMessageHandlerTest
    {
        private readonly Mock<IBaseRepository<Country>> _mockRepository;
        private readonly GetCountryWarningMessageHandler _handler;
        private readonly GetCountryWarningMessageQuery _query;
        private readonly IFixture _fixture;

        public GetCountryWarningMessageHandlerTest()
        {
            _mockRepository = new Mock<IBaseRepository<Country>>();
            _handler = new GetCountryWarningMessageHandler(_mockRepository.Object);
            _fixture = new Fixture();
            _query = _fixture.Create<GetCountryWarningMessageQuery>();
        }

        [Fact]
        public async Task Handle_WhenCountryExists_ReturnsSuccessWithWarningMessage()
        {
            // Arrange
            var expectedWarningMessage = "This is a warning message";
            var entity = new Country { CountryId = 1, WarningMessage = expectedWarningMessage };


            _mockRepository
                       .Setup(x => x.GetAsync(It.IsAny<Expression<Func<Country, bool>>>()))
                       .ReturnsAsync(entity);

            // Act
            var response = await _handler.Handle(_query, CancellationToken.None);

            // Assert
            Assert.True(response.Success);
            Assert.Equal(expectedWarningMessage, response.Data);
        }
    }
}
