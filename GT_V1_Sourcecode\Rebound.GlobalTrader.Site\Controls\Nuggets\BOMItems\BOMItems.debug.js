Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");

Rebound.GlobalTrader.Site.Controls.Nuggets.BOMItems = function (element) {
    Rebound.GlobalTrader.Site.Controls.Nuggets.BOMItems.initializeBase(this, [element]);
    this._intBOMID = -1;
    this._intCountTab = 0;
    this._intCountStock = 0;
    this._isEnable = false;
    this._intCustomerRequirementID = -1;
    this._blnRequirementClosed = false;
    this._intCompanyID = -1;
    this._hasSourcingResult = false;
    this._blnRequirementReleased = false;
    this._allExistInSourcingResult = false;
    this._intSelectedLineNo = 0;
    var bomStatus = "";
    this._BomCode = "";
    this._BomName = "";
    this._BomCompanyName = "";
    this._BomCompanyNo = 0;
    this._SalesManNo = 0;
    this._SalesManName = 0;
    this._lineLength = 0;
    this._isRequestToPurchaseQuote = false;
    this._blnAllHasProduct = false;
    this._blnAllHasDelDate = false;
    this._blnAllItemHasDelDate = false;
    this._blnAllItemHasProduct = false;
    this._blnCanRelease = false;
    this._isClosed = false;
    this._blnCanRecal = true;
    //var isClosed=false;
    this._CustReqNo = -1;
    this._blnCanNoBid = true;
    this._isNoBid = false;
    this._ReqIds = [];
    this._ReqNos = [];
    this._BOMNo = -1;
    this._UpdateByPH = -1;
    this._ReqSalesman = -1;
    this._intContact2No = -1;
    this._RequestToPOHubBy = -1;
    this._RequestToPOHubByNo = -1;
    this._SupportTeamMemberNo = null;
    this._blnRelease = false;
    this._blnCanApplyPartWatch = false;
};

Rebound.GlobalTrader.Site.Controls.Nuggets.BOMItems.prototype = {
    get_intBOMID: function () { return this._intBOMID; }, set_intBOMID: function (value) { if (this._intBOMID !== value) this._intBOMID = value; },
    get_tblStock: function () { return this._tblStock; }, set_tblStock: function (value) { if (this._tblStock !== value) this._tblStock = value; },
    get_ctlTabStrip: function () { return this._ctlTabStrip; }, set_ctlTabStrip: function (v) { if (this._ctlTabStrip !== v) this._ctlTabStrip = v; },
    get_ctlTabStock: function () { return this._ctlTabStock; }, set_ctlTabStock: function (v) { if (this._ctlTabStock !== v) this._ctlTabStock = v; },
    get_IsEnable: function () { return this._isEnable; }, set_isEnable: function (v) { this._isEnable = v; },
    get_ibtnRelease: function () { return this._ibtnRelease; }, set_ibtnRelease: function (value) { if (this._ibtnRelease !== value) this._ibtnRelease = value; },
    get_blnPOHub: function () { return this._blnPOHub; }, set_blnPOHub: function (value) { if (this._blnPOHub !== value) this._blnPOHub = value; },
    get_ibtnAdd: function () { return this._ibtnAdd; }, set_ibtnAdd: function (v) { if (this._ibtnAdd !== v) this._ibtnAdd = v; },
    get_ibtnDelete: function () { return this._ibtnDelete; }, set_ibtnDelete: function (value) { if (this._ibtnDelete !== value) this._ibtnDelete = value; },
    get_ibtnUnRelease: function () { return this._ibtnUnRelease; }, set_ibtnUnRelease: function (value) { if (this._ibtnUnRelease !== value) this._ibtnUnRelease = value; },
    get_pnlLineDetail: function () { return this._pnlLineDetail; }, set_pnlLineDetail: function (v) { if (this._pnlLineDetail !== v) this._pnlLineDetail = v; },
    get_pnlLoadingLineDetail: function () { return this._pnlLoadingLineDetail; }, set_pnlLoadingLineDetail: function (v) { if (this._pnlLoadingLineDetail !== v) this._pnlLoadingLineDetail = v; },
    get_pnlLineDetailError: function () { return this._pnlLineDetailError; }, set_pnlLineDetailError: function (v) { if (this._pnlLineDetailError !== v) this._pnlLineDetailError = v; },
    get_ibtnNoBid: function () { return this._ibtnNoBid; }, set_ibtnNoBid: function (value) { if (this._ibtnNoBid !== value) this._ibtnNoBid = value; },
    get_ibtnRecallNoBid: function () { return this._ibtnRecallNoBid; }, set_ibtnRecallNoBid: function (value) { if (this._ibtnRecallNoBid !== value) this._ibtnRecallNoBid = value; },
    get_ibtnNote: function () { return this._ibtnNote; }, set_ibtnNote: function (value) { if (this._ibtnNote !== value) this._ibtnNote = value; },
    //start code by umendra
    get_ibtnImportSrcReslt: function () { return this._ibtnImportSrcReslt; }, set_ibtnImportSrcReslt: function (value) { if (this._ibtnImportSrcReslt !== value) this._ibtnImportSrcReslt = value; },
    //end code by umendra

    get_ibtnApplyPartwatch: function () { return this._ibtnApplyPartwatch; }, set_ibtnApplyPartwatch: function (value) { if (this._ibtnApplyPartwatch !== value) this._ibtnApplyPartwatch = value; },
    get_ibtnRemovePartwatch: function () { return this._ibtnRemovePartwatch; }, set_ibtnRemovePartwatch: function (value) { if (this._ibtnRemovePartwatch !== value) this._ibtnRemovePartwatch = value; },
    get_ibtnExportToExcel: function () { return this._ibtnExportToExcel; }, set_ibtnExportToExcel: function (value) { if (this._ibtnExportToExcel !== value) this._ibtnExportToExcel = value; },
    get_ibtnHubImportSR: function () { return this._ibtnHubImportSR; }, set_ibtnHubImportSR: function (value) { if (this._ibtnHubImportSR !== value) this._ibtnHubImportSR = value; },

    addStartGetData: function (handler) { this.get_events().addHandler("StartGetData", handler); },
    removeStartGetData: function (handler) { this.get_events().removeHandler("StartGetData", handler); },

    onStartGetData: function () {
        var handler = this.get_events().getHandler("StartGetData");
        if (handler) handler(this, Sys.EventArgs.Empty);
    },

    addGotDataOK: function (handler) { this.get_events().addHandler("GotDataOK", handler); },

    removeGotDataOK: function (handler) { this.get_events().removeHandler("GotDataOK", handler); },
    onGotDataOK: function () {
        var handler = this.get_events().getHandler("GotDataOK");
        if (handler) handler(this, Sys.EventArgs.Empty);
    },

    addPartSelected: function (handler) {
        this.get_events().addHandler("PartSelected", handler);
    },
    removePartSelected: function (handler) { this.get_events().removeHandler("PartSelected", handler); },
    onPartSelected: function () {

        var handler = this.get_events().getHandler("PartSelected");
        if (handler) handler(this, Sys.EventArgs.Empty);
    },

    addCallBeforeRelease: function (handler) { this.get_events().addHandler("CallBeforeRelease", handler); },
    removeCallBeforeRelease: function (handler) { this.get_events().removeHandler("CallBeforeRelease", handler); },
    onCallBeforeRelease: function () {
        var handler = this.get_events().getHandler("CallBeforeRelease");
        if (handler) handler(this, Sys.EventArgs.Empty);
    },
    addRefereshAfterRelease: function (handler) { this.get_events().addHandler("RefereshAfterRelease", handler); },
    removeRefereshAfterRelease: function (handler) { this.get_events().removeHandler("RefereshAfterRelease", handler); },
    onRefereshAfterRelease: function () {
        var handler = this.get_events().getHandler("RefereshAfterRelease");
        if (handler) handler(this, Sys.EventArgs.Empty);
    },

    addImportSourcingResultSuccess: function (handler) { this.get_events().addHandler("ImportSourcingResultSuccess", handler); },
    removeImportSourcingResultSuccess: function (handler) { this.get_events().removeHandler("ImportSourcingResultSuccess", handler); },
    onImportSourcingResultSuccess: function () {
        var handler = this.get_events().getHandler("ImportSourcingResultSuccess");
        if (handler) handler(this, Sys.EventArgs.Empty);
    },

    initialize: function () {
        $("#As6081WarningMsg").hide();
        $("#dvtxt").html("");
        Rebound.GlobalTrader.Site.Controls.Nuggets.BOMItems.callBaseMethod(this, "initialize");
        this.addRefreshEvent(Function.createDelegate(this, this.getData));
        this._tblStock.addSelectedIndexChanged(Function.createDelegate(this, this.tbl_SelectedIndexChanged));
        if (this._ibtnRelease) {
            $R_IBTN.addClick(this._ibtnRelease, Function.createDelegate(this, this.showReleaseForm));
            this._frmConfirm = $find(this._aryFormIDs[0]);
            this._frmConfirm._BomCode = this._BomCode;
            this._frmConfirm._BomName = this._BomName;
            this._frmConfirm._BomCompanyName = this._BomCompanyName;
            this._frmConfirm._BomCompanyNo = this._BomCompanyNo;
            //this._frmConfirm._SalesManNo = this._SalesManNo;
            this._frmConfirm._SalesManName = this._SalesManName;
            this._frmConfirm._CustReqNo = this._CustReqNo;
            this._frmConfirm.addCancel(Function.createDelegate(this, this.hideConfirmForm));
            this._frmConfirm.addSaveComplete(Function.createDelegate(this, this.saveCeaseComplete));
            this._frmConfirm.addNotConfirmed(Function.createDelegate(this, this.hideConfirmForm));


        }
        if (this._ibtnAdd) {
            $R_IBTN.addClick(this._ibtnAdd, Function.createDelegate(this, this.showAddForm));
            this._frmAdd = $find(this._aryFormIDs[1]);
            this._frmAdd.addCancel(Function.createDelegate(this, this.hideAddForm));
            this._frmAdd.addSaveComplete(Function.createDelegate(this, this.saveAddComplete));
            this._frmAdd.addNotConfirmed(Function.createDelegate(this, this.hideAddForm));
            this._frmAdd._intBOMID = this._intBOMID;
        }
        if (this._ibtnDelete) {
            $R_IBTN.addClick(this._ibtnDelete, Function.createDelegate(this, this.showDeleteForm));
            this._frmDelete = $find(this._aryFormIDs[2]);
            this._frmDelete.addCancel(Function.createDelegate(this, this.hideDeleteForm));
            this._frmDelete._intBOMID = this._intBOMID;
            this._frmDelete._intCustomerRequirementID = this._intCustomerRequirementID;
            this._frmDelete.addSaveComplete(Function.createDelegate(this, this.DeleteComplete));
            this._frmDelete.addNotConfirmed(Function.createDelegate(this, this.hideDeleteForm));
        }
        if (this._ibtnUnRelease) {
            $R_IBTN.addClick(this._ibtnUnRelease, Function.createDelegate(this, this.showUnReleaseForm));
            this._frmUnRelease = $find(this._aryFormIDs[3]);
            this._frmUnRelease.addCancel(Function.createDelegate(this, this.hideUnReleaseForm));
            this._frmUnRelease._intBOMID = this._intBOMID;
            this._frmUnRelease._intCustomerRequirementID = this._intCustomerRequirementID;
            this._frmUnRelease.addSaveComplete(Function.createDelegate(this, this.UnReleaseComplete));
            this._frmUnRelease.addNotConfirmed(Function.createDelegate(this, this.hideUnReleaseForm));
        }

        if (this._ibtnNoBid) {

            $R_IBTN.addClick(this._ibtnNoBid, Function.createDelegate(this, this.showNoBidConfirmForm));
            this._frmNoBidConfirm = $find(this._aryFormIDs[4]);
            this._frmNoBidConfirm._BomCode = this._BomCode;
            this._frmNoBidConfirm._BomName = this._BomName;
            this._frmNoBidConfirm._BomCompanyName = this._BomCompanyName;
            this._frmNoBidConfirm._BomCompanyNo = this._BomCompanyNo;
            this._frmNoBidConfirm._SalesManNo = this._SalesManNo;
            this._frmNoBidConfirm._SalesManName = this._SalesManName;
            this._frmNoBidConfirm._CustReqNo = this._CustReqNo;
            this._frmNoBidConfirm.addCancel(Function.createDelegate(this, this.hideNoBidConfirmForm));
            this._frmNoBidConfirm.addSaveComplete(Function.createDelegate(this, this.saveNoBidConfirmComplete));
            this._frmNoBidConfirm.addNotConfirmed(Function.createDelegate(this, this.hideNoBidConfirmForm));

        }
        if (this._ibtnRecallNoBid) {

            $R_IBTN.addClick(this._ibtnRecallNoBid, Function.createDelegate(this, this.showRecallNoBidConfirmForm));
            this._frmRecallNoBidConfirm = $find(this._aryFormIDs[4]);
            this._frmRecallNoBidConfirm._BomCode = this._BomCode;
            this._frmRecallNoBidConfirm._BomName = this._BomName;
            this._frmRecallNoBidConfirm._BomCompanyName = this._BomCompanyName;
            this._frmRecallNoBidConfirm._BomCompanyNo = this._BomCompanyNo;
            this._frmRecallNoBidConfirm._SalesManNo = this._SalesManNo;
            this._frmRecallNoBidConfirm._SalesManName = this._SalesManName;
            this._frmRecallNoBidConfirm._CustReqNo = this._CustReqNo;
            this._frmRecallNoBidConfirm.addCancel(Function.createDelegate(this, this.hideRecallNoBidConfirmForm));
            this._frmRecallNoBidConfirm.addSaveComplete(Function.createDelegate(this, this.saveRecallNoBidConfirmComplete));
            this._frmRecallNoBidConfirm.addNotConfirmed(Function.createDelegate(this, this.hideRecallNoBidConfirmForm));

        }
        if (this._ibtnNote) {

            $R_IBTN.addClick(this._ibtnNote, Function.createDelegate(this, this.showExpediteNoteForm));
            this._frmAddExpediteNote = $find(this._aryFormIDs[5]);
            this._frmAddExpediteNote.addCancel(Function.createDelegate(this, this.cancelAddExpediteNoteForm));
            this._frmAddExpediteNote.addSaveComplete(Function.createDelegate(this, this.saveAddExpediteNoteComplete));
        }
        ////start code by umendra
        //if (this._ibtnImportSrcReslt) {
        //    $R_IBTN.addClick(this._ibtnImportSrcReslt, Function.createDelegate(this, this.showImportSourcingResultForm));

        //    this._frmImportSourcing = $find(this._aryFormIDs[6]);
        //    this._frmImportSourcing._intBOMID = this._intBOMID;
        //    this._frmImportSourcing.addCancel(Function.createDelegate(this, this.cancelImportSourcingForm));
        //    $("#ctl00_cphMain_ctlBOMItems_ctlDB_ctl14_ctlImprtSrcResult_ctlDB_ctl07").hide();
        //}

        if (this._ibtnApplyPartwatch) {
            $R_IBTN.addClick(this._ibtnApplyPartwatch, Function.createDelegate(this, this.showApplyPartwatchForm));
            this._frmApplyPartwatch = $find(this._aryFormIDs[7]);
            this._frmApplyPartwatch.addCancel(Function.createDelegate(this, this.hideApplyPartwatchForm));
            this._frmApplyPartwatch._intBOMID = this._intBOMID;
            this._frmApplyPartwatch._intCustomerRequirementID = this._intCustomerRequirementID;
            this._frmApplyPartwatch.addSaveComplete(Function.createDelegate(this, this.saveApplyPartwatchConfirmComplete));
            this._frmApplyPartwatch.addNotConfirmed(Function.createDelegate(this, this.hideApplyPartwatchForm));
        }

        if (this._ibtnRemovePartwatch) {
            $R_IBTN.addClick(this._ibtnRemovePartwatch, Function.createDelegate(this, this.showRemovePartwatchForm));
            this._frmRemovePartwatch = $find(this._aryFormIDs[8]);
            this._frmRemovePartwatch.addCancel(Function.createDelegate(this, this.hideRemovePartwatchForm));
            this._frmRemovePartwatch._intBOMID = this._intBOMID;
            this._frmRemovePartwatch._intCustomerRequirementID = this._intCustomerRequirementID;
            this._frmRemovePartwatch.addSaveComplete(Function.createDelegate(this, this.saveRemovePartwatchConfirmComplete));
            this._frmRemovePartwatch.addNotConfirmed(Function.createDelegate(this, this.hideRemovePartwatchForm));
        }

        if (this._ibtnExportToExcel) {
            $R_IBTN.addClick(this._ibtnExportToExcel, Function.createDelegate(this, this.exportToExcel));
        }

        //end code by umendra
        //if (this._ibtnExportCSV) $R_IBTN.addClick(this._ibtnExportCSV, Function.createDelegate(this, this.showExportCSV));

        //HUB side import sourcing result
        if (this._ibtnHubImportSR) {
            this._frmHubImportSR = $find(this._aryFormIDs[6]);
            this._frmHubImportSR.addCancel(Function.createDelegate(this, this.cancelHubImportSRForm));
            $R_IBTN.addClick(this._ibtnHubImportSR, Function.createDelegate(this, this.showHubImportSRForm));
        }

        this.getData();
        document.getElementById("myplasegrde").addEventListener("click", Function.createDelegate(this, this.getPartDetaildata));
        document.getElementById("closePoppartdetails").addEventListener("click", Function.createDelegate(this, this.hidpartdetaildive));
    },

    dispose: function () {
        if (this.isDisposed) return;
        if (this._frmTransfer) this._frmTransfer.dispose();
        if (this._ctlTabStrip) this._ctlTabStrip.dispose();
        if (this._ctlTabStock) this._ctlTabStock.dispose();
        if (this._tblStock) this._tblStock.dispose();
        if (this._frmConfirm) this._frmConfirm.dispose();
        if (this._frmHubImportSR) this._frmHubImportSR.dispose();
        //if (this._ibtnRelease) this._ibtnRelease.dispose();
        this._intBOMID = null;
        this._ctlTabStrip = null;
        this._tblStock = null;
        this._ibtnExportCSV = null;
        this._pnlSummary = null;
        this._ibtnRelease = null;
        this._intCustomerRequirementID = null;
        this._blnRequirementClosed = null;
        this._blnRequirementReleased = null;
        this._blnPOHub = null;
        this._ibtnAdd = null;
        this._intCompanyID = null;
        this._intSelectedLineNo = null;
        this._blnAllHasDelDate = null;
        this._blnAllItemHasDelDate = null;
        this._blnAllHasProduct = null;
        this._blnAllItemHasProduct = null;
        this._blnCanRelease = null;
        this._isClosed = null;
        this._ibtnNoBid = null;
        this._isNoBid = null;
        this._ibtnRecallNoBid = null;
        this._RequestToPOHubByNo = null;
        this._ReqSalesman = null;
        this._SupportTeamMemberNo = null;
        //start code by umendra
        this._ibtnImportSrcReslt = null;
        //end code by umendra
        this._ibtnRemovePartwatch = null;
        this._ibtnExportToExcel = null;
        this._ibtnApplyPartwatch = null;
        this._blnCanApplyPartWatch = null;
        this._ibtnHubImportSR = null;
        Rebound.GlobalTrader.Site.Controls.Nuggets.BOMItems.callBaseMethod(this, "dispose");
    },


    getData: function () {
        this.getData_Start();
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/BOMItems");
        obj.set_DataObject("BOMItems");
        obj.set_DataAction("GetData");
        obj.addParameter("id", this._intBOMID);
        //obj.addParameter("percentage", this._decPercentage);
        obj.addDataOK(Function.createDelegate(this, this.getDataOK));
        obj.addError(Function.createDelegate(this, this.getDataError));
        obj.addTimeout(Function.createDelegate(this, this.getDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
        this.onStartGetData();
        //this.onSave();
    },

    getDataOK: function (args) {
        var res = args._result;
        var aryData, row;
        this._tblStock.clearTable();
        this.showLoading(false);
        this._intCustomerRequirementID = -1;
        this._blnRequirementReleased = false;
        //isClosed=this._isClosed;
        // alert(this._isClosed);
        this._intCompanyID = -1;
        this.enableButtons(false);
        this._ctlTabStock.addCountToTitle(0);
        this.showLoading(false);

        //alert(strCSS)
        if (res.Items) {
            var checkCss = false;
            for (var i = 0; i < res.Items.length; i++) {

                var row = res.Items[i];

                this._lineLength = res.Items.length; // res.length; Changes on 19-04-2018(To Enable Delete button on HUBRFQ Item)
                this._BomCode = row.BOMCode;
                this._BomName = row.BOMFullName;
                this._BomCompanyName = row.Company;
                this._BomCompanyNo = row.CompanyNo;
                this._SalesManNo = row.Salesman;
                this._SalesManName = row.SalesmanName;
                this._RequestToPOHubBy = row.RequestToPOHubBy;
                this._UpdateByPH = row.UpdateByPH;
                var IsCheckBoxEnabled = row.IsRequestToPurchaseQuote && !this._isClosed;
                var strAPartHubIPO = "No";
                var strCSS = "cusReqMainPart";
                var strCSS1 = "cusReqMainPart";
                //if (row.Alt) strCSS = "cusReqAlternatePart";
                if (!row.Alt) strCSS = "cusReqMainPart";
                if (row.Released) strCSS = "readyToShip";
                if (row.Released == false && row.HasSourcingResult == true) strCSS = "allocated";
                //if (row.Released) strCSS = "readyToShip";
                if (this._blnPOHub == true) {
                    if (row.PriceIssueBuyAndSell == true) {
                        strCSS = "notReadyToShip"
                        checkCss = true;
                    };
                }
                if (row.PartWatchHUBIPO == true) {
                    strAPartHubIPO = "Yes";

                }

                //alert(strCSS)
                if (this._allExistInSourcingResult == false) {
                    this._allExistInSourcingResult = row.HasSourcingResult;
                }
                this.disableItemAddButton(!row.IsRequestToPurchaseQuote && !this._inActive);
                this._isRequestToPurchaseQuote = row.IsRequestToPurchaseQuote;
                aryData = [
                    $R_FN.setCleanTextValue(row.IsAssignedToMe == true ? this.writeCheckbox(row.ID, i, this._tblStock) : ""),
                    $R_FN.setCleanTextValue($R_FN.setCleanTextValue(row.Alt == true ? $R_FN.showYellowTextImportant($RGT_nubButton_CustomerRequirement(row.ID, row.CustReqNo)) : $RGT_nubButton_CustomerRequirement(row.ID, row.CustReqNo)))
                    // , $R_FN.writeDoubleCellValue(row.Quantity, strAPartHubIPO)
                    , (this._blnPOHub == true) ? $R_FN.writeDoubleCellValue(row.Quantity, $R_FN.setCleanTextValue(strAPartHubIPO)) : $R_FN.setCleanTextValue(row.Quantity)
                    , $R_FN.writeDoubleCellValue($R_FN.writePartNo(row.Alt == true ? $R_FN.showYellowTextImportant(row.Part) : row.Part, row.ROHS), $R_FN.setCleanTextValue(row.CustomerPart))
                    , $R_FN.writeDoubleCellValue($RGT_nubButton_Manufacturer(row.MfrNo, row.Mfr, row.MfrAdvisoryNotes), $R_FN.setCleanTextValue(row.DC))
                    , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Product), $R_FN.setCleanTextValue(row.Package))
                    , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Company) + $R_FN.createAdvisoryNotesIcon(row.CompanyAdvisoryNotes, 'margin-left-10'), $R_FN.setCleanTextValue(row.Date))
                    , $R_FN.writeDoubleCellValue(row.TPriceInBom, row.SalesmanName)
                    , $R_FN.writeDoubleCellValue(row.MSL, row.FactorySealed)
                    , $R_FN.writeDoubleCellValue(row.IsAs6081Required, "")
                    , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.AssignedTo), "")
                    , (this._blnPOHub == true) ? $R_FN.writeDoubleCellValue(row.IsPurchaseRequestCreated == true ? "YES" : "-", $R_FN.setCleanTextValue(row.Instructions)) : $R_FN.setCleanTextValue(row.Instructions)

                ];
                var objExtraData = {
                    Part: $R_FN.setCleanTextValue(row.PartNo)
                    , Closed: row.Closed
                    , IsAssignedToMe: row.IsAssignedToMe
                    , Released: row.Released
                    , CMNo: row.CMNo
                    , HasSourcingResult: row.HasSourcingResult
                    , SourcingResult: row.SourcingResult
                    , IPOClientNo: row.IPOClientNo
                    , CustomerRequirementNumber: row.CustReqNo
                    , IsNoBid: row.IsNoBid
                    , BOMNo: row.BOMNo
                    , CMP: row.Company
                    , CMPNo: row.CompanyNo
                    , isPartWatchHUBIPO: row.PartWatchHUBIPO
                    , IsAs6081Required: row.IsAs6081Required
                    , MfrNo: row.MfrNo
                    , MfrCode: row.Mfr
                };

                this._tblStock.addRow(aryData, row.ID, (row.ID == this._intSelectedLineNo), objExtraData, strCSS);

                bomStatus = row.BOMStatus;
                if (row.IsAssignedToMe == true) {
                    this.registerCheckBox(row.ID, i, false, IsCheckBoxEnabled, this._tblStock);
                    var chk = this.getCheckBox(i, this._tblStock);
                    chk._element.setAttribute("onClick", String.format("$find(\"{0}\").getCheckedCellValue({1},{2});", this._element.id, i, row.ID));
                    chk = null;
                }




                row = null; aryData = null; objExtraData = null;
                this._tblStock.resizeColumns();
                this._ctlTabStock.addCountToTitle(i + 1);
            }
            if (checkCss == true) {
                var count = document.getElementsByClassName('first notReadyToShip notReadyToShip_First alignCenter');
                for (var i = 0; i < count.length; i++) {
                    document.getElementsByClassName('first notReadyToShip notReadyToShip_First alignCenter')[i].setAttribute('title', 'Sourcing Results having price issue kindly check and verify.');
                }

            }
        }

        if (res.Items) {
            this._intCountStock = res.Count;
            this._blnAllItemHasDelDate = res.AllHasDelDate;
            this._blnAllItemHasProduct = res.AllHasProduct;
            // this.addCustReqRows(res.Items, 0);
            var anyItemNotRelease = res.Items.find(o => o.Released == false);
            var hubrfqStatus = res.Items.find(o => o.BOMStatus != 'CLOSED' && o.BOMStatus != 'RELEASED');
            if (this._ibtnExportToExcel) $R_IBTN.enableButton(this._ibtnExportToExcel, anyItemNotRelease || hubrfqStatus);
            if (this._ibtnHubImportSR) $R_IBTN.enableButton(this._ibtnHubImportSR, anyItemNotRelease || hubrfqStatus);
        } else {
            if (this._ibtnExportToExcel) $R_IBTN.enableButton(this._ibtnExportToExcel, false);
            if (this._ibtnHubImportSR) $R_IBTN.enableButton(this._ibtnHubImportSR, false);
        }

        this.disableItemAddButton(!this._blnRequestedToPoHub && !this._inActive && !this._isClosed);
        //this.enableButtons(true);
        this.showInvalidImportWarning(res.HasImportFile && (!res.Items || res.Items.length == 0));
        this.getDataOK_End();
        Array.clear(this._ReqIds);
    },

    addCustReqRows: function (res, i) {
        if (i < res.length) {
            for (var i = 0; i < res.length; i++) {
                var row = res[i];

                this._lineLength = res.length;
                this._BomCode = row.BOMCode;
                this._BomName = row.BOMFullName;
                this._BomCompanyName = row.Company;
                this._BomCompanyNo = row.CompanyNo;
                this._SalesManNo = row.Salesman;
                this._SalesManName = row.SalesmanName;
                this._RequestToPOHubBy = row.RequestToPOHubBy;
                this._UpdateByPH = row.UpdateByPH;
                var IsCheckBoxEnabled = row.IsRequestToPurchaseQuote && !this._isClosed;
                //                if (row.SourcingResult == false) {
                //                    this._blnCanRecal = row.SourcingResult;
                //                }
                var strCSS = "cusReqMainPart";
                //////if (row.Alt) strCSS = "cusReqAlternatePart";
                ////if (!row.Alt) strCSS = "cusReqMainPart";
                ////if (row.Released) strCSS = "readyToShip";
                ////if (row.Released == false && row.HasSourcingResult == true) strCSS = "allocated";
                ////if (row.Released) strCSS = "readyToShip";
                //alert(strCSS)
                if (this._allExistInSourcingResult == false) {
                    this._allExistInSourcingResult = row.HasSourcingResult;
                }
                this.disableItemAddButton(!row.IsRequestToPurchaseQuote && !this._inActive);
                this._isRequestToPurchaseQuote = row.IsRequestToPurchaseQuote;

                //if (this._blnPOHub == true) {
                //    aryData = [
                //         this.writeCheckbox(row.ID, i, this._tblStock),
                //    //$RGT_nubButton_CustomerRequirement(row.ID, row.No)
                // // $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Alt == true ? $R_FN.showYellowTextImportant(row.ID, row.CustReqNo) : row.ID, row.CustReqNo))
                // $R_FN.setCleanTextValue($R_FN.setCleanTextValue(row.Alt == true ? $R_FN.showYellowTextImportant(row.CustReqNo) : row.CustReqNo))
                //, $R_FN.writeDoubleCellValue(row.Quantity)
                //, $R_FN.writeDoubleCellValue($R_FN.writePartNo(row.Part, row.ROHS), $R_FN.setCleanTextValue(row.CustomerPart))
                //, $R_FN.writeDoubleCellValue($RGT_nubButton_Manufacturer(row.MfrNo, row.Mfr), $R_FN.setCleanTextValue(row.DC))
                //, $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Product), $R_FN.setCleanTextValue(row.Package))
                //, $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Company), $R_FN.setCleanTextValue(row.Date))
                //, $R_FN.writeDoubleCellValue(row.TPriceInBom, row.SalesmanName)
                //, $R_FN.writeDoubleCellValue(row.IsPurchaseRequestCreated == true ? "YES" : "-", $R_FN.setCleanTextValue(row.Instructions)) /*$RGT_nubButton_POQuote(row.PurchaseQuoteId, row.PurchaseQuoteNumber)*/
                //, $R_FN.writeDoubleCellValue(row.MSL, row.FactorySealed)
                //    ];
                //}
                //else {


                //    aryData = [
                //        this.writeCheckbox(row.ID, i, this._tblStock),
                //  $R_FN.setCleanTextValue($R_FN.setCleanTextValue(row.Alt == true ? $R_FN.showYellowTextImportant($RGT_nubButton_CustomerRequirement(row.ID, row.CustReqNo)) : $RGT_nubButton_CustomerRequirement(row.ID, row.CustReqNo)))
                //, $R_FN.writeDoubleCellValue(row.Quantity)
                //, $R_FN.writeDoubleCellValue($R_FN.writePartNo(row.Alt == true ? $R_FN.showYellowTextImportant(row.Part) : row.Part, row.ROHS), $R_FN.setCleanTextValue(row.CustomerPart))
                //, $R_FN.writeDoubleCellValue($RGT_nubButton_Manufacturer(row.MfrNo, row.Mfr), $R_FN.setCleanTextValue(row.DC))
                //, $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Product), $R_FN.setCleanTextValue(row.Package))
                //, $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Company), $R_FN.setCleanTextValue(row.Date))
                //, $R_FN.writeDoubleCellValue(row.TPriceInBom, row.SalesmanName)
                //, $R_FN.setCleanTextValue(row.Instructions)
                //, $R_FN.writeDoubleCellValue(row.MSL, row.FactorySealed)
                //    //, row.IsPurchaseRequestCreated == true ? "YES" : "-" /*$RGT_nubButton_POQuote(row.PurchaseQuoteId, row.PurchaseQuoteNumber)*/
                //    //, row.IsPurchaseRequestCreated == true ? "YES" : "--" /*$RGT_nubButton_POQuote(row.PurchaseQuoteId, row.PurchaseQuoteNumber)*/
                //    ];
                //}
                aryData = [
                    this.writeCheckbox(row.ID, i, this._tblStock),
                    $R_FN.setCleanTextValue($R_FN.setCleanTextValue(row.Alt == true ? $R_FN.showYellowTextImportant($RGT_nubButton_CustomerRequirement(row.ID, row.CustReqNo)) : $RGT_nubButton_CustomerRequirement(row.ID, row.CustReqNo)))
                    , $R_FN.writeDoubleCellValue(row.Quantity)
                    , $R_FN.writeDoubleCellValue($R_FN.writePartNo(row.Alt == true ? $R_FN.showYellowTextImportant(row.Part) : row.Part, row.ROHS), $R_FN.setCleanTextValue(row.CustomerPart))
                    , $R_FN.writeDoubleCellValue($RGT_nubButton_Manufacturer(row.MfrNo, row.Mfr), $R_FN.setCleanTextValue(row.DC))
                    , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Product), $R_FN.setCleanTextValue(row.Package))
                    , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Company), $R_FN.setCleanTextValue(row.Date))
                    , $R_FN.writeDoubleCellValue(row.TPriceInBom, row.SalesmanName)
                    , $R_FN.writeDoubleCellValue(row.MSL, row.FactorySealed)
                    , (this._blnPOHub == true) ? $R_FN.writeDoubleCellValue(row.IsPurchaseRequestCreated == true ? "YES" : "-", $R_FN.setCleanTextValue(row.Instructions)) : $R_FN.setCleanTextValue(row.Instructions)

                ];
                var objExtraData = {
                    Part: $R_FN.setCleanTextValue(row.PartNo)
                    , Closed: row.Closed
                    , Released: row.Released
                    , CMNo: row.CMNo
                    , HasSourcingResult: row.HasSourcingResult
                    , SourcingResult: row.SourcingResult
                    , IPOClientNo: row.IPOClientNo
                    , CustomerRequirementNumber: row.CustReqNo
                    , IsNoBid: row.IsNoBid
                    , BOMNo: row.BOMNo
                };
                this._tblStock.addRow(aryData, row.ID, (row.ID == this._intSelectedLineNo), objExtraData, strCSS);

                bomStatus = row.BOMStatus;

                this.registerCheckBox(row.ID, i, false, IsCheckBoxEnabled, this._tblStock);
                var chk = this.getCheckBox(i, this._tblStock);
                chk._element.setAttribute("onClick", String.format("$find(\"{0}\").getCheckedCellValue({1},{2});", this._element.id, i, row.ID));
                chk = null;



                row = null; aryData = null; objExtraData = null;
                this._tblStock.resizeColumns();
                this._ctlTabStock.addCountToTitle(i + 1);
            }
        }
    },
    //    addCustReqRows: function(res, i) {
    //        if (i < res.length) {
    //            for (var i = 0; i < res.length; i++) {
    //                var row = res[i];

    //                this._lineLength = res.length;
    //                this._BomCode = row.BOMCode;
    //                this._BomName = row.BOMFullName;
    //                this._BomCompanyName = row.Company;
    //                this._BomCompanyNo = row.CompanyNo;
    //                this._SalesManNo = row.Salesman;
    //                this._SalesManName = row.SalesmanName;

    ////                if (row.SourcingResult == false) {
    ////                    this._blnCanRecal = row.SourcingResult;
    ////                }
    //                var strCSS = "cusReqMainPart";
    //                //if (row.Alt) strCSS = "cusReqAlternatePart";
    //                if (!row.Alt) strCSS = "cusReqMainPart";
    //                if (row.Released) strCSS = "readyToShip";
    //                if (row.Released == false && row.HasSourcingResult == true) strCSS = "allocated";
    //                if (row.Released) strCSS = "readyToShip";
    //                //alert(strCSS)
    //                if (this._allExistInSourcingResult == false) {
    //                    this._allExistInSourcingResult = row.HasSourcingResult;
    //                }
    //                this.disableItemAddButton(!row.IsRequestToPurchaseQuote && !this._inActive);
    //                this._isRequestToPurchaseQuote = row.IsRequestToPurchaseQuote;

    //                if (this._blnPOHub == true) {
    //                    aryData = [
    //                    //$RGT_nubButton_CustomerRequirement(row.ID, row.No)
    //                 // $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Alt == true ? $R_FN.showYellowTextImportant(row.ID, row.CustReqNo) : row.ID, row.CustReqNo))
    //                 $R_FN.setCleanTextValue(row.Alt == true ? $R_FN.showYellowTextImportant(row.CustReqNo) : row.CustReqNo)
    //                , $R_FN.writeDoubleCellValue(row.Quantity)
    //                , $R_FN.writeDoubleCellValue($R_FN.writePartNo(row.Part, row.ROHS), $R_FN.setCleanTextValue(row.CustomerPart))
    //                , $R_FN.writeDoubleCellValue($RGT_nubButton_Manufacturer(row.MfrNo, row.Mfr), $R_FN.setCleanTextValue(row.DC))
    //				, $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Product), $R_FN.setCleanTextValue(row.Package))
    //				, $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Company), $R_FN.setCleanTextValue(row.Date))
    //				, $R_FN.writeDoubleCellValue(row.TPriceInBom, row.SalesmanName)
    //				, $R_FN.writeDoubleCellValue(row.IsPurchaseRequestCreated == true ? "YES" : "-", $R_FN.setCleanTextValue(row.Instructions)) /*$RGT_nubButton_POQuote(row.PurchaseQuoteId, row.PurchaseQuoteNumber)*/
    //				, $R_FN.writeDoubleCellValue(row.MSL, row.FactorySealed)
    //				];
    //                }
    //                else {

    //                    aryData = [
    //                  $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Alt == true ? $R_FN.showYellowTextImportant($RGT_nubButton_CustomerRequirement(row.ID, row.CustReqNo)) : $RGT_nubButton_CustomerRequirement(row.ID, row.CustReqNo)))
    //                , $R_FN.writeDoubleCellValue(row.Quantity)
    //                , $R_FN.writeDoubleCellValue($R_FN.writePartNo(row.Alt == true ? $R_FN.showYellowTextImportant(row.Part) : row.Part, row.ROHS), $R_FN.setCleanTextValue(row.CustomerPart))
    //                , $R_FN.writeDoubleCellValue($RGT_nubButton_Manufacturer(row.MfrNo, row.Mfr), $R_FN.setCleanTextValue(row.DC))
    //				, $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Product), $R_FN.setCleanTextValue(row.Package))
    //				, $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Company), $R_FN.setCleanTextValue(row.Date))
    //				, $R_FN.writeDoubleCellValue(row.TPriceInBom, row.SalesmanName)
    //				, $R_FN.setCleanTextValue(row.Instructions)
    //				, $R_FN.writeDoubleCellValue(row.MSL, row.FactorySealed)
    //                    //, row.IsPurchaseRequestCreated == true ? "YES" : "-" /*$RGT_nubButton_POQuote(row.PurchaseQuoteId, row.PurchaseQuoteNumber)*/
    //                    //, row.IsPurchaseRequestCreated == true ? "YES" : "--" /*$RGT_nubButton_POQuote(row.PurchaseQuoteId, row.PurchaseQuoteNumber)*/
    //				];
    //                }
    //                var objExtraData = {
    //                    Part: $R_FN.setCleanTextValue(row.PartNo)
    //				, Closed: row.Closed
    //				, Released: row.Released
    //				, CMNo: row.CMNo
    //				, HasSourcingResult: row.HasSourcingResult
    //				, SourcingResult:row.SourcingResult
    //				, IPOClientNo: row.IPOClientNo
    //                , CustomerRequirementNumber: row.CustReqNo
    //				, IsNoBid: row.IsNoBid
    //                };
    //                this._tblStock.addRow(aryData, row.ID, (row.ID == this._intSelectedLineNo), objExtraData, strCSS);

    //                this.registerCheckBox(row.ID, i, false, IsCheckBoxEnabled, this._tblStock);
    //                var chk = this.getCheckBox(i, this._tblStock);
    //                chk._element.setAttribute("onClick", String.format("$find(\"{0}\").getCheckedCellValue({1},{2});", this._element.id, i, row.ID));
    //                chk = null;

    //                bomStatus = row.BOMStatus;
    //                row = null; aryData = null; objExtraData = null;
    //                this._tblStock.resizeColumns();
    //                this._ctlTabStock.addCountToTitle(i + 1);
    //            }
    //        }
    //    },

    getDataError: function (args) {
        this.showError(true, args.get_ErrorMessage());
    },

    enableButtons: function (bln) {
        if (bln) {
            //alert(this._isClosed);
            if (this._ibtnRelease) $R_IBTN.enableButton(this._ibtnRelease, this._intCustomerRequirementID > 0 && !this._blnRequirementReleased && this._blnPOHub && this._hasSourcingResult && !this._isClosed && (this._tblStock.getSelectedExtraData().IsAssignedToMe == true));
            if (this._ibtnUnRelease) $R_IBTN.enableButton(this._ibtnUnRelease, this._intCustomerRequirementID > 0 && this._blnRequirementReleased && this._blnPOHub && this._hasSourcingResult && this._blnCanRecal && !this._isClosed && (this._tblStock.getSelectedExtraData().IsAssignedToMe == true));
            if (this._ibtnNoBid) {
                $R_IBTN.enableButton(
                    this._ibtnNoBid,
                    this._intCustomerRequirementID > 0
                    && !this._hasSourcingResult
                    && this._blnPOHub
                    && !this._isClosed
                    && !this._isNoBid
                    && (this._tblStock.getSelectedExtraData().IsAssignedToMe == true));
            }
            if (this._ibtnRecallNoBid) $R_IBTN.enableButton(this._ibtnRecallNoBid, this._intCustomerRequirementID > 0 && !this._hasSourcingResult && this._blnPOHub && !this._isClosed && this._isNoBid && (this._tblStock.getSelectedExtraData().IsAssignedToMe == true));
            // if (this._ibtnImportSrcReslt) $R_IBTN.enableButton(this._ibtnImportSrcReslt,  this._blnPOHub && this._hasSourcingResult && !this._isClosed && !this._blnRelease);
        }
        else {
            if (this._ibtnNoBid) $R_IBTN.enableButton(this._ibtnNoBid, false);
            if (this._ibtnRecallNoBid) $R_IBTN.enableButton(this._ibtnRecallNoBid, false);
            if (this._ibtnRelease) $R_IBTN.enableButton(this._ibtnRelease, false);
            // if (this._ibtnImportSrcReslt) $R_IBTN.enableButton(this._ibtnImportSrcReslt, false);
            if (this._ibtnUnRelease) {
                $R_IBTN.enableButton(this._ibtnUnRelease, false);
            }
            if (this._ibtnNote) $R_IBTN.enableButton(this._ibtnNote, false);

            if (this._ibtnApplyPartwatch) $R_IBTN.enableButton(this._ibtnApplyPartwatch, false);
            if (this._ibtnRemovePartwatch) $R_IBTN.enableButton(this._ibtnRemovePartwatch, false);

        }
        //        if (!this._blnPOHub) {
        //            $R_IBTN.showButton(this._ibtnUnRelease, false);
        //        }

    },

    saveError: function (args) {
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    showExportCSV: function () {
        this.getData_Start();
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/BOMItems");
        obj.set_DataObject("BOMItems");
        obj.set_DataAction("ExportToCSV");
        obj.addParameter("id", this._intBOMID);
        obj.addDataOK(Function.createDelegate(this, this.exportCSV_OK));
        obj.addError(Function.createDelegate(this, this.exportCSV_Error));
        obj.addTimeout(Function.createDelegate(this, this.exportCSV_Error));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
        this.onSave();
    },
    exportCSV_OK: function (args) {
        var res = args._result;
        if (res.Filename) {
            //add the date to the file to force the latest version to be returned (not from cache)
            var dt = new Date();
            location.href = String.format("{0}?t={1}", res.Filename, dt.getTime());
            dt = null;
        }
        this.getDataOK_End();
    },

    exportCSV_Error: function (args) {
        this.showError(true, args.get_ErrorMessage());
    },

    exportToExcel: function () {
        this.getData_Start();
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/BOMItems");
        obj.set_DataObject("BOMItems");
        obj.set_DataAction("ExportToExcel");
        obj._intTimeoutMilliseconds = 300 * 1000;
        obj.addParameter("id", this._intBOMID);

        obj.addDataOK(Function.createDelegate(this, this.exportToExcel_OK));
        obj.addError(Function.createDelegate(this, this.exportToExcel_Error));
        obj.addTimeout(Function.createDelegate(this, this.exportToExcel_Error));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    exportToExcel_OK: function (args) {
        var res = args._result;
        if (res.Filename) {
            //add the date to the file to force the latest version to be returned (not from cache)
            var dt = new Date();
            location.href = String.format("{0}?t={1}", res.Filename, dt.getTime());
            dt = null;
        }
        this.getDataOK_End();
    },

    exportToExcel_Error: function (args) {
        this.showError(true, args.get_ErrorMessage());
    },

    showAddForm: function () {
        this.showForm(this._frmAdd, true);
    },
    hideAddForm: function () {
        this.showForm(this._frmAdd, false);
    },
    saveAddComplete: function () {
        //this._decPercentage = 0;
        this.hideAddForm();
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
        this.getData();
    },
    showReleaseForm: function () {
        this.onCallBeforeRelease();
        if (this._blnCanRelease) {
            this._frmConfirm._intRequirementLineID = this._intCustomerRequirementID;
            this._frmConfirm._intBOMID = this._intBOMID;
            this._frmConfirm._BomCode = this._BomCode;
            this._frmConfirm._BomName = this._BomName;
            this._frmConfirm._BomCompanyName = this._BomCompanyName;
            this._frmConfirm._BomCompanyNo = this._BomCompanyNo;
            this._frmConfirm._SalesManNo = this._RequestToPOHubByNo;
            this._frmConfirm._SalesManName = this._SalesManName;
            this._frmConfirm._CustReqNo = this._CustReqNo;
            this._frmConfirm._ReqSalesman = this._ReqSalesman;
            this._frmConfirm._SupportTeamMemberNo = this._SupportTeamMemberNo;
            this.showForm(this._frmConfirm, true);
        }
    },



    hideConfirmForm: function () {
        this.showForm(this._frmConfirm, false);
    },

    showNoBidConfirmForm: function () {
        //   alert("hi");
        if (this._blnCanNoBid) {
            this._frmNoBidConfirm._intRequirementLineID = this._intCustomerRequirementID;
            this._frmNoBidConfirm._intBOMID = this._intBOMID;
            this._frmNoBidConfirm._BomCode = this._BomCode;
            this._frmNoBidConfirm._BomName = this._BomName;
            this._frmNoBidConfirm._BomCompanyName = this._BomCompanyName;
            this._frmNoBidConfirm._BomCompanyNo = this._BomCompanyNo;
            this._frmNoBidConfirm._SalesManNo = this._SalesManNo;
            //this._frmNoBidConfirm._SalesManName = this._SalesManName;
            //this._frmNoBidConfirm._CustReqNo = this._CustReqNo;
            this._frmNoBidConfirm._RecallNoBid = false;
            this._frmNoBidConfirm.setFieldValue("ctlNotes", "");
            this._frmNoBidConfirm.changeMode("NoBid");
            this.showForm(this._frmNoBidConfirm, true);
        }
    },



    hideNoBidConfirmForm: function () {
        this.showForm(this._frmNoBidConfirm, false);
    },

    saveNoBidConfirmComplete: function () {

        this.hideNoBidConfirmForm();
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);

        this.onRefereshAfterRelease();
    },

    showRecallNoBidConfirmForm: function () {

        if (this._blnCanNoBid) {

            this._frmRecallNoBidConfirm._intRequirementLineID = this._intCustomerRequirementID;
            this._frmRecallNoBidConfirm._RecallNoBid = true;
            this._frmRecallNoBidConfirm._CustReqNo = this._CustReqNo;
            this._frmRecallNoBidConfirm.setFieldValue("ctlNotes", "");
            this._frmRecallNoBidConfirm.changeMode("RecallNoBid");
            this.showForm(this._frmRecallNoBidConfirm, true);
        }
    },

    hideRecallNoBidConfirmForm: function () {
        this.showForm(this._frmRecallNoBidConfirm, false);
    },

    saveRecallNoBidConfirmComplete: function () {

        this.hideRecallNoBidConfirmForm();
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);

        this.onRefereshAfterRelease();
    },

    showDeleteForm: function () {
        this._frmDelete._intCustomerRequirementID = this._intCustomerRequirementID;
        this._frmDelete._intBOMID = this._intBOMID;
        this.showForm(this._frmDelete, true);
    },
    hideDeleteForm: function () {
        this.showForm(this._frmDelete, false);
    }
    ,
    showApplyPartwatchForm: function () {


        this._frmApplyPartwatch._intCustomerRequirementID = this._intCustomerRequirementID;
        this._frmApplyPartwatch._ReqIds = this._ReqIds;
        this._frmApplyPartwatch._intBOMID = this._intBOMID;
        this._frmApplyPartwatch._intBOMNo = this._BOMNo;
        this._frmApplyPartwatch._RequestToPOHubBy = this._RequestToPOHubBy;
        this._frmApplyPartwatch._UpdateByPH = this._UpdateByPH;
        this._frmApplyPartwatch._BomName = this._BomName;
        this._frmApplyPartwatch._BomCompanyNo = this._BomCompanyNo;
        this._frmApplyPartwatch._intContact2No = this._intContact2No;
        this.showForm(this._frmApplyPartwatch, true);



    },

    showRemovePartwatchForm: function () {
        this._frmRemovePartwatch._intCustomerRequirementID = this._intCustomerRequirementID;
        this._frmRemovePartwatch._ReqIds = this._ReqIds;
        this._frmRemovePartwatch._intBOMID = this._intBOMID;
        this._frmRemovePartwatch._intBOMNo = this._BOMNo;
        this._frmRemovePartwatch._RequestToPOHubBy = this._RequestToPOHubBy;
        this._frmRemovePartwatch._UpdateByPH = this._UpdateByPH;
        this._frmRemovePartwatch._BomName = this._BomName;
        this._frmRemovePartwatch._BomCompanyNo = this._BomCompanyNo;
        this._frmRemovePartwatch._intContact2No = this._intContact2No;
        this.showForm(this._frmRemovePartwatch, true);
    },

    hideRemovePartwatchForm: function () {
        this.showForm(this._frmRemovePartwatch, false);
    },
    saveRemovePartwatchConfirmComplete: function () {

        this.hideRemovePartwatchForm();
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
        this.onRefereshAfterRelease();
    },


    hideApplyPartwatchForm: function () {
        this.showForm(this._frmApplyPartwatch, false);
    },
    saveApplyPartwatchConfirmComplete: function () {

        this.hideApplyPartwatchForm();
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);

        this.onRefereshAfterRelease();
    },

    showUnReleaseForm: function () {
        this._frmUnRelease._intCustomerRequirementID = this._intCustomerRequirementID;
        this._frmUnRelease._intBOMID = this._intBOMID;
        this.showForm(this._frmUnRelease, true);
    },
    hideUnReleaseForm: function () {
        this.showForm(this._frmUnRelease, false);
    },
    UnReleaseComplete: function () {
        this.hideDeleteForm();
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
        // if (this._ibtnUnRelease) $R_IBTN.enableButton(this._ibtnUnRelease, false)
        // this.getData();
        this.onRefereshAfterRelease();
    },
    saveCeaseComplete: function () {
        //this._decPercentage = 0;
        this.hideConfirmForm();
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
        //this.getData();
        this.onRefereshAfterRelease();
    },
    DeleteComplete: function () {
        this.hideDeleteForm();
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
        if (this._ibtnDelete) $R_IBTN.enableButton(this._ibtnDelete, false)
        this.getData();
    },
    enableBOMProvision: function (bln) {
        if (this._ibtnStockProvision) $R_IBTN.enableButton(this._ibtnStockProvision, bln && this._blnProvisionLoaded);
    },
    tbl_SelectedIndexChanged: function () {
        //alert(bomStatus);

        SetAssignToMe(this._tblStock.getSelectedExtraData().IsAssignedToMe);
        if (this._tblStock.getSelectedExtraData().IsAs6081Required == "Yes") {
            SetAS6081Required(true);
        } else {
            SetAS6081Required(false);
        }

        var mfrNo = this._tblStock.getSelectedExtraData().MfrNo;
        var mfrCode = this._tblStock.getSelectedExtraData().MfrCode;
        if (mfrNo) {
            this.StartRefreshLylicaAPIData(this._tblStock.getSelectedExtraData().Part, mfrCode, mfrNo);
        }

        this.enableDisableItemDeleteButton(this._isRequestToPurchaseQuote);
        this._intCustomerRequirementID = this._tblStock._varSelectedValue;
        this._intSelectedLineNo = this._tblStock._varSelectedValue;
        this._blnRequirementClosed = this._tblStock.getSelectedExtraData().Closed;
        this._blnRequirementReleased = this._tblStock.getSelectedExtraData().Released;
        this._intCompanyID = this._tblStock.getSelectedExtraData().CMNo;
        this._hasSourcingResult = this._tblStock.getSelectedExtraData().HasSourcingResult;
        this._CustReqNo = this._tblStock.getSelectedExtraData().CustomerRequirementNumber;
        //this._blnCanRecal = this._tblStock.getSelectedExtraData().SourcingResult;
        // alert(this._CustReqNo);
        this._isNoBid = this._tblStock.getSelectedExtraData().IsNoBid;
        this._blnCanApplyPartWatch = this._tblStock.getSelectedExtraData().isPartWatchHUBIPO;
        this.enableButtons(true);
        this.onPartSelected();
        this.onGotDataOK();
        this.getLineData();
    },
    getSelectedPartNo: function () {
        return this._tblStock.getSelectedExtraData().Part;
    },
    getIPOClientNo: function () {
        return this._tblStock.getSelectedExtraData().IPOClientNo;
    },
    enableItemReleaseButton: function (hasSourcingResult) {
        if (this._ibtnRelease) $R_IBTN.enableButton(this._ibtnRelease, this._intCustomerRequirementID > 0 && !this._blnRequirementReleased && this._blnPOHub && hasSourcingResult && !this._isClosed && (this._tblStock.getSelectedExtraData().IsAssignedToMe == true));
    },
    disableItemAddButton: function (isRequestedToPQ) {
        if (this._ibtnAdd) $R_IBTN.enableButton(this._ibtnAdd, isRequestedToPQ);
    }
    ,
    enableDisableItemDeleteButton: function (sendToPurchaseRequest) {
        if (this._ibtnDelete) $R_IBTN.enableButton(this._ibtnDelete, this._lineLength > 0 && !sendToPurchaseRequest && !this._isClosed);

    },

    StartRefreshLylicaAPIData: function (partNumber, mfrCode, mfrNo) {
        partNumber = this.beautifyPartNumber(partNumber);
        $.ajax({
            type: 'POST',
            contentType: 'application/json',
            url: window.location.origin + "" + "/controls/Nuggets/CusReqAdd/CusReqAdd.ashx" + '?action=RefreshLyticaAPIAfter3Days&PartNumber=' + partNumber + '&mfr=' + mfrCode + '&mfrNo=' + mfrNo,
            async: true,
            error: function () { /*alert("something went wrong");*/ }
        });
    },
    beautifyPartNumber: function (partNumber) {
        partNumber = partNumber.replace(" (Alternate)", "");
        partNumber = partNumber.replace("&", "_AMPERSAND_");
        partNumber = partNumber.replace("#", "_HASH_");
        partNumber = partNumber.replace("=", "_EQUALS_");

        return partNumber;
    },

    getLineData: function () {
        $("#As6081WarningMsg").hide();
        $("#dvtxt").html("");
        $('#divBlockBox').hide();
        CloseKubPopup();
        ShowKubIcon(false);
        this._blnLineLoaded = false;
        var obj = new Rebound.GlobalTrader.Site.Data();
        this.showLoading(true);
        $R_FN.showElement(this._pnlLineDetail, false);
        $R_FN.showElement(this._pnlLoadingLineDetail, true);
        $R_FN.showElement(this._pnlLineDetailError, false);
        obj.set_PathToData("controls/Nuggets/BOMItems");
        obj.set_DataObject("BOMItems");
        // obj.set_PathToData("controls/Nuggets/CusReqMainInfo");
        // obj.set_DataObject("CusReqMainInfo");
        obj.set_DataAction("GetItem");
        obj.addParameter("id", this._intCustomerRequirementID);
        obj.addDataOK(Function.createDelegate(this, this.getLineDataOK));
        obj.addError(Function.createDelegate(this, this.getLineDataError));
        obj.addTimeout(Function.createDelegate(this, this.getLineDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getLineDataOK: function (args) {
        var res = args._result;

        //Load KUB Assistant
        if (res.IsAllowedEnableKub) {
            LoadParNumberDetailsForKub(res);
        }
        //} else {
        //    HideKubIcon();
        //}

        if (res.IsAs6081Required == "Yes") {
            $("#As6081WarningMsg").show();
            $("#dvtxt").html("This part requires AS6081 compliance, please ensure the appropriate supplier is chosen to fulfil this requirement.");
        }
        else {
            $("#As6081WarningMsg").hide();
            $("#dvtxt").html("");
        }
        var imagepath = '';
        // this.setFieldValue("ctlCompany", $RGT_nubButton_Company(res.CustomerNo, res.CustomerName, null, null, res.CustomerOnStop));
        //  this.setFieldValue("ctlContact", $RGT_nubButton_Contact(res.ContactNo, res.Contact));
        this.setFieldValue("hidCompanyID", res.CustomerNo);
        this.setFieldValue("hidCompanyName", $R_FN.setCleanTextValue(res.CustomerName));
        this.setFieldValue("hidContactID", res.ContactNo);
        this.setFieldValue("hidContactName", $R_FN.setCleanTextValue(res.Contact));
        this.setFieldValue("ctlQuantity", res.Quantity);
        this.setFieldValue("ctlPartNo", res.Part);

        this.setFieldValue("ctlAs6081Required", res.IsAs6081Required);
        if (res.IsAs6081Required == "Yes") {
            $("#ctl00_cphMain_ctlBOMItems_ctlDB_ctl13_ctlAs6081Required_lbl").css("background-color", "Yellow");
        }
        else {
            $("#ctl00_cphMain_ctlBOMItems_ctlDB_ctl13_ctlAs6081Required_lbl").css("background-color", "white");
        }
        this.setFieldValue("ctlQuantity", res.Quantity);
        if (res.IsPDFAvailable) {
            imagepath = "app_themes/original/images/IconButton/pdficon-clear.png";
            //adding image for pdf
            //this.setFieldValue("ctlPartNo", res.Part + '  ' + String.format("&nbsp;&nbsp;<a style='float:right' href=\"javascript:void(0);\" onclick=\"$RGT_openIHSDoc(" + res.IHSPartsId + ",2" + ")\" title=\"Click to View docs\"><img border='0'  src=" + imagepath + " width='20' height='18'></a>", res.IHSPartsId));
            //this.setFieldValue("ctlPartNo", $R_FN.setCleanTextValue(res.Part + String.format("&nbsp;&nbsp;<a style='float:right' href=\"javascript:void(0);\" onclick=\"$RGT_openBomItemIHS({0},{1})\" title=\"Click to View docs\"><img border='0'  src=" + imagepath + " width='20' height='18'></a>", res.IHSPartsId, 1)));

            if (res.StockAvailableDetail != null && res.StockAvailableDetail != '') {
                var stkDetailArr = res.StockAvailableDetail.split('-');
                var QuantityInStock = stkDetailArr[0];
                var QuantityOnOrder = stkDetailArr[1];
                var QuantityAllocated = stkDetailArr[2];
                var QuantityAvailable = stkDetailArr[3];
                var StockNo = stkDetailArr[4];
                var strStockAvailableDetail = "";//"Stock Available Detail";//res.StockAvailableDetail;
                var stockURL = "Whs_StockDetail.aspx?stk=" + StockNo + ""
                // Hidden rule
                var cleanText = $R_FN.setCleanTextValue(String.format("&nbsp;&nbsp;<a style='padding-left:15px' href=\"javascript:void(0);\" onclick=\"$RGT_openBomItemIHS({0},{1})\" title=\"Click to View docs\"><img style='margin-bottom:-5px' border='0'  src=" + imagepath + " width='20' height='18'></a>", res.IHSPartsId, 1));

                this.setFieldValue("ctlPartNo", $R_FN.showStockAvailableHUBRFQ(res.Part, QuantityInStock, QuantityOnOrder, QuantityAllocated, QuantityAvailable, stockURL, strStockAvailableDetail, cleanText));
            }
            else {
                this.setFieldValue("ctlPartNo", $R_FN.setCleanTextValue(res.Part + String.format("&nbsp;&nbsp;<a style='float:right' href=\"javascript:void(0);\" onclick=\"$RGT_openBomItemIHS({0},{1})\" title=\"Click to View docs\"><img border='0'  src=" + imagepath + " width='20' height='18'></a>", res.IHSPartsId, 1)));
                //this.setFieldValue("ctlPartNo", res.Part);
                //this.setFieldValue("ctlPartNo", $R_FN.showStockAvailableNA(res.Part,"N/A"));
            }
        }
        else {
            if (res.StockAvailableDetail != null && res.StockAvailableDetail != '') {
                var stkDetailArr = res.StockAvailableDetail.split('-');
                var QuantityInStock = stkDetailArr[0];
                var QuantityOnOrder = stkDetailArr[1];
                var QuantityAllocated = stkDetailArr[2];
                var QuantityAvailable = stkDetailArr[3];
                var StockNo = stkDetailArr[4];
                var strStockAvailableDetail = "";//"Stock Available Detail";//res.StockAvailableDetail;
                var stockURL = stockURL = 'Whs_StockBrowse.aspx';;
                if (parseInt(StockNo) > 0) {
                    stockURL = "Whs_StockDetail.aspx?stk=" + StockNo + "";
                }
                this.setFieldValue("ctlPartNo", $R_FN.showStockAvailableNew(res.Part, QuantityInStock, QuantityOnOrder, QuantityAllocated, QuantityAvailable, stockURL, strStockAvailableDetail));
            }
            else {
                this.setFieldValue("ctlPartNo", res.Part);
                //this.setFieldValue("ctlPartNo", $R_FN.showStockAvailableNA(res.Part,"N/A"));
            }
        }
        this.setFieldValue("ctlCustomerPart", $R_FN.setCleanTextValue(res.CustomerPart));
        this.setFieldValue("ctlManufacturer", $RGT_nubButton_Manufacturer(res.ManufacturerNo, res.Manufacturer, res.MfrAdvisoryNotes));
        this.setFieldValue("hidManufacturer", $R_FN.setCleanTextValue(res.Manufacturer));
        this.setFieldValue("hidManufacturerNo", res.ManufacturerNo);
        this.setFieldValue("hidMfrAdvisoryNotes", res.MfrAdvisoryNotes);
        this.setFieldValue("ctlDateCode", res.DateCode);
        this.setFieldValue("ctlProduct", $R_FN.setCleanTextValue(res.Product));
        this.setFieldValue("hidProductID", res.ProductNo);
        this.setFieldValue("ctlPackage", $R_FN.setCleanTextValue(res.Package));
        this.setFieldValue("hidPackageID", res.PackageNo);
        this.setFieldValue("ctlTargetPrice", res.Price);
        this.setFieldValue("hidPrice", res.PriceRaw);
        this.setFieldValue("ctlCurrency", $R_FN.setCleanTextValue(res.Currency));
        this.setFieldValue("hidCurrencyID", res.CurrencyNo);

        this.setFieldValue("ctlDateRequired", res.DatePromised);
        this.setFieldValue("ctlUsage", $R_FN.setCleanTextValue(res.Usage));
        this.setFieldValue("hidUsageID", $R_FN.setCleanTextValue(res.UsageNo));
        this.setFieldValue("ctlNotes", $R_FN.setCleanTextValue(res.Notes));
        this.setFieldValue("ctlInstructions", $R_FN.setCleanTextValue(res.Instructions));
        this.setFieldValue("ctlROHS", $R_FN.writeROHS(res.ROHS));
        this.setFieldValue("hidROHS", res.ROHS);
        //  this.setFieldValue("ctlClosed", res.Closed);
        this.setFieldValue("ctlClosedReason", res.ClosedReason);
        this.setFieldValue("hidDisplayStatus", $R_FN.setCleanTextValue(res.DisplayStatus));
        this.setFieldValue("ctlPartWatch", res.PartWatch);
        this.setFieldValue("ctlBOM", res.BOM);
        this.setFieldValue("ctlBOMName", res.BOMName);
        //this.setFieldValue("ctlBOMHeader", res.BOMHeader);
        //  this.setFieldValue("ctlBOMHeader", $RGT_nubButton_BOM(res.BOMId, $R_FN.setCleanTextValue(res.BOMHeader)));
        this.setFieldValue("hidBOMID", res.BOMId);
        this.setFieldValue("hidBOMHeaderDisplayStatus", res.RequestToPOHubBy == null ? true : false);
        this.setFieldValue("ctlMSL", res.MSL);
        this.setFieldValue("ctlFactorySealed", res.FactorySealed);
        this.setFieldValue("ctlPQA", res.PQA);
        this.setFieldValue("ctlObsolete", res.Obsolete);
        this.setFieldValue("ctlLastTimeBuy", res.LastTimeBuy);
        this.setFieldValue("ctlRefirbsAcceptable", res.RefirbsAcceptable);
        this.setFieldValue("ctlTestingRequired", res.TestingRequired);
        this.setFieldValue("ctlTargetSellPrice", res.TargetSellPrice);
        this.setFieldValue("ctlCompetitorBestoffer", res.CompetitorBestOffer);
        this.setFieldValue("ctlCustomerDecisionDate", res.CustomerDecisionDate);
        this.setFieldValue("ctlRFQClosingDate", res.RFQClosingDate);
        this.setFieldValue("ctlQuoteValidityRequiredHid", res.QuoteValidityRequired);
        this.setFieldValue("ctlQuoteValidityRequired", res.QuoteValidityText);
        this.setFieldValue("ctlTypeHid", res.Type);
        this.setFieldValue("ctlType", res.ReqTypeText);
        this.setFieldValue("ctlOrderToPlace", res.OrderToPlace);
        this.setFieldValue("ctlRequirementforTraceability", res.ReqForTraceabilityText);
        this.setFieldValue("ctlRequirementforTraceabilityHid", res.RequirementforTraceability);
        this.setFieldValue("ctlTargetSellPriceHidden", res.hidTargetSellPrice);
        this.setFieldValue("ctlCompetitorBestofferHidden", res.hidCompetitorBestOffer);
        this.setFieldValue("ctlEAU", res.EAU);
        this.setFieldValue("ctlCustomerRefNo", res.CustomerRefNo);
        //   alert(res.Closed);
        this.setFieldValue("ctlIsNoBid", $R_FN.showLargeFonts(res.IsNoBidStatus));
        this.showField("ctlIsNoBid", res.IsNoBid);
        this.setFieldValue("ctlIsNoBidNotes", res.NoBidNotes);
        this.showField("ctlIsNoBidNotes", res.IsNoBid);
        ////[001] Start Here
        this.setFieldValue("ctlClosed", res.Closed == false ? $R_FN.showLargeFontsWithColor('No') : $R_FN.showLargeFonts('Yes'));
        //[001] End Here
        this.setFieldValue("ctlAlternativesAccepted", res.AlternativesAccepted);
        this.setFieldValue("ctlRepeatBusiness", res.RepeatBusiness);
        this.setFieldValue("ctlPrdDutyCodeRate", res.DutyCodeAndRate);
        this._isNoBid = res.IsNoBid;
        this._blnCanRecal = res.SourcingResult;
        this.setFieldValue("hidMSL", res.MSLLevelNo);

        //ihs
        this.setFieldValue("ctlCountryOfOrigin", res.CountryOfOrigin);
        //this.setFieldValue("ctlLifeCycleStage", res.LifeCycleStage);
        this.setFieldValue("ctlLifeCycleStage", $R_FN.showIHSstatusDefi(res.LifeCycleStage, res.IHSStatusDefination));
        // this.setFieldValue("ctlHTSCode", res.HTSCode);
        //this.setFieldValue("ctlPacking", res.Packaging);
        this.setFieldValue("ctlPackagingSize", res.PackagingSize);
        this.setFieldValue("ctlDescriptions", res.Descriptions);
        // this.setFieldValue("ctlIHSProduct", res.IHSProduct);
        if (res.IHSProductNo != 0 && res.ProductNo != 0) {
            this.setFieldValue("ctlIHSProduct", res.IHSProduct);
            $("#ctl00_cphMain_ctlBOMItems_ctlDB_ctl13_ctlIHSProduct_lbl").css("color", "");
        }
        else {
            this.setFieldValue("ctlIHSProduct", res.IHSProduct);


            //$("#ctl00_cphMain_ctlBOMItems_ctlDB_ctl13_ctlIHSProduct_lbl").css("color", "red");
        }
        if (res.IHSHTSCode != "") {
            this.setFieldValue("ctlHTSCode", res.HTSCode);

            $("#ctl00_cphMain_ctlBOMItems_ctlDB_ctl13_ctlHTSCode_lbl").css("color", "");
            //
        }
        else {
            this.setFieldValue("ctlHTSCode", res.HTSCode);

            // $("#ctl00_cphMain_ctlBOMItems_ctlDB_ctl13_ctlHTSCode_lbl").css("color", "red");
            //
        }
        if (res.IHSDutyCode != null) {

        }
        this._IsPOHub = res.IsPOHub;
        if (this._IsPOHub == true) {
            //$("#ctl00_cphMain_ctlBOMItems_ctlDB_ctl13_ctlAveragePrice").show();
            this.showField("ctlAveragePrice", true);
            this.setFieldValue("ctlAveragePrice", res.AveragePrice);

            this.showField("ctlMarketLeading", true);
            this.setFieldValue("ctlMarketLeading", res.MarketLeading);

            this.showField("ctlTargetPriceAPI", true);
            this.setFieldValue("ctlTargetPriceAPI", res.TargetPrice);
            $('#CmbLyticaManufacture').show();
            this.showField("ctlCustomerRefNo", true);
        }
        else {
            this.showField("ctlAveragePrice", false);
            this.showField("ctlMarketLeading", false);
            this.showField("ctlTargetPriceAPI", false);
            $('#CmbLyticaManufacture').hide();
            this.showField("ctlCustomerRefNo", false);
        }
        //ihs
        //[010] code start
        //ECCN Code Defination
        if (res.IHSECCNSCodeDefination != null && res.IHSECCNSCodeDefination != '') {
            this.setFieldValue("ctlECCNCode", $R_FN.showIHSECCNCodeDefi(res.ECCNCode, res.IHSECCNSCodeDefination));
        }
        else {
            this.setFieldValue("ctlECCNCode", res.ECCNCode);
        }
        //[010] code end
        var PurchaseRequestId = "";


        if ((res.PurchaseRequestId) && (res.PurchaseRequestNumber)) {
            for (var i = 0; i < res.PurchaseRequestNumber.length; i++) {
                var row = res.PurchaseRequestId[i];
                var row1 = res.PurchaseRequestNumber[i];
                PurchaseRequestId += $RGT_nubButton_POQuote(row.PurchaseRequestId, row1.PurchaseRequestNumber);
                // PurchaseRequestId += $RGT_nubButton_POQuote(row.PurchaseRequestId, row.PurchaseRequestId);
                //[004] code start(Break line after 5 pricerequestId)
                if (i % 5 == 0 && i > 4)
                    PurchaseRequestId += "<br/>";
                //[004] code end
            }
        }

        this._RequestToPOHubByNo = res.RequestToPOHubBy;
        this._ReqSalesman = res.SalesmanNo;
        this._SupportTeamMemberNo = res.SupportTeamMemberNo;
        this.enableButtons(true);
        $R_FN.showElement(this._pnlLineDetail, true);
        $R_FN.showElement(this._pnlLoadingLineDetail, false);
        this.showLoading(false);
        //added on 17 May 2018
        this.onGotDataOK();

        $("#lblRsMFR").hide();
        $("#spanmfr").text("");
        if (res.LyticaManufacturerRef) {
            $("#ctl00_cphMain_ctlBOMItems_ctlDB_ctl13_cmbManufacturertxt").addClass("invisible"); //textbox
            $("#ctl00_cphMain_ctlBOMItems_ctlDB_ctl13_cmbManufacturertxt").val(res.LyticaManufacturerRef); //textbox
            $("#ctl00_cphMain_ctlBOMItems_ctlDB_ctl13_cmbManufactureraut_ctl04").removeClass("invisible"); //reselect
            $("#ctl00_cphMain_ctlBOMItems_ctlDB_ctl13_cmbManufactureraut_ctl03").removeClass("invisible"); //span
            $("#ctl00_cphMain_ctlBOMItems_ctlDB_ctl13_cmbManufactureraut_ctl03").html(res.LyticaManufacturerRef); //span
            $('#ctl00_cphMain_ctlBOMItems_ctlDB_ctl13_cmbManufactureraut_ctl05').addClass("invisible");//search result
        } else {
            $("#ctl00_cphMain_ctlBOMItems_ctlDB_ctl13_cmbManufacturertxt").removeClass("invisible"); //textbox
            $("#ctl00_cphMain_ctlBOMItems_ctlDB_ctl13_cmbManufacturertxt").val(""); //textbox
            $("#ctl00_cphMain_ctlBOMItems_ctlDB_ctl13_cmbManufactureraut_ctl04").addClass("invisible"); //reselect
            $("#ctl00_cphMain_ctlBOMItems_ctlDB_ctl13_cmbManufactureraut_ctl03").addClass("invisible"); //span
            $('#ctl00_cphMain_ctlBOMItems_ctlDB_ctl13_cmbManufactureraut_ctl05').addClass("invisible");//search result 
        }

        if ($find(this._element.id + '_ctl13_cmbManufacturer')) {
            $find(this._element.id + '_ctl13_cmbManufacturer')._aut.addSelectionMadeEvent(Function.createDelegate(this, this.RsMfrChanged));
        }
    },

    getLineDataError: function (args) {
        this.showLoading(false);
        $R_FN.showElement(this._pnlLoadingLineDetail, false);
        $R_FN.showElement(this._pnlLineDetailError, true);
        $R_FN.setInnerHTML(this._pnlLineDetailError, args.get_ErrorMessage());
        Array.clear(this._ReqIds);
    },
    ResetMFR: function () {
        $("#spanmfr").text("");
    },
    RsMfrChanged: function () {
        var mfrName = $("#ctl00_cphMain_ctlBOMItems_ctlDB_ctl13_cmbManufacturertxt").val();
        this.getRsMfr(mfrName);
    },
    getRsMfr: function (mfrName) {
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/BOMItems");
        obj.set_DataObject("BOMItems");
        obj.set_DataAction("GetLyticaManufacturer");
        obj.addParameter("RsManufacturerName", mfrName);
        obj.addParameter("CustomerRequirementID", this._intCustomerRequirementID);
        obj.addDataOK(Function.createDelegate(this, this.getRsMfrOK));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },
    getRsMfrOK: function (args) {
        var res = args._result;
        if (res != null) {
            $("#lblRsMFR").show();
            $("#ctl00_cphMain_ctlBOMItems_ctlDB_ctl13_cmbManufacturertxt").addClass("invisible");
            $("#ctl00_cphMain_ctlBOMItems_ctlDB_ctl13_cmbManufactureraut_ctl04").removeClass("invisible");
            $("#ctl00_cphMain_ctlBOMItems_ctlDB_ctl13_cmbManufactureraut_ctl03").removeClass("invisible");
            this.setFieldValue("ctlAveragePrice", res.AveragePrice);
            this.setFieldValue("ctlMarketLeading", res.MarketLeading);
            this.setFieldValue("ctlTargetPriceAPI", res.TargetPrice);
            $('#lytica-data').html(`AV Price: ${res.AveragePrice}   M/Leading: ${res.MarketLeading}   Target: ${res.TargetPrice}   P/Status: ${res.LifeCycleStatus}`)
        } else {
            $("#lblRsMFR").hide();
        }
    },
    showExpediteNoteForm: function () {
        this._frmAddExpediteNote._ReqIds = this._ReqIds;
        this._frmAddExpediteNote._intBOMID = this._intBOMID;
        this._frmAddExpediteNote._intBOMNo = this._BOMNo;
        this._frmAddExpediteNote._RequestToPOHubBy = this._RequestToPOHubBy;
        this._frmAddExpediteNote._UpdateByPH = this._UpdateByPH;
        this._frmAddExpediteNote._BomName = this._BomName;
        this._frmAddExpediteNote._BomCompanyNo = this._BomCompanyNo;
        this._frmAddExpediteNote._intContact2No = this._intContact2No;
        this._frmAddExpediteNote.setFieldValue("ctlExpediteNotes", "");
        this.showForm(this._frmAddExpediteNote, true);
    },
    saveAddExpediteNoteComplete: function () {
        if (this._ibtnNote) $R_IBTN.enableButton(this._ibtnNote, false);
        this._ReqIds = [];
        this.showForm(this._frmAddExpediteNote, false);
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
        this.getData();

    },
    cancelAddExpediteNoteForm: function () {
        this.showForm(this._frmAddExpediteNote, false);
        this.showContent(true);
        Array.clear(this._ReqIds);
    },
    cancelImportSourcingForm: function () {
        this.showForm(this._frmImportSourcing, false);
        this.getData();
    },
    writeCheckbox: function (varID, intIndex, tbl) {
        var strChkID = this.getControlID("chk", intIndex, tbl);
        var strChkImageID = this.getControlID("chkImg", intIndex, tbl);
        var str = String.format("<div class=\"imageCheckBoxDisabled\" id=\"{0}\" ><img id=\"{1}\" class=\"{2}\" src=\"images/x.gif\" style=\"border-width: 0px;\" /> </div>", strChkID, strChkImageID, "off");
        return str;
    },
    getControlID: function (str, i, tbl) {
        return String.format("{0}_{1}{2}", tbl._element.id, str, i);
    },
    getCheckBox: function (intCheckBox, tbl) {
        return $find(this.getControlID("chk", intCheckBox, tbl));
    },
    registerCheckBox: function (varID, intIndex, blnChecked, blnEnabled, tbl) {

        var strChkID = this.getControlID("chk", intIndex, tbl);

        var strChkImageID = this.getControlID("chkImg", intIndex, tbl);

        var chk = this.getCheckBox(intIndex, tbl);

        if (chk) {
            chk.dispose();
            chk = null;
        }

        eval($R_FN.getCreateStatement("Rebound.GlobalTrader.Site.Controls.ImageCheckBox", [["blnChecked", blnChecked], ["blnEnabled", blnEnabled], ["img", String.format("$get(\"{0}\")", strChkImageID)]], strChkID));

    },

    getCheckedCellValue: function (intIndex, ID) {
        //alert(ID);
        //
        var tbl = this._tblStock;
        var chk = this.getCheckBox(intIndex, tbl);
        var IsChecked = chk._blnChecked;
        var tr = tbl._tbl.rows[intIndex];
        if (!tr) return;
        if (IsChecked) {
            $R_IBTN.enableButton(this._ibtnNote, true);
            if (!this._isClosed) {
                if (this._blnPOHub == true) {
                    if (this._tblStock.getSelectedExtraData().isPartWatchHUBIPO == true) {
                        $R_IBTN.enableButton(this._ibtnApplyPartwatch, false);
                        $R_IBTN.enableButton(this._ibtnRemovePartwatch, true);
                    }
                    else {
                        $R_IBTN.enableButton(this._ibtnApplyPartwatch, true);
                        $R_IBTN.enableButton(this._ibtnRemovePartwatch, false);
                    }
                }

            }
            else {
                if (this._blnPOHub == true) {
                    $R_IBTN.enableButton(this._ibtnApplyPartwatch, true);
                    $R_IBTN.enableButton(this._ibtnRemovePartwatch, true);
                }
            }
            // this._ReqIds.push(ID);
            Array.add(this._ReqIds, ID);
        }
        else {
            $R_IBTN.enableButton(this._ibtnNote, false);
            if (this._blnPOHub == true) {
                $R_IBTN.enableButton(this._ibtnApplyPartwatch, false);
                $R_IBTN.enableButton(this._ibtnRemovePartwatch, false);
            }
            // this._ReqIds.remove(ID);
            Array.remove(this._ReqIds, ID)
        }
        if (this._ReqIds.length == 0) {
            $R_IBTN.enableButton(this._ibtnNote, false);
            if (this._blnPOHub == true) {
                $R_IBTN.enableButton(this._ibtnApplyPartwatch, false);
                $R_IBTN.enableButton(this._ibtnRemovePartwatch, false);
            }
        }
        else {
            $R_IBTN.enableButton(this._ibtnNote, true);

        }
    },
    enableItemUnReleaseButton: function (hasSourcingResult) {
        // if (this._ibtnUnRelease) $R_IBTN.enableButton(this._ibtnUnRelease, this._intCustomerRequirementID > 0 && this._blnRequirementReleased && this._blnPOHub && hasSourcingResult);
    },
    getPartDetaildata: function () {
        var strPart = this._tblStock.getSelectedExtraData().Part;
        if (strPart.length > 0) {
            this.getPartDetail(strPart);
            this.showDetailDiv(true);
        }
        else {
            this.showDetailDiv(false);
        }
    },
    showDetailDiv: function (visible) {
        if (visible) {
            $("#mydiv").show();
            $("#tbpartdet").show();
        }
        else {
            $("#mydiv").hide();
            $("#tbpartdet").hide();
        }
    },
    getPartDetail: function (partNo) {
        $('#divBlockBox').hide();
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/CusReqAdd");
        obj.set_DataObject("CusReqAdd");
        obj.set_DataAction("GetPartDetail");
        obj.addParameter("partNo", partNo);
        obj.addParameter("CompanyNo", this._tblStock.getSelectedExtraData().CMPNo);
        obj.addDataOK(Function.createDelegate(this, this.setPartDetail));
        obj.addError(Function.createDelegate(this, this.getPartDetailError));
        obj.addTimeout(Function.createDelegate(this, this.getPartDetailError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;


    },
    setPartDetail: function (args) {
        res = args._result;

        for (var i = 0; i < res.LastPriceCustDetails.length; i++) {
            var row = res.LastPriceCustDetails[i];

            $("#spnpartname").text(this._tblStock.getSelectedExtraData().Part);
            $("#spnLastSoldPrice").text(row.LastPricePaidByCust);
            $("#spnsoldtocuston").text($R_FN.setCleanTextValue(row.LastSoldtoCustomer));
            $("#spnAvgPrice").text(row.LastAverageReboundPriceSold);
            $("#spnlastsoldon").text(row.LastSoldOn);


            $("#spnLastQuantity").text(row.LastQuantity);
            $("#spnLastSupplierType").text($R_FN.setCleanTextValue(row.LastSupplierType));
            $("#spnLastDatecode").text($R_FN.setCleanTextValue(row.LastDatecode));
            $("#spnLastDatePurchased").text(row.LastDatePurchased);
            $("#spnLastCustomerRegion").text($R_FN.setCleanTextValue(row.LastCustomerRegion));

            $("#spnCustLastSoldPrice").text(row.CustLastPricePaidByCust);
            $("#spnCurrentCust").text(this._tblStock.getSelectedExtraData().CMP);
            // $("#spnCustAvgPrice").text(row.CustLastAvgPriceSold);
            $("#spnCustlastsoldon").text(row.CustLastSoldOn);

            $("#spnCustQuantity").text(row.CustQuantity);
            $("#spnCustSupplierType").text($R_FN.setCleanTextValue(row.CustSupplierType));
            $("#spnCustDatecode").text($R_FN.setCleanTextValue(row.CustDatecode));
            $("#spnCustDatePurchased").text(row.CustDatePurchased);
            $("#spnCustomerRegion").text($R_FN.setCleanTextValue(row.CustomerRegion));
            $("#spnLastPricePaid12").text(row.BestLastPricePaid12);
            $("#spnCleintBestPricePaid12").text(row.CleintBestPricePaid12);
        }
        $('#divBlockBox').hide();

    },
    getPartDetailError: function (args) {
    },
    hidpartdetaildive: function () {
        this.showDetailDiv(false);


    }
    ,//start code by umendra

    showImportSourcingResultForm: function () {


        //this._frmAddExpediteNote._ReqIds = this._ReqIds;
        //this._frmAddExpediteNote._intBOMID = this._intBOMID;
        //this._frmAddExpediteNote._intBOMNo = this._BOMNo;
        //this._frmAddExpediteNote._RequestToPOHubBy = this._RequestToPOHubBy;
        //this._frmAddExpediteNote._UpdateByPH = this._UpdateByPH;
        //this._frmAddExpediteNote._BomName = this._BomName;
        //this._frmAddExpediteNote._BomCompanyNo = this._BomCompanyNo;
        //this._frmAddExpediteNote._intContact2No = this._intContact2No;
        //this._frmAddExpediteNote.setFieldValue("ctlExpediteNotes", "");
        this.showForm(this._frmImportSourcing, true);
    },
    //end code by umendra

    showHubImportSRForm: function () {
        //this._frmHubImportSR._intCustomerRequirementID = this._tblStock._varSelectedValue;
        this._frmHubImportSR._intBOMID = this._intBOMID;
        this.showForm(this._frmHubImportSR, true);
    },
    cancelHubImportSRForm: function () {
        var isGridModified = this._frmHubImportSR.checkUnsaveData();
        if (isGridModified) {
            var unsaveWarningMessage = "Your input data has not been saved and will be cleared for the action. Do you want to continue?";
            var isConfirm = confirm(unsaveWarningMessage);
            if (!isConfirm) {
                return;
            }
        }

        this._frmHubImportSR.resetForm();
        this.showForm(this._frmHubImportSR, false);
        this.onImportSourcingResultSuccess();
    },
    showInvalidImportWarning: function (isShow) {
        $('#BomImportWarning').remove();
        if (isShow) {
            var warningHtml = String.format("<span id=\"BomImportWarning\" title=\"{0}\" class=\"ihspartstatusdoc\"></span>"
                , "HUBRFQ Items are invalid. Kindly check and use the 'BOM Import' function on the 'Uploaded Document' section to modify."
            );
            $('#ctl00_cphMain_ctlBOMItems_ctlDB_ctl06').parent().append(warningHtml);
        }
    }
};
Rebound.GlobalTrader.Site.Controls.Nuggets.BOMItems.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.BOMItems", Rebound.GlobalTrader.Site.Controls.Nuggets.Base);
Rebound.GlobalTrader.Site.Functions.openIHSPDFWindow = function (intIHSPartNo) {
    var strURL = String.format("IHSPDFDocument.aspx?ihs=" + intIHSPartNo);
    window.open(strURL, "winIHSPDFDocument", "left=20,top=20,width=900,height=400,toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=yes,scrollbars=yes");
};
$RGT_openIHSDoc = function (intID) {
    $R_FN.openIHSPDFWindow(intID);
};