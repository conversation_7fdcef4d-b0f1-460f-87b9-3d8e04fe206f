﻿namespace GlobalTrader2.Orders.UserCases.Orders.PurchaseOrder.Queries.GetEprRejectedLog;

public class GetEprRejectedLogHandler : IRequestHandler<GetEprRejectedLogQuery, BaseResponse<EprRejectedLogReadModel>>
{
    private readonly IBaseRepository<EprRejectedLogReadModel> _eprRejectedLogRepository;

    public GetEprRejectedLogHandler(IBaseRepository<EprRejectedLogReadModel> eprRejectedLogRepository)
    {
        _eprRejectedLogRepository = eprRejectedLogRepository;
    }

    public async Task<BaseResponse<EprRejectedLogReadModel>> Handle(GetEprRejectedLogQuery request, CancellationToken cancellationToken)
    {
        var response = new BaseResponse<EprRejectedLogReadModel>();
        var parameters = new List<SqlParameter>()
        {
            new SqlParameter("@EPRNo", SqlDbType.Int)
            {
                Value = request.EprNo
            }
        };

        var result = await _eprRejectedLogRepository
            .SqlQueryRawAsync($"{StoredProcedures.Select_EPR_Rejected_Log} @EPRNo", parameters.ToArray());

        if (result is null)
            throw new ArgumentException($"Cannot find EPR Rejected Log with id {request.EprNo}");

        response.Data = result[0];
        response.Success = true;

        return response;
    }
}
