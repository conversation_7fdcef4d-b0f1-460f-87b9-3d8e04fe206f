export class AddLineDetailModel {
    Part;
    DateCode;
    CustomerPart;
    Price;
    Quantity;
    Manufacturer;
    ManufacturerNo;
    ProductNo;
    Product
    PackageNo;
    Package;
    ROHS;
    LineNotes;
    ShippingInstructions;
    Msl;
    MslNo;
    AS6081; //boolean
    ProductSource;
    IsIPO;
    IsIPOExist;
    Cost;
    ServiceDescription;
    ServiceName;
    ClientCurrency;
    EccnCode;
    PoDeliveryDate;
    LandedCost;
    SourceItemId;
    SourcingResultNo;
}
