@using Microsoft.AspNetCore.Mvc.Localization
@using Microsoft.Extensions.Localization

@inject IStringLocalizer<GlobalTrader2.SharedUI.CommonResources> _commonLocalizer
@inject IStringLocalizer<GlobalTrader2.SharedUI.MessageResources> _messageLocalizer
@inject IViewLocalizer _localizer

@model GlobalTrader2.SharedUI.Areas.Common.Pages.Shared.Components.UploadMediaDialog.UploadMediaDialogViewModel

<div class="dialog-container" id="@Model.DialogId" title="@_localizer[Model.DialogTitle]" style="display: none;">
    <div class="dialog-description">
        <div class="d-flex justify-content-between">
            <h5 class="text-uppercase">@_localizer[Model.DialogName]</h5>
            <span>
                <span class="me-1 required">*</span>@_commonLocalizer["denotes a required field"]
            </span>
        </div>
        <div class="line"></div>
        <div class="mb-2">@_localizer[Model.DialogDescription]</div>
        <div class="form-error-summary" style="display: none;">
            <img src="~/img/icons/x-octagon.svg" alt="X-icon" />
            <div id="form-error-content">
            </div>
        </div>
    </div>

    <form method="post" id="@Model.FormId" class="row common-form" enctype="multipart/form-data">
        @Html.AntiForgeryToken()
        <input type="hidden" name="sectionName" value="@Model.SectionName" />
        <div class="form-control-wrapper col-12">
            <div class="row g-3 align-items-center">
                <div class="col-md-2">
                    <label for="@string.Format("{0}-file-field", Model.FormId)" class="form-label m-0">@_localizer["File"]<span class="required">*</span></label>
                </div>
                <div class="col-md-10">
                    <div id="file-upload-zone" class="drop-zone">
                        <p class="mb-0 d-flex justify-content-center gap-2 align-items-center">
                            Drag & drop your file here or
                            <button class="btn btn-primary" id="uploadBtn">
                                <img src="/img/icons/upload-file.svg" alt="Add">
                                <span>Upload file</span>
                            </button>
                        </p>
                        <div id="ErrorFileInput" class="error-message text-danger mt-2 d-none"></div> <!-- Error appears here -->
                        <div class="progress-wrapper d-flex gap-2 align-items-center mt-3 d-none">
                            <div class="progress w-100">
                                <div class="progress-bar progress-bar-striped progress-bar-animated" style="width: 0%"></div>
                            </div>
                            <button class="btn btn-sm btn-danger abort-btn">Abort</button>
                        </div>
                        @* Input to hold uploaded file in browser *@
                        <input type="file" id="FileInput"
                               accept="@Model.AllowedFileExtensions"
                               hidden multiple>
                    </div>
                </div>
            </div>

            @* File review container (file is stored on browser,not posted to blob yet)*@
            <div id="files-to-upload-container" class="row g-2 align-items-center pt-2"></div>
        </div>
    </form>
</div>
