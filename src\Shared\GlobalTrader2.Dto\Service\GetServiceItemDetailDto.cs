﻿namespace GlobalTrader2.Dto.Service
{
    public class GetServiceItemDetailDto
    {
        public int ServiceId { get; set; }
        public int ClientNo { get; set; }
        public string ServiceName { get; set; } = string.Empty;
        public string ServiceDescription { get; set; } = string.Empty;
        public decimal Price { get; set; }
        public decimal Cost { get; set; }
        public string? Notes { get; set; }
        public int? LotNo { get; set; }
        public bool Inactive { get; set; }
        public int UpdatedBy { get; set; }
        public DateTime DLUP { get; set; }
        public string? RefIdHK { get; set; }
        public string? LotName { get; set; }
        public int Allocations { get; set; }
        public int Quantity { get; set; } = 1;
        public string? PriceString { get; set; }
        public string? CostString { get; set; }
        public string? Msl { get; set; }
        public string? ClientCurrencyCode { get; set; }
    }
}
