﻿namespace GlobalTrader2.Core.Domain.Entities
{
    public class POQuoteReadModel
    {
        public int? PurchaseRequestId { get; set; }
        public int? ClientNo { get; set; }
        public int? PurchaseRequestNumber { get; set; }
        public string? Notes { get; set; }
        public bool? Closed { get; set; }
        public int? UpdatedBy { get; set; }
        public DateTime? DLUP { get; set; }
        public int? Buyer { get; set; }
        public string? EmployeeName { get; set; }
        public int? DivisionNo { get; set; }
        public string? DivisionName { get; set; }
        public string? PRStatus { get; set; }
    }
}
