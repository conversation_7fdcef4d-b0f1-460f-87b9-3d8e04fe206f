﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace GlobalTrader2.Core.Domain.Entities;

[Table("tbEPR")]
public class Epr
{
    [Key]
    public int EPRId { get; set; }
    public int PurchaseOrderId { get; set; }
    public int PurchaseOrderNumber { get; set; }
    public bool? IsNew { get; set; }
    public string CompanyName { get; set; } = string.Empty;
    public decimal OrderValue { get; set; }
    public string CurrencyCode { get; set; } = string.Empty;
    public DateTime? DeliveryDate { get; set; }
    public bool? InAdvance { get; set; }
    public bool? UponReceipt { get; set; }
    public int? NetSpecify { get; set; }
    public string OtherSpecify { get; set; } = string.Empty;
    public bool? TT { get; set; }
    public bool? Cheque { get; set; }
    public bool? CreditCard { get; set; }
    public string Comments { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Address { get; set; } = string.Empty;
    public string Tel { get; set; } = string.Empty;
    public string Fax { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string Name1 { get; set; } = string.Empty;
    public string Address1 { get; set; } = string.Empty;
    public string Tel1 { get; set; } = string.Empty;
    public string Fax1 { get; set; } = string.Empty;
    public string Email1 { get; set; } = string.Empty;
    public string Comment { get; set; } = string.Empty;
    public string Name2 { get; set; } = string.Empty;
    public string Address2 { get; set; } = string.Empty;
    public string Tel2 { get; set; } = string.Empty;
    public string Fax2 { get; set; } = string.Empty;
    public string Email2 { get; set; } = string.Empty;
    public bool? ProFormaAttached { get; set; }
    public string? RaisedBy { get; set; }
    public DateTime? RaisedByDate { get; set; }
    public bool? SORSigned { get; set; }
    public bool? ForStock { get; set; }
    public bool? ValuesCorrect { get; set; }
    public string? Authorized { get; set; } = string.Empty;
    public DateTime? AuthorizedDate { get; set; }
    public bool? ERAIMember { get; set; }
    public bool? ERAIReported { get; set; }
    public bool? DebitNotes { get; set; }
    public bool? APOpenOrders { get; set; }
    public decimal ACTotalValue { get; set; }
    public decimal ACTotalValue1 { get; set; }
    public string SLComment { get; set; } = string.Empty;
    public string SLTerms { get; set; } = string.Empty;
    public bool? SLOverdue { get; set; }
    public decimal SLTotalValue { get; set; }
    public string? PaymentAuthorizedBy { get; set; } = string.Empty;
    public string? Countersigned { get; set; } = string.Empty;
    public DateTime? PaymentAuthorizedDate { get; set; }
    public int? UpdatedBy { get; set; }
    public DateTime? DLUP { get; set; }
    public string SupplierCode { get; set; } = string.Empty;
    public int? EPRCompletedByNo { get; set; }
    public int? RaisedByNo { get; set; }
    public int? RefIdHK { get; set; }
    public int? NewRecord { get; set; }
    public bool? Inactive { get; set; }
}
