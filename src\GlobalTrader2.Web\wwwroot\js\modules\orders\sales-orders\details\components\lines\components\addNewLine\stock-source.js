﻿import { SourceFilterComponent } from './source-filter-component.js?v=#{BuildVersion}#';
import { SourceItemModel } from '../../models/source-item.model.js';
import { FromSourceTypeConstant } from '../../configs/source-type.config.js';
import { FieldType } from '../../../../../../../../components/table-filter/constants/field-type.constant.js?v=#{BuildVersion}#';
import { NumberType } from "../../../../../../../../components/table-filter/constants/number-type.constant.js?v=#{BuildVersion}#";
import { ROHSHelper } from "../../../../../../../../helper/rohs-helper.js?v=#{BuildVersion}#";
export class StockSourceManager {
    constructor({ onSelectedSourceItem = (data) => { } }) {
        this.tableFilter = null;
        this.stocksSourceTable = null;
        this.stocksSourceUrl = "/api/stocks/item-search";
        this.onSelectedSourceItem = onSelectedSourceItem;
        this.pageSize = 10;
        this.sortBy = 1;
        this.orderBy = 2;
        this.filterInputs = [
            {
                fieldType: FieldType.TEXT,
                label: 'Part No',
                name: 'PartNo',
                id: 'PartNo',
                value: '',
                attributes: {
                    "maxlength": 50
                },
                locatedInContainerByClass: 'filter-column-1'
            },
            {
                fieldType: FieldType.NUMBER,
                label: 'Purchase Order',
                name: 'PoNo',
                id: 'PoNo',
                attributes: {
                    "data-input-type": "numeric",
                    "data-input-format": "int",
                    "data-input-min": 0,
                    "data-input-max": 2147483647,
                    "data-input-type-allow-empty": true,
                },
                extraPros: {
                    numberType: NumberType.INT
                },
                value: '',
                locatedInContainerByClass: 'filter-column-1'
            },
            {
                fieldType: FieldType.SELECT,
                label: 'Warehouse',
                name: 'Warehouse',
                id: 'Warehouse',
                value: '',
                options: {
                    serverside: false,
                    endpoint: "lists/warehouse-for-client",
                    valueKey: 'id',
                    textKey: 'name',
                    isHideRefresButton: false,
                    isCacheApplied: true,
                    placeholderValue: "",
                },
                locatedInContainerByClass: 'filter-column-2'
            },
            {
                fieldType: FieldType.TEXT,
                label: 'Location',
                name: 'Location',
                id: 'Location',
                value: '',
                attributes: {
                    "maxlength": 50
                },
                locatedInContainerByClass: 'filter-column-2'
            }
        ]
    }

    async initialize() {
        $("#stocks-source").show();
        await this.initTableFilter();
        this.initDataTable();
    }

    async initTableFilter() {
        this.tableFilter = new SourceFilterComponent('#stocks-source-filter-section-wrapper', 'Search for and select the item you would like to use as the source for the new Line and press Continue', {
            inputConfigs: this.filterInputs,
            wrapperClass: 'bg-none m-0 p-0'
        });

        await this.tableFilter.init();
        this.tableFilter.on('applied.mtf', () => {
            this.stocksSourceTable.ajax.url(this.stocksSourceUrl);
            GlobalTrader.Helper.reloadPagingDatatableServerSide(this.stocksSourceTable, true);
        })
        this.tableFilter.on('cancel.mtf', () => {
            if (window.currentXhr) {
                window.currentXhr.abort();
                if ($('#stocksSourceTbl_processing').is(':visible')) {
                    $('#stocksSourceTbl_processing').hide();
                }
                this.tableFilter.toggleApplyCancelButtons(true);
            }
        })
    }

    initDataTable() {
        this.stocksSourceTable = new DataTable('#stocksSourceTbl', {
            scrollCollapse: true,
            paging: true,
            dataSrc: 'data',
            serverSide: true,
            lengthMenu: [5, 10, 25, 50],
            pageLength: this.pageSize,
            ajax: {
                url: this.tableFilter.hasAnyActiveFilter() ? this.stocksSourceUrl : "",
                type: 'POST',
                contentType: 'application/json',
                beforeSend: (xhr) => {
                    window.currentXhr = xhr;
                },
                data: (data) => {
                    const filterValues = this.tableFilter.getAllValue();
                    return JSON.stringify({
                        poNoLo: filterValues.PoNo.isOn ? filterValues.PoNo.low : null,
                        poNoHi: filterValues.PoNo.isOn ? filterValues.PoNo.hi : null,
                        locationSearch: filterValues.Location.isOn ? filterValues.Location.value : null,
                        partSearch: filterValues.PartNo.isOn ? filterValues.PartNo.value : null,
                        warehouse: filterValues.Warehouse.isOn ? filterValues.Warehouse.value : null,
                        incLockCust: SODetailGeneralInfo.companyNo,
                        draw: data.draw,
                        index: data.start,
                        size: data.length,
                        sortDir: data.order.length !== 0 ? GlobalTrader.SortHelper.getSortDirIdByName(data.order[0].dir) : this.sortBy,
                        orderBy: data.order.length !== 0 ? data.order[0].column : this.orderBy
                    });
                },
            },
            info: true,
            responsive: true,
            select: {
                style: 'single'
            },
            ordering: true,
            searching: false,
            processing: true,
            columnDefs: [
                { "orderSequence": ["asc", "desc"], "targets": "_all" },
            ],
            language: {
                emptyTable: `<i>${window.localizedStrings.noDataFound}</i>`,
                zeroRecords: `<i>${window.localizedStrings.noDataFound}</i>`,
                infoFiltered: "",
                lengthMenu: "_MENU_ per page",
                loadingRecords: "",
            },
            dom: '<"dt-layout-row dt-layout-table" <"dt-layout-cell dt-layout-full" rt >>' +
                '<"dt-layout-row" <"dt-layout-cell dt-layout-start" i l >' +
                '<"dt-layout-cell dt-layout-end" p >><"clear">',
            rowId: "stockId",
            order: [[this.sortBy, "asc"]],
            columns: [
                {
                    name: 'stockId',
                    data: 'stockId',
                    title: 'stockId',
                    visible: false
                },
                {
                    data: (row) => (
                        {
                            partNo: row.part,
                            supplierPart: row.supplierPart,
                            rohs: row.rohs
                        }
                    ),
                    name: 'partNo_supplierPart',
                    title: `<div style="border-bottom: dotted 1px #999; margin-bottom: 5px; padding-bottom: 8px;">Part No</div>Supplier Part`,
                    type: 'string',
                    className: 'text-wrap text-break',
                    render: (data) => {
                        let escapedSupplierPart = "";
                        if (data.supplierPart) {
                            const supplierPartText = GlobalTrader.StringHelper.setCleanTextValue(data.supplierPart);
                            escapedSupplierPart = DataTable.render.text().display(supplierPartText);
                        }

                        return `<p class="m-0" style="min-height: 15px;">${ROHSHelper.writePartNo(data.partNo, data.rohs)}</p><p class="m-0" style="min-height: 15px;">${escapedSupplierPart}</p>`;
                    }
                },
                {
                    data: (row) => (
                        {
                            quantityInStock: row.quantityInStock,
                            quantityOnOrder: row.quantityOnOrder
                        }
                    ),
                    name: 'quantityInStock_quantityOnOrder',
                    title: `<div style="border-bottom: dotted 1px #999; margin-bottom: 5px; padding-bottom: 8px;">Qty In Stock(FREE)</div>Qty On Order`,
                    type: 'string',
                    className: 'text-wrap text-break',
                    render: (data) => {
                        return `<p class="m-0" style="min-height: 15px;">${data.quantityInStock}</p><p class="m-0" style="min-height: 15px;">${data.quantityOnOrder}</p>`;
                    }
                },
                {
                    data: (row) => (
                        {
                            quantityAllocated: row.quantityAllocated,
                            quantityAvailable: row.quantityAvailable
                        }
                    ),
                    name: 'quantityAllocated_quantityAvailable',
                    title: `<div style="border-bottom: dotted 1px #999; margin-bottom: 5px; padding-bottom: 8px;">Qty Allocated</div>Qty Available`,
                    type: 'string',
                    className: 'text-wrap text-break',
                    render: (data) => {
                        return `<p class="m-0" style="min-height: 15px;">${data.quantityAllocated}</p><p class="m-0" style="min-height: 15px;">${data.quantityAvailable}</p>`;
                    }
                },
                {
                    data: (row) => (
                        {
                            warehouseName: row.warehouseName,
                            location: row.location
                        }
                    ),
                    name: 'warehouse_location',
                    title: `<div style="border-bottom: dotted 1px #999; margin-bottom: 5px; padding-bottom: 8px;">Warehouse</div>Location`,
                    type: 'string',
                    className: 'text-wrap text-break',
                    render: (data) => {
                        let escapedWarehouse = "";
                        if (data.warehouseName) {
                            const warehouseText = GlobalTrader.StringHelper.setCleanTextValue(data.warehouseName);
                            escapedWarehouse = DataTable.render.text().display(warehouseText);
                        }

                        let escapedLocation = "";
                        if (data.location) {
                            const locationText = GlobalTrader.StringHelper.setCleanTextValue(data.location);
                            escapedLocation = DataTable.render.text().display(locationText);
                        }

                        return `<p class="m-0" style="min-height: 15px;">${escapedWarehouse}</p><p class="m-0" style="min-height: 15px;">${escapedLocation}</p>`;
                    }
                },
                {
                    data: (row) => (
                        {
                            supplierName: row.supplierName,
                            formatedLandedCost: row.formatedLandedCost
                        }
                    ),
                    name: 'supplierName_formatedLandedCost',
                    title: `<div style="border-bottom: dotted 1px #999; margin-bottom: 5px; padding-bottom: 8px;">Supplier</div>Landed Cost`,
                    type: 'string',
                    className: 'text-wrap text-break',
                    render: (data) => {
                        let escapedSupplierName = "";
                        if (data.supplierName) {
                            const supplierNameText = GlobalTrader.StringHelper.setCleanTextValue(data.supplierName);
                            escapedSupplierName = DataTable.render.text().display(supplierNameText);
                        }

                        return `<p class="m-0" style="min-height: 15px;">${escapedSupplierName}</p><p class="m-0" style="min-height: 15px;">${data.formatedLandedCost}</p>`;
                    }
                },
                {
                    data: (row) => (
                        {
                            purchaseOrderNumber: row.purchaseOrderNumber,
                            poSerialNo: row.poSerialNo,
                            poDeliveryDateText: row.poDeliveryDateText
                        }
                    ),
                    name: 'quantityAllocated_quantityAvailable',
                    title: `<div style="border-bottom: dotted 1px #999; margin-bottom: 5px; padding-bottom: 8px;">PO (Line No)</div>Delivered`,
                    type: 'string',
                    className: 'text-wrap text-break',
                    render: (data) => {
                        return `<p class="m-0" style="min-height: 15px;">${GlobalTrader.StringHelper.showSerialNumber(data.purchaseOrderNumber, data.poSerialNo)}</p><p class="m-0" style="min-height: 15px;">${GlobalTrader.StringHelper.setCleanTextValue(data.poDeliveryDateText)}</p>`;
                    }
                },
            ]
        }).on('draw.dt', () => {
            this.stocksSourceTable.columns.adjust();
        }).on('processing.dt', (e, settings, processing) => {
            this.removeNeutralSortingIcon(this.stocksSourceTable);
            if (processing) {
                // Table is processing (e.g., AJAX call happening)
                this.tableFilter.toggleApplyCancelButtons(false);
            } else {
                // Done processing
                this.tableFilter.toggleApplyCancelButtons(true);
            }
        });

        this.stocksSourceTable.on('select', async (e, dt, type, indexes) => {
            if (type === 'row') {
                const selectedRowId = dt.row(indexes).id();
                this.stocksSourceTable.rows('.selected').deselect();
                this.onSelectedSourceItem(new SourceItemModel(FromSourceTypeConstant.STOCK, selectedRowId));
            };
        })
    }

    clearTable() {
        $("#stocks-source").hide();
        if ($('#stocksSourceTbl_processing').is(':visible')) {
            $('#stocksSourceTbl_processing').hide();
        }
        if (this.stocksSourceTable != null) {
            this.stocksSourceTable.rows('.selected').deselect();
            this.stocksSourceTable.ajax.url('').load();
            this.stocksSourceTable.ajax.url(this.stocksSourceUrl);
        }
    }

    doSearch() {
        $("#stocks-source").show();
        if (this.tableFilter.hasAnyActiveFilter()) {
            GlobalTrader.Helper.reloadPagingDatatableServerSide(this.stocksSourceTable, true);
        }
    }

    formatDateFilter(dateString) {
        const [day, month, year] = dateString.split("/");
        return `${year}-${month}-${day}T00:00:00.000Z`
    }

    removeNeutralSortingIcon(datatable) {
        // Remove neutral sorting icon
        const tableId = datatable.table().node().id;
        $(`#${tableId} thead th`)
            .removeClass('dt-orderable-asc dt-orderable-desc')
            .addClass('position-relative');

        $(`#${tableId} thead th:not(.dt-orderable-none)`)
            .attr('role', 'button');

        $(`#${tableId} thead th .dt-column-order`).addClass('dt-column-order-custom');
    }
}