namespace GlobalTrader2.Dto.SalesOrderLine
{
    public class GetSoLineDetailsDto
    {
        public int IsSourcingResultExist { get; set; }
        public int? SONo { get; set; }
        public int? SalesOrderNumber { get; set; }
        public string Part { get; set; } = string.Empty;
        public string? Mfr { get; set; }
        public int? MfrNo { get; set; }
        public string MfrAdvisoryNotes { get; set; } = string.Empty;
        public string DateCd { get; set; } = string.Empty;
        public string PackageName { get; set; } = string.Empty;
        public string Package { get; set; } = string.Empty;
        public int? PackageNo { get; set; }
        public string? Quantity { get; set; }
        public string CustomerPart { get; set; } = string.Empty;
        public string Price { get; set; } = string.Empty;
        public string PriceVal { get; set; } = string.Empty;
        public string CurrencyCode { get; set; } = string.Empty;
        public string? Shipped { get; set; }
        public string? Allocated { get; set; }
        public string? BackOrder { get; set; }
        public string DatePromised { get; set; } = string.Empty;
        public string RequiredDate { get; set; } = string.Empty;
        public DateTime? DatePromisedRawValue { get; set; }
        public DateTime? DateRequiredRawValue { get; set; }
        public string DeliveryDate { get; set; } = string.Empty;
        public string Instructions { get; set; } = string.Empty;
        public string ProductName { get; set; } = string.Empty;
        public string Product { get; set; } = string.Empty;
        public int? ProductNo { get; set; }
        public bool Closed { get; set; }
        public bool Posted { get; set; }
        public bool ShipASAP { get; set; }
        public bool IsAllocated { get; set; }
        public bool IsShipped { get; set; }
        public bool Inactive { get; set; }
        public string LineValue { get; set; } = string.Empty;
        public int? StockNo { get; set; }
        public int? ServiceNo { get; set; }
        public byte? ROHS { get; set; }
        public int? PurchaseOrderNo { get; set; }
        public int? PurchaseOrderNumber { get; set; }
        public int? WarehouseNo { get; set; }
        public string Warehouse { get; set; } = string.Empty;
        public int? AllocationId { get; set; }
        public int? AllocatedQuantity { get; set; }
        public int? SupplierNo { get; set; }
        public string Supplier { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public string LineNotes { get; set; } = string.Empty;
        public int CompanyNo { get; set; }
        public bool isIpoApproved { get; set; }
        public string CompanyName { get; set; } = string.Empty;
        public int InternalPurchaseOrderNo { get; set; }
        public bool IsIpoExist { get; set; }
        public bool IsIpoOpen { get; set; }
        public bool IsIpo { get; set; }
        public string PoDelDate { get; set; } = string.Empty;
        public bool SourcingResultUsedByOther { get; set; }
        public string CloneSerialNo { get; set; } = string.Empty;
        public string SOSerialNo { get; set; } = string.Empty;
        public int QuoteId { get; set; }
        public int QuoteLineNo { get; set; }
        public int QuoteNumber { get; set; }
        public byte? ProductSource { get; set; }
        public string ProductSourceName { get; set; } = string.Empty;
        public bool ProdInactive { get; set; }
        public string DutyCodeAndRate { get; set; } = string.Empty;
        public string MSLLevel { get; set; } = string.Empty;
        public string ContractNo { get; set; } = string.Empty;
        public bool IsProdHaz { get; set; }
        public bool IsPrintHaz { get; set; }
        public string DateConfirmed { get; set; } = string.Empty;
        public int? CustomerRequirementId { get; set; }
        public int? CustomerRequirementNumber { get; set; }
        public string CountryOfOrigin { get; set; } = string.Empty;
        public string LifeCycleStage { get; set; } = string.Empty;
        public string HTSCode { get; set; } = string.Empty;
        public double AveragePrice { get; set; }
        public string Packaging { get; set; } = string.Empty;
        public string PackagingSize { get; set; } = string.Empty;
        public string Descriptions { get; set; } = string.Empty;
        public string DescShort { get; set; } = string.Empty;
        public string IHSProduct { get; set; } = string.Empty;
        public bool IsPoHub { get; set; }
        public string ECCNCode { get; set; } = string.Empty;
        public bool IsOrderViaIpoOnly { get; set; }
        public bool IsRestrictedProduct { get; set; }
        public string ProductMessage { get; set; } = string.Empty;
        public string MsgHazardous { get; set; } = string.Empty;
        public string MsgIpo { get; set; } = string.Empty;
        public string MsgRestricted { get; set; } = string.Empty;
        public string IhsEccnsCodeDefination { get; set; } = string.Empty;
        public string StockAvailableDetail { get; set; } = string.Empty;
        public bool AS6081 { get; set; }
        public string Cost { get; set; } = string.Empty;
        public string ClientCurrency { get; set; } = string.Empty;
        public string Taxable { get; set; } = string.Empty;
        public string BlankECCNCode { get; set; } = string.Empty;
        public string? PoECCNCode { get; set; }
    }
}