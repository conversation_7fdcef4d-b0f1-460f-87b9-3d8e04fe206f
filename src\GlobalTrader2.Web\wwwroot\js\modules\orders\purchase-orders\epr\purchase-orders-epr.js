﻿$(async () => {
    const purchaseOrdersEPRManager = new PurchaseOrdersEarlyPaymentRequestManager();
    purchaseOrdersEPRManager.initialize();
})

class PurchaseOrdersEarlyPaymentRequestManager {
    constructor() {
        this._keyword = "epr";
        this._purchaseOrderId = getParameterByName('po');

        this.$valueAndCurrency = $("#ValueCurrency");
        this.$net = $("#Net");

        this.$raisedByDropdown = $("#RaisedBySelect");
        this.$deliveryDatePicker = $("#DeliveryDate");
        this.$raisedByDatePicker = $("#SaleDate");
    }

    async initialize() {
        this._setupDropdowns();
        this._setupDatePicker();
        this._bindOnChangeIntegerInput();
    }

    _setupDropdowns() {
        this.$raisedByDropdown.dropdown({
            serverside: false,
            endpoint: `/lists/employee`,
            valueKey: 'loginId',
            textKey: 'employeeName',
            placeholderValue: "",
            selectedValue: this.$raisedByDropdown.data("raised-by-no"),
        });
    }

    _setupDatePicker() {
        this.$deliveryDatePicker.datepicker2({
            dateFormat: "dd/mm/yy"
        });

        this.$raisedByDatePicker.datepicker2({
            dateFormat: "dd/mm/yy"
        });

        if (this.$raisedByDatePicker.val() === "") {
            this.$raisedByDatePicker.datepicker2("setToDay");
        }
    }

    _bindOnChangeIntegerInput() {
        allowPositiveDecimalInput('#ValueCurrency');
        allowPositiveDecimalInput('#Net');
        allowPositiveDecimalInput('#OutStandingDebitTotalValue');
        allowPositiveDecimalInput('#AdvancePaymentTotalValue');
        allowPositiveDecimalInput('#SalesLedgerOverdueTotalValue');
    }
}
