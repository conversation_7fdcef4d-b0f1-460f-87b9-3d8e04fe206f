﻿import { FieldType } from '../../../../components/table-filter/constants/field-type.constant.js?v=#{BuildVersion}#';
import { ClientId } from '../../constants/orders.constant.js?v=#{BuildVersion}';
export const inputFilterDefinition = [
    {
        fieldType: FieldType.RADIO,
        label: '',
        name: 'HeaderOrDetail',
        value: '',
        options: {
            multiplesOptions: [{ label: 'Header', value: 'header' }, { label: 'Detail', value: 'detail' }],
        },
        locatedInContainerByClass: 'filter-column-1'
      },
    {
       fieldType: FieldType.TEXT,
       label: 'Name',
       name: 'Name',
       value: '',
       locatedInContainerByClass: 'filter-column-2'
    },
    {
       fieldType: FieldType.TEXT,
       label: 'Code',
       name: 'Code',
       value: '',
       locatedInContainerByClass: 'filter-column-1'
    },
    {
       fieldType: FieldType.SELECT,
       label: 'Client',
       name: 'Client',
       id: 'Client',
       value: '',
       options: {
           serverside: false,
           endpoint: '/lists/active-hubrfq-clients',
           valueKey: 'clientId',
           textKey: 'clientName',
           isCacheApplied: true,
           isCacheAppliedOnInit: true
       },
       locatedInContainerByClass: 'filter-column-2'
    },
    {
       fieldType: FieldType.SELECT,
       label: 'Status',
       name: 'Status',
       id: 'Status',
       value: '',
       options: {
           serverside: false,
           endpoint: '/lists/bom-status',
           isHideRefresButton: false,
           isCacheApplied: true,
           isCacheAppliedOnInit: true
       },
       locatedInContainerByClass: 'filter-column-1'
    },
    {
       fieldType: FieldType.SELECT,
       label: 'Salesperson',
       name: 'Salesperson',
       id: 'Salesperson',
       value: '',
       options: {
           serverside: false,
           endpoint: `/lists/employee?globalLoginClientNo=${ClientId.Default}`,
           valueKey: 'loginId',
           textKey: 'employeeName',
           isCacheApplied: true,
           isCacheAppliedOnInit: true
       },
       locatedInContainerByClass: 'filter-column-2'
    },
    {
       fieldType: FieldType.SELECT,
       label: 'Assigned User',
       name: 'AssignedUser',
       id: 'AssignedUser',
       value: '',
       options: {
           serverside: false,
           endpoint: '/lists/employee/purchase-hub/114',
           valueKey: 'loginId',
           textKey: 'employeeName',
           isHideRefresButton: false,
           isCacheAppliedOnInit: true
       },
       locatedInContainerByClass: 'filter-column-1'
    }
]