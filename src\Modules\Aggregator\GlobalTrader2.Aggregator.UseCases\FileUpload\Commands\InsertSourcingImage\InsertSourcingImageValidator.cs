﻿using FluentValidation;
using GlobalTrader2.Aggregator.UseCases.FileUpload.Commands.InsertSourcingImage;

namespace GlobalTrader2.Orders.UserCases.Orders.Sourcing.Commands.InsertSourcingImage
{
    public class InsertSourcingImageValidator : AbstractValidator<InsertSourcingImageCommand>
    {
        public InsertSourcingImageValidator()
        {
            RuleFor(x => x.SourcingNo).NotEmpty().NotNull();
            RuleFor(x => x.ImageName).NotEmpty().NotNull();
        }
    }
}