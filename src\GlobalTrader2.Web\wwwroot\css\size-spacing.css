﻿/*Height*/
.h-22 {
    height: 22px !important;
}

.h-21 {
    height: 21px;
}

.h-20 {
    height: 20px !important;
}

.h-28 {
    height: 28px !important;
}

.h-40 {
    height: 40px !important;
}

.mh-200 {
    min-height: 200px;
}

.mh-800 {
    min-height: 800px;
}

.mh-1500 {
    min-height: 1500px;
}

.max-h-300 {
    max-height: 300px;
}

/*Width*/
.mw-300 {
    min-width: 300px;
}

.max-w-500 {
    max-width: 500px;
}

.w-100px {
    width: 100px;
}

.w-40 {
    width: 40px;
}

.w-20 {
    width: 20% !important;
}

.w-25 {
    width: 25% !important;
}

.w-fit-content {
    width: fit-content;
}

/*Font size*/
.fs-11 {
    font-size: 11px !important;
}

.fs-12 {
    font-size: 12px !important;
}

.fs-14 {
    font-size: 14px !important;
}

.fs-15 {
    font-size: 14px !important;
}

.fs-18 {
    font-size: 18px !important;
}

.fs-22 {
    font-size: 22px !important;
}

/*Margin*/
.my-5px {
    margin-top: 5px !important;
    margin-bottom: 5px !important;
}

.my--5px {
    margin-top: -5px;
    margin-bottom: -5px;
}

.ml-15px {
    margin-left: 15px;
}
.ml-10px {
    margin-left: 10px;
}

.mt-10px{
    margin-top: 10px !important;
}

.mb-10px{
    margin-bottom: 10px !important;
}

.mb-5px{
    margin-bottom: 5px;
}

.mr-10px {
    margin-right: 10px;
}

/*Padding*/
.py-5px {
    padding-top: 5px;
    padding-bottom: 5px;
}

.px-10px {
    padding-left: 10px;
    padding-right: 10px;
}

.pr-0 {
    padding-right: 0;
}
.pl-12px {
    padding-left: 12px;
}
/*Z-index*/
.z-index-1 {
    z-index: 1 !important;
}
.z-index-2 {
    z-index: 2 !important;
}
.lh-10px{
    line-height:10px;
}
.pr-15px{
    padding-right:15px;
}
.gap-5px{
    gap:5px;
}
.w-83 {
    width: 83%;
}
.pr-10px{
    padding-right:10px;
}
.ml-3px{
    margin-left: 3px;
}
.px-5px{
    padding-left: 5px;
    padding-right: 5px;
}
.lh-14px{
    line-height: 14px;
}
.gap-1px {
    gap: 1px;
}