@page
@using GlobalTrader2.Orders.UI.Areas.Orders.Pages.PurchaseOrder.EPR
@using GlobalTrader2.SharedUI
@using GlobalTrader2.Core.Helpers
@model IndexModel

@inject IStringLocalizer<GlobalTrader2.SharedUI.CommonResources> _commonLocalizer
@inject IViewLocalizer _localizer
@inject SessionManager _sessionManager;
@inject SettingManager _settingManager;

@{
    Layout = "_FormLayout";
    ViewData["Title"] = "Early Payment Request";

    var epr = Model.EprDetails;
    var po = Model.PurchaseOrderDetails;
    var eprRejectedLog = Model.EprRejectedLog;

    // Infomation
    var isNew = epr?.IsNew ?? false;
    var supplierCode = epr?.SupplierCode ?? po?.SupplierCode ?? "";
    var companyName = epr?.CompanyName ?? po?.CompanyName ?? "";
    var companyType = epr?.CompanyType ?? po?.CompanyType ?? "";
    var purchaseOrderNumber = epr?.PurchaseOrderNumber ?? po?.PurchaseOrderNumber;
    var pols = (epr is null, string.IsNullOrWhiteSpace(Model.PurchaseOrderLinesSerialNoParams)) switch
    {
        (true, false) => Model.PurchaseOrderLinesSerialNoParams,
        (false, _) => epr?.POLineSerialNo,
        _ => null
    };
    var orderValue = epr?.OrderValue.ToString("0.####") ?? "";
    var currency = epr?.CurrencyCode ?? po?.CurrencyCode ?? "";
    var deliveryDate = epr is not null ? Functions.FormatDate(epr.DeliveryDate) : null;
    var inAdvance = epr?.InAdvance ?? false;
    var uponReceipt = epr?.UponReceipt ?? false;
    var netSpecify = epr?.NetSpecify?.ToString() ?? "";
    var otherSpecify = epr?.OtherSpecify ?? "";
    var tt = epr?.TT ?? false;
    var cheque = epr?.Cheque ?? false;
    var creditCard = epr?.CreditCard ?? false;
    var paymentMethodComment = epr?.Comments ?? "";

    // References information
    var name = epr?.Name ?? "";
    var address = epr?.Address ?? "";
    var tel = epr?.Tel ?? "";
    var fax = epr?.Fax ?? "";
    var email = epr?.Email ?? "";
    var name1 = epr?.Name1 ?? "";
    var address1 = epr?.Address1 ?? "";
    var tel1 = epr?.Tel1 ?? "";
    var fax1 = epr?.Fax1 ?? "";
    var email1 = epr?.Email1 ?? "";
    var name2 = epr?.Name2 ?? "";
    var address2 = epr?.Address2 ?? "";
    var tel2 = epr?.Tel2 ?? "";
    var fax2 = epr?.Fax2 ?? "";
    var email2 = epr?.Email2 ?? "";
    var referenceComment = epr?.Comment ?? "";

    var proFormaAttached = epr?.ProFormaAttached ?? false;
    var raisedByNo = epr?.RaisedByNo ?? _sessionManager.LoginID;
    var raisedByDate = epr is not null ? Functions.FormatDate(epr?.RaisedByDate) : null;
    var sorSigned = epr?.SORSigned ?? false;
    var forStock = epr?.ForStock ?? false;
    var valuesCorrect = epr?.ValuesCorrect ?? false;
    var authorizedBy = epr?.Authorized ?? "";
    var authorizedDate = (epr is not null && epr.AuthorizedDate is not null) ? Functions.FormatDate(epr.AuthorizedDate) : "";

    // Account only information
    var eraiMember = epr?.ERAIMember ?? null;
    var eraiReported = epr?.ERAIReported ?? null;
    var debitNotes = epr?.DebitNotes ?? false;
    var apOpenOrder = epr?.APOpenOrders ?? false;
    var totalValue = epr?.ACTotalValue.ToString("0.####") ?? "";
    var totalValue1 = epr?.ACTotalValue1.ToString("0.####") ?? "";
    var slComments = epr?.SLComment ?? "";
    var slTerms = epr?.SLTerms ?? "";
    var slOverdue = epr?.SLOverdue ?? false;
    var slTotalValue = epr?.SLTotalValue.ToString("0.####") ?? "";
    var paymentAuthorizedBy = epr?.EPRCompletedBy ?? "";
    var counterSigned = epr?.Countersigned ?? "";
    var paymentAuthorizedDate = (epr is not null && epr.PaymentAuthorizedDate is not null) ? Functions.FormatDate(epr?.PaymentAuthorizedDate) : "";

    var canAuthorize = Model.EprViewModel.CanAuthorise && string.IsNullOrEmpty(epr?.Authorized);
    var canComplete = Model.EprViewModel.CanComplete &&
                      epr is not null &&
                      !string.IsNullOrEmpty(epr.Authorized) &&
                      !epr.EPRCompletedByNo.HasValue;
    var canDelete = epr is not null && Model.EprViewModel.CanDelete;

    var showRejectedLog = eprRejectedLog?.EPRTotalRejectCounts > 0;
}

@section HeadBlock {
    @await Html.PartialAsync("Partials/_ThirdPartyStyleSheetsPartial")
    <link type="text/css" rel="stylesheet" href="~/css/date-picker.css" asp-append-version="true" />
}

<div class="mx-5">
    <section class="row">
        <div class="col-4 text-center">
            <img src="/img/rebound/ReboundLogo.jpg" alt="reboundLogo" />
        </div>
        <div class="col-4 d-flex justify-content-center align-items-center fw-bold fs-5">
            Early Payment Request
        </div>
        <div style="flex-direction: column" class="col-4 d-flex justify-content-center align-items-center fw-bold fs-5">
            <span>
                (Sub-30 Day Payment)
            </span>
            <span>
                @supplierCode
            </span>
        </div>
    </section>
    <section class="d-flex">
        <div class="col-4 pe-1">
            <table style="height: 246px">
                <thead>
                    <tr>
                        <th colspan="2">
                            ORDER DETAILS
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td colspan="2">
                            <div class="row">
                                <span class="col-6">Supplier</span>
                                <div class="d-flex col-6 gap-1">
                                    <p class="m-0">New?</p>
                                    <input type="checkbox" class="form-check-input form-control mt-0 p-0" name="New" id="New" checked="@isNew">
                                </div>
                                <span class="col-12">
                                    <span>
                                        @companyName
                                    </span>
                                    &nbsp;
                                    @if (!string.IsNullOrEmpty(companyType))
                                    {
                                        <span>
                                            (@companyType)
                                        </span>
                                    }
                                </span>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            PO Number
                        </td>
                        <td>
                            @purchaseOrderNumber

                            @if (pols is not null)
                            {
                                <span>( @pols )</span>
                            }
                        </td>
                    </tr>
                    <tr>
                        <td>
                            Value & Currency
                        </td>
                        <td>
                            <div class="d-flex col-6 gap-1">
                                <input class="form-control form-input" value="@orderValue" name="ValueCurrency" id="ValueCurrency">
                                <p class="m-0">@currency</p>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            Delivery Date
                        </td>
                        <td>
                            <div class="d-flex gap-1">
                                <input class="form-control form-input mt-0" style="height: 28px !important" type="text" name="DeliveryDate" id="DeliveryDate" value="@deliveryDate" data-input-type="DATE">
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="col-4 px-1">
            <table>
                <thead>
                    <tr>
                        <th colspan="2">
                            PAYMENT TERMS
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>
                            In Advance
                        </td>
                        <td>
                            <input type="checkbox" class="form-check-input form-control mt-0 p-0" name="InAdvance" id="InAdvance" checked="@inAdvance">
                        </td>
                    </tr>
                    <tr>
                        <td>
                            Upon Receipt
                        </td>
                        <td>
                            <input type="checkbox" class="form-check-input form-control mt-0 p-0" name="UponReceipt" id="UponReceipt" checked="@uponReceipt">
                        </td>
                    </tr>
                    <tr>
                        <td>
                            Net (Specify)
                        </td>
                        <td>
                            <div class="d-flex col-6 gap-1">
                                <input class="form-control form-input" name="Net" id="Net" maxlength="5" value="@netSpecify">
                                <strong class="m-0">Days</strong>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="2">
                            <div class="form-control-wrapper">
                                <label for="OtherSpecify" class="form-label">
                                    Other (Specify)
                                </label>
                                <textarea class="form-control form-textarea height-auto" id="OtherSpecify" name="OtherSpecify" rows="4" cols="38">@otherSpecify</textarea>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="col-4 ps-1">
            <table>
                <thead>
                    <tr>
                        <th colspan="2">
                            PAYMENT METHOD
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>
                            TT
                        </td>
                        <td>
                            <input type="checkbox" class="form-check-input form-control mt-0 p-0" name="TT" id="TT" checked="@tt">
                        </td>
                    </tr>
                    <tr>
                        <td>
                            Cheque
                        </td>
                        <td>
                            <input type="checkbox" class="form-check-input form-control mt-0 p-0" name="Cheque" id="Cheque" checked="@cheque">
                        </td>
                    </tr>
                    <tr>
                        <td>
                            Credit Card
                        </td>
                        <td>
                            <input type="checkbox" class="form-check-input form-control mt-0 p-0" name="CreditCard" id="CreditCard" checked="@creditCard">
                        </td>
                    </tr>
                    <tr>
                        <td colspan="2">
                            <div class="form-control-wrapper">
                                <label for="Comment" class="form-label">
                                    Comment
                                </label>
                                <textarea class="form-control form-textarea height-auto" id="Comment" name="Comment" rows="4" cols="40">@paymentMethodComment</textarea>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </section>

    <h1 class="text-center fs-5 fw-bold my-3">
        ------ REFERENCES --------
    </h1>

    <section class="d-flex">
        <div class="col-6 pe-1">
            <table>
                <tbody>
                    <tr>
                        <td colspan="2">
                            Name
                        </td>
                        <td colspan="2">
                            <input class="form-control form-input" name="Name" id="Name" value="@name">
                        </td>
                    </tr>
                    <tr>
                        <td colspan="4">
                            <div class="form-control-wrapper">
                                <label for="Comment" class="form-label">
                                    Address
                                </label>
                                <textarea class="form-control form-textarea height-auto" id="Address" name="Address" rows="2" cols="64">@address</textarea>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td class="col-1">
                            Tel
                        </td>
                        <td class="col-5">
                            <input class="form-control form-input" name="Tel" id="Tel" value="@tel">
                        </td>
                        <td class="col-1">
                            Fax
                        </td>
                        <td class="col-5">
                            <input class="form-control form-input" name="Fax" id="Fax" value="@fax">
                        </td>
                    </tr>
                    <tr>
                        <td colspan="2">
                            Email
                        </td>
                        <td colspan="2">
                            <input class="form-control form-input" name="Email" id="Email" type="email" value="@email">
                        </td>
                    </tr>
                    <tr>
                        <th colspan="4" scope="colgroup"></th>
                    </tr>
                    <tr>
                        <td colspan="2">
                            Name
                        </td>
                        <td colspan="2">
                            <input class="form-control form-input" name="Name1" id="Name1" value="@name1">
                        </td>
                    </tr>
                    <tr>
                        <td colspan="4">
                            <div class="form-control-wrapper">
                                <label for="Comment" class="form-label">
                                    Address
                                </label>
                                <textarea class="form-control form-textarea height-auto" id="Address1" name="Address1" rows="2" cols="64">@address1</textarea>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td class="col-1">
                            Tel
                        </td>
                        <td class="col-5">
                            <input class="form-control form-input" name="Tel1" id="Tel1" value="@tel1">
                        </td>
                        <td class="col-1">
                            Fax
                        </td>
                        <td class="col-5">
                            <input class="form-control form-input" name="Fax1" id="Fax1" value="@fax1">
                        </td>
                    </tr>
                    <tr>
                        <td colspan="2">
                            Email
                        </td>
                        <td colspan="2">
                            <input class="form-control form-input" name="Email1" id="Email1" type="email" value="@email1">
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="col-6 ps-1">
            <table>
                <tbody>
                    <tr>
                        <td colspan="2">
                            Name
                        </td>
                        <td colspan="2">
                            <input class="form-control form-input" name="Name2" id="Name2" value="@name2">
                        </td>
                    </tr>
                    <tr>
                        <td colspan="4">
                            <div class="form-control-wrapper">
                                <label for="Comment" class="form-label">
                                    Address
                                </label>
                                <textarea class="form-control form-textarea height-auto" id="Address2" name="Address2" rows="2" cols="64">@address2</textarea>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td class="col-1">
                            Tel
                        </td>
                        <td class="col-5">
                            <input class="form-control form-input" name="Tel2" id="Tel2" value="@tel2">
                        </td>
                        <td class="col-1">
                            Fax
                        </td>
                        <td class="col-5">
                            <input class="form-control form-input" name="Fax2" id="Fax2" value="@fax2">
                        </td>
                    </tr>
                    <tr>
                        <td colspan="2">
                            Email
                        </td>
                        <td colspan="2">
                            <input class="form-control form-input" name="Email2" id="Email2" type="email" value="@email2">
                        </td>
                    </tr>
                    <tr>
                        <th colspan="4" scope="colgroup"></th>
                    </tr>
                    <tr style="height: 181px">
                        <td colspan="4" rowspan="4">
                            <div class="form-control-wrapper">
                                <label for="Comment" class="form-label">
                                    Comment
                                </label>
                                <textarea class="form-control form-textarea height-auto" id="Comment2" name="Comment2" rows="7" cols="64">@referenceComment</textarea>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </section>

    <section class="d-flex mt-2">
        <div class="col-5 pe-1">
            <table>
                <thead>
                    <tr>
                        <th colspan="2">
                            SALES
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>
                            Pro Forma Attached?
                        </td>
                        <td>
                            <input type="checkbox" class="form-check-input form-control mt-0 p-0" name="ProFormaAttached" id="ProFormaAttached" checked="@proFormaAttached">
                        </td>
                    </tr>
                    <tr>
                        <td>
                            Raised by
                        </td>
                        <td>
                            <select id="RaisedBySelect" name="RaisedBySelect" data-raised-by-no="@raisedByNo" aria-label="Raised by select" class="form-control mt-0"></select>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            Date
                        </td>
                        <td>
                            <div class="d-flex gap-1">
                                <input name="SaleDate" id="SaleDate" class="form-control form-input mt-0" style="height: 28px !important" type="text" data-input-type="DATE" value="@raisedByDate">
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        <div class="col-7 ps-1">
            <table>
                <thead>
                    <tr>
                        <th colspan="6">
                            MANAGER
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>
                            SOR Signed?
                        </td>
                        <td>
                            <input type="checkbox" class="form-check-input form-control mt-0 p-0" name="SORSigned" id="SORSigned" checked="@sorSigned">
                        </td>
                        <td>
                            FOR Stock?
                        </td>
                        <td>
                            <input type="checkbox" class="form-check-input form-control mt-0 p-0" name="FORStock" id="FORStock" checked="@forStock">
                        </td>
                        <td>
                            Values Correct?
                        </td>
                        <td>
                            <input type="checkbox" class="form-check-input form-control mt-0 p-0" name="ValuesCorrect" id="ValuesCorrect" checked="@valuesCorrect">
                        </td>
                    </tr>
                    <tr>
                        <td colspan="4">
                            Authorized
                        </td>
                        <td colspan="2">
                            Date
                        </td>
                    </tr>
                    <tr>
                        <td colspan="4">
                            <div class="d-flex align-items-center justify-content-between">
                                <p class="m-0">@authorizedBy</p>
                                @if (canAuthorize)
                                {
                                    <button class="btn btn-primary">
                                        <span class="lh-base">Authorise</span>
                                    </button>
                                }
                            </div>
                        </td>
                        <td colspan="2">@authorizedDate</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </section>

    <h1 class="text-center fs-5 fw-bold my-3">
        ----- ACCOUNTS ONLY --------
    </h1>

    <section class="d-flex">
        <div class="col-4 pe-1">
            <table>
                <tr>
                    <th class="fw-normal">ERAI Member?</th>
                    <td>
                        <div class="d-flex gap-1">
                            <p class="m-0">Yes</p>
                            <input type="checkbox" class="form-check-input form-control mt-0 p-0" name="ERAIMemberYes" id="ERAIMemberYes" checked="@eraiMember">
                        </div>
                    </td>
                    <td>
                        <div class="d-flex gap-1">
                            <p class="m-0">No</p>
                            <input type="checkbox" class="form-check-input form-control mt-0 p-0" name="ERAIMemberNo" id="ERAIMemberNo" checked="@(!eraiMember)">
                        </div>
                    </td>
                </tr>
                <tr>
                    <th class="fw-normal">ERAI Reported?</th>
                    <td>
                        <div class="d-flex gap-1">
                            <p class="m-0">Yes</p>
                            <input type="checkbox" class="form-check-input form-control mt-0 p-0" name="ERAIReportedYes" id="ERAIReportedYes" checked="@eraiReported">
                        </div>
                    </td>
                    <td>
                        <div class="d-flex gap-1">
                            <p class="m-0">No</p>
                            <input type="checkbox" class="form-check-input form-control mt-0 p-0" name="ERAIReportedNo" id="ERAIReportedNo" checked="@(!eraiReported)">
                        </div>
                    </td>
                </tr>
            </table>
        </div>
        <div class="col-8 ps-1">
            <table>
                <tr>
                    <th class="fw-normal" scope="col">
                        Out-standing Debit notes on P/L?
                    </th>
                    <td>
                        <input type="checkbox" class="form-check-input form-control mt-0 p-0" name="OutStandingDebit" id="OutStandingDebit" checked="@debitNotes">
                    </td>
                    <th class="fw-normal" scope="col">Total Value</th>
                    <td>
                        <input class="form-control form-input" name="OutStandingDebitTotalValue" id="OutStandingDebitTotalValue" value="@totalValue">
                    </td>
                </tr>
                <tr>
                    <th class="fw-normal" scope="col">
                        Advance Payment on Open Orders?
                    </th>
                    <td>
                        <input type="checkbox" class="form-check-input form-control mt-0 p-0" name="AdvancePayment" id="AdvancePayment" checked="@apOpenOrder">
                    </td>
                    <th class="fw-normal" scope="col">Total Value</th>
                    <td>
                        <input class="form-control form-input" name="AdvancePaymentTotalValue" id="AdvancePaymentTotalValue" value="@totalValue1">
                    </td>
                </tr>
            </table>
        </div>
    </section>
    <section class="d-flex mt-2">
        <div class="col-4 pe-1">
            <table>
                <tr>
                    <th class="fw-normal">
                        Comments
                    </th>
                    <td>
                        <textarea class="form-control form-textarea height-auto" id="OutStandingComment" name="OutStandingComment" rows="3" cols="64">@slComments</textarea>
                    </td>
                </tr>
            </table>
        </div>
        <div class="col-8 ps-1">
            <table>
                <tr>
                    <th class="fw-normal" scope="col">
                        Sales Ledger Terms
                    </th>
                    <td colspan="3">
                        <input class="form-control form-input" name="SalesLedgerTerms" id="SalesLedgerTerms" value="@slTerms">
                    </td>
                </tr>
                <tr>
                    <th class="fw-normal" scope="col">
                        Sales Ledger Overdue?
                    </th>
                    <td>
                        <input type="checkbox" class="form-check-input form-control mt-0 p-0" name="SalesLedgerOverdue" id="SalesLedgerOverdue" checked="@slOverdue">
                    </td>
                    <th class="fw-normal" scope="col">Total Value</th>
                    <td>
                        <input class="form-control form-input" name="SalesLedgerOverdueTotalValue" id="SalesLedgerOverdueTotalValue" value="@slTotalValue">
                    </td>
                </tr>
            </table>
        </div>
    </section>
    <section class="d-flex mt-2">
        <div class="col-12">
            <table>
                <thead>
                    <tr>
                        <th>
                            Payment Authorized By
                        </th>
                        <th>
                            Countersigned (where applicable)
                        </th>
                        <th>
                            Date
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>
                            <div class="d-flex align-items-center justify-content-between">
                                <p class="m-0">@paymentAuthorizedBy</p>
                                @if (@canComplete)
                                {
                                    <button class="btn btn-primary">
                                        <span class="lh-base">Complete</span>
                                    </button>
                                }
                            </div>
                        </td>
                        <td>
                            <input class="form-control form-input" name="CounterSignedValue" id="CounterSignedValue" value="@counterSigned">
                        </td>
                        <td>@paymentAuthorizedDate</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </section>

    @if (epr is not null)
    {
        <section class="d-flex mt-2">
            <div class="col-12">
                <table>
                    <tbody>
                        <tr>
                            <th class="d-none"></th>
                        </tr>
                        <tr>
                            <td class="col-10">
                                <textarea class="form-control form-textarea height-auto" name="RejectEprReason" id="RejectEprReason" rows="2" color="20" placeholder="Enter Reason of EPR Rejection"></textarea>

                                @if (showRejectedLog)
                                {
                                    <span style="padding-top:10px">
                                        <span style="font-weight:bold">No. Of Times EPR Rejected: </span>
                                        <span style="color: red !important;font-weight:bold">@eprRejectedLog?.EPRTotalRejectCounts</span>
                                        <span>
                                            (Follow the
                                            <button style="color:#0000ff;text-decoration:underline;padding:0;margin:0"
                                                    id="epr-rejected-log-view-button"
                                                    class="link-button text-break text-wrap text-start">
                                                <span class="lh-base">logs</span>
                                            </button>
                                            for more details)
                                        </span>
                                    </span>
                                }
                            </td>
                            <td class="col-2">
                                <div class="d-flex align-items-center justify-content-center">
                                    <button style="width: 120px; height: 28px; justify-content: center"
                                            class="btn btn-primary">
                                        <span class="lh-base">Reject EPR</span>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </section>
    }

    @if (showRejectedLog)
    {
        <section class="d-flex mt-2">
            <span style="font-size: 12px;font-weight:bold">Last EPR Rejected Details:</span>
            &nbsp;
            <span style="font-size: 12px;">@(eprRejectedLog?.EPRLastRejectedDetails?.Split("||")[1])</span>
        </section>
    }

    <footer class="my-3">
        <div class="my-3">
            <div class="d-flex justify-content-center gap-2">
                <button class="btn btn-primary">
                    <span class="lh-base">Save & Send</span>
                </button>
                <button class="btn btn-primary">
                    <span class="lh-base">Save & Print</span>
                </button>
                @if (canDelete)
                {
                    <button class="btn btn-primary">
                        <span class="lh-base">Delete EPR</span>
                    </button>
                }
            </div>
        </div>
    </footer>
</div>

@section Scripts {
    @await Html.PartialAsync("_ValidationScriptsPartial")
    <script src="@_settingManager.GetCdnUrl("/js/helper/string-helper.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/helper/html-helper.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/helper/page-url-function-helper.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/widgets/drop-down.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/widgets/custom-date-picker.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/helper/form-helper.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/widgets/drop-down.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/helper/datetime-helper.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/helper/number-validation.js")" asp-append-version="true"></script>
    <environment include="Development">
        <script type="module" src="/js/modules/orders/purchase-orders/epr/purchase-orders-epr.js" asp-append-version="true"></script>
    </environment>
    <environment exclude="Development">
        <script type="module" src="/dist/js/purchase-order-epr.bundle.js" asp-append-version="true"></script>
    </environment>
}
