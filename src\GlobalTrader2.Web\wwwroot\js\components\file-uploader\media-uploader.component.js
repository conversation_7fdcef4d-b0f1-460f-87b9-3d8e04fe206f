﻿import { FileUploaderEvents } from './constants/file-uploader-events.constant.js?v=#{BuildVersion}#';
import { EventEmitter } from '../base/event-emmiter.js?v=#{BuildVersion}#';
import { FileUploaderErrorCode } from './constants/file-uploader-error.constant.js?v=#{BuildVersion}#';

export class MediaUploaderComponent extends EventEmitter {

    constructor (formId, dialogId, sectionId, options) {
        super();
        this.settings = {
            maxSizeMB: 10,
            allowedTypes: [],
            maxCount: 20,
            multiple: false,
            errorMessages: {
                multipleFiles: () => window.localizedStrings.NoMultipleFileUploadMessage,
                allowedTypes: () => window.localizedStrings.fileNotAllowedErrorMessage,
                maxSize: (size) => window.localizedStrings.fileTooLargeErrorMessage.replace('{0}', size),
                maxCount: (maxCount) => window.localizedStrings.CannotUploadMoreThanImages.replace('{0}', maxCount),
                mixed: () => 'Some files do not match the allowed types or size limits. Please check your selection.',
                zeroSize: () => `The file you have selected is invalid. Please select a file larger than 0 KB`,
                selectedAllowedTypes: () => `Allowed extension: .jpg, .jpeg, .bmp`,
                selectedMaxSize: (size) => (`Allowed Max size: {0} MB`).replace('{0}', size),

            },
            ...options
        }
        this.$form = $(`#${formId}`);
        this.$dialog = $(`#${dialogId}`);
        this.container = document.getElementById(formId);

        this.maxSizeBytes = this.settings.maxSizeMB * 1024 * 1024;
        this.allowedTypes = this.settings.allowedTypes.length ? this.settings.allowedTypes.map(type => type.toLowerCase()) : null; // Allow all if empty

        this.uploadButtonId = `${sectionId}-upload-btn`;
        this.uploadButton = document.getElementById(this.uploadButtonId);

        this.openFileBrowseButton = this.container.querySelector('#uploadBtn');


        // Get references to UI elements
        this.dropZone = this.container.querySelector(".drop-zone");
        this.dropZoneText = this.container.querySelector(".drop-zone p"); // Error will appear here
        this.fileInput = this.container.querySelector(`#FileInput`);
        this.errorMessage = this.container.querySelector(`#ErrorFileInput`);

        // File hodler prop
        this.uploadedFiles = []; //Array to hold all selected files
        this.$filesToUploadContainer = $('#files-to-upload-container');
        this.lastRenderedFileCount = 0;
        this.currentCount = 0;

        this._unbindEvents();
        this._initEvents();
    }

    _unbindEvents() {
        this.dropZone?.removeEventListener("dragover", this._onDragOver);
        this.dropZone?.removeEventListener("dragleave", this._onDragLeave);
        this.dropZone?.removeEventListener("drop", this._onDrop);

        this.fileInput?.removeEventListener("change", this._onFileInputChange);
        this.uploadButton?.removeEventListener("click", this._onUploadButtonClick);
        this.openFileBrowseButton?.removeEventListener("click", this._onOpenFileBrowseClick);

        if (typeof this.off === "function" && this._onFileChange) {
            this.off(FileUploaderEvents.FILE_CHANGE, this._onFileChange);
        }
    }

    // Initialize event listeners
    _initEvents() {
        // Drag & Drop
        this._onDragOver = (e) => {
            e.preventDefault();
            this.dropZone.classList.add("dragover");
        };

        this._onDragLeave = () => {
            this.dropZone.classList.remove("dragover");
        };

        this._onDrop = (e) => {
            e.preventDefault();
            this.dropZone.classList.remove("dragover");

            const files = e.dataTransfer.files;
            if (files.length > 1 && !this.settings.multiple) {
                this.showError({
                    code: FileUploaderErrorCode.MULTIPLE_FILES,
                    message: this.settings.errorMessages.multipleFiles()
                });
            } else if (files.length) {
                this._handleFiles(files);
            }
        };

        // File Input Change
        this._onFileInputChange = (e) => {
            this._handleFiles(e.target.files);
        };

        // Upload Button Click
        this._onUploadButtonClick = (e) => {
            e.preventDefault();
            this._handleClick();
        };

        // Open File Browse
        this._onOpenFileBrowseClick = (e) => {
            e.preventDefault();
            this.fileInput.click();
        };

        // File change logic
        this._onFileChange = (file) => {
            const fileList = Array.isArray(file) ? file : [file];

            for (const f of fileList) {
                const isDuplicate = this.uploadedFiles.some(existing =>
                    existing.name === f.name && existing.size === f.size
                );

                if (!isDuplicate) {
                    this.uploadedFiles.push(f);
                }
            }

            this.renderFilesToUpload();
            this.bindSelectedFilesToInputFile();
        };

        this.dropZone.addEventListener("dragover", this._onDragOver);
        this.dropZone.addEventListener("dragleave", this._onDragLeave);
        this.dropZone.addEventListener("drop", this._onDrop);

        this.fileInput.addEventListener("change", this._onFileInputChange);
        this.uploadButton.addEventListener("click", this._onUploadButtonClick);
        this.openFileBrowseButton.addEventListener("click", this._onOpenFileBrowseClick);

        this.on(FileUploaderEvents.FILE_CHANGE, this._onFileChange);
    }

    renderFilesToUpload() {
        for (let index = this.lastRenderedFileCount; index < this.uploadedFiles.length; index++) {
            const file = this.uploadedFiles[index];
            const fileName = file.name;
            const errorMessage = this.getMessageError(file.errorCode);

            this.$filesToUploadContainer.append(
                `
                <div class="border p-2 card card-body position-relative ${file.errorCode ? "border-danger" : ""}">
                    <div class="col-md-12">
                        <p class="card-title m-0 line-clamp-3 text-break fw-bold pb-2" data-bs-toggle="tooltip" data-bs-placement="bottom" title="${fileName}">${fileName}</p>
                    </div>

                    <div class="row g-3 align-items-center">
                        ${file.errorCode ?
                    `
                            <p class="card-text text-danger">${errorMessage}</p>
                        ` :
                    `
                            <div class="col-md-2 d-flex align-items-center">
                                <label for="@($"{Model.FormId}-caption-field")" class="mb-0">Caption</label>
                            </div>
                            <div class="col-md-10">
                                    <textarea class="form-control form-textarea height-auto" style="height:auto"
                                        id="@string.Format("{0}-caption-field", Model.FormId)" name="caption" rows="1"
                                        maxlength="250"></textarea>
                            </div>
                        `}
                    </div>

                    <button
                        tabindex="0"
                        type="button"
                        class="btn-delete-document btn-close position-absolute end-0 top-0"
                        aria-label="Delete document"
                        data-index="${index}"></button>               
                </div>
                `
            );
        }
        this.lastRenderedFileCount = this.uploadedFiles.length;

        // Bind click event to delete button (delegate to container for dynamic elements)
        this.$filesToUploadContainer.off('click', '.btn-delete-document').on('click', '.btn-delete-document', (event) => {
            const index = $(event.currentTarget).data('index');
            this.uploadedFiles.splice(index, 1);
            this.lastRenderedFileCount = 0; // Reset so all files will be re-rendered
            this.$filesToUploadContainer.empty();
            this.renderFilesToUpload();
        });
    }

    _handleClick() {
        this.trigger(FileUploaderEvents.CLICK);
    }

    _handleFiles(files) {
        if (files.length == 0)
            return false;

        const { validFiles, allValid } = this._validatedFiles(files);

        if (!validFiles || validFiles.length === 0)
            return false;

        if (allValid) this.clearError();

        this.trigger(
            FileUploaderEvents.FILE_CHANGE,
            validFiles.length > 1 ? validFiles : validFiles[0]
        );
    }

    _validatedFiles(files) {

        const getFileType = (file) => file.name.substring(file.name.lastIndexOf('.')).toLowerCase();

        // Check max count limit
        if (this.currentCount + this.uploadedFiles.length + files?.length > this.settings.maxCount) {
            this.showError({
                code: FileUploaderErrorCode.MAX_COUNT,
                message: this.settings.errorMessages.maxCount(this.settings.maxCount),
            });
            return { validFiles: [], allValid: false };
        }

        const isSingleFile = files.length === 1;

        const validateFile = (file) => {
            // Type check
            if (this.allowedTypes && !this.allowedTypes.includes(getFileType(file))) {
                return {
                    code: FileUploaderErrorCode.ALLOWED_TYPES,
                    message: `${this.settings.errorMessages.allowedTypes(this.allowedTypes)} (${file.name})`
                };
            }
            // Size check
            if (file.size > this.maxSizeBytes) {
                return {
                    code: FileUploaderErrorCode.MAX_SIZE,
                    message: `${this.settings.errorMessages.maxSize(this.settings.maxSizeMB)} (${file.name})`
                };
            }
            if (file.size === 0) {
                return {
                    code: FileUploaderErrorCode.ZERO_SIZE,
                    message: this.settings.errorMessages.zeroSize()
                };
            }
            return null;
        };

        if (isSingleFile) {
            const error = validateFile(files[0]);
            if (error) {
                this.showError(error);
                return { validFiles: [], allValid: false };
            }
            return { validFiles: [files[0]], allValid: true };
        } else {
            // Multiple files: collect all errors
            const validFiles = [];
            const errors = [];
            Array.from(files).forEach(file => {
                const error = validateFile(file);
                // If error, push to errors array, else to valid files
                if (error) {
                    errors.push({
                        errorCode: error.code,
                    })
                    file.errorCode = error.code;
                }
                validFiles.push(file);
            });
            if (errors.length > 0) {
                this.showError({
                    code: FileUploaderErrorCode.MIXED,
                    errorFiles: errors,
                    message: this.settings.errorMessages.mixed()
                });
            }
            return { validFiles, allValid: errors.length === 0 };
        }
    }

    showError(error) {
        const $summaryError = this.$dialog.find(".form-error-summary");
        const $summaryContent = this.$dialog.find("#form-error-content");
        const errorElement = this.container.querySelector(".error-message");

        this.trigger(FileUploaderEvents.ERROR, error); // Call once at the start

        if (error.code === FileUploaderErrorCode.MIXED) {
            $summaryContent.html(error.message);
            $summaryError.show();
            return;
        }

        errorElement.innerHTML = error.message;
        this.dropZone.classList.add("error-border");
        this.errorMessage.classList.remove("d-none");
    }


    setCurrentCount(currentCount) {
        this.currentCount = currentCount;
    }

    getMessageError(errorCode) {
        switch (errorCode) {
            case FileUploaderErrorCode.ALLOWED_TYPES:
                return this.settings.errorMessages.selectedAllowedTypes();
            case FileUploaderErrorCode.MAX_SIZE:
                return this.settings.errorMessages.selectedMaxSize(this.settings.maxSizeMB);
            case FileUploaderErrorCode.ZERO_SIZE:
                return this.settings.errorMessages.zeroSize();
            default:
                return "";
        }
    }

    bindSelectedFilesToInputFile() {
        const dataTransfer = new DataTransfer();
        for (const file of this.uploadedFiles) {
            if (!file.errorCode) {
                dataTransfer.items.add(file);
            }
        }
        const fileInput = this.$form.find('input[id="FileInput"]')[0];
        fileInput.files = dataTransfer.files;
    }

    resetFileUploadValues() {
        this.uploadedFiles = [];
        this.lastRenderedFileCount = 0;
        this.$filesToUploadContainer.empty();
    }

    clearError() {
        const errorElement = this.container.querySelector(".error-message");
        errorElement.innerHTML = "";
        this.dropZone.classList.remove("error-border");
        this.errorMessage.classList.add("d-none");
    }
}
