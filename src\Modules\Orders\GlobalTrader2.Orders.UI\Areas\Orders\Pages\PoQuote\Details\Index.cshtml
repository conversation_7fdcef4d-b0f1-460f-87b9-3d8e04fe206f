﻿@page

@using GlobalTrader2.Orders.UI.Areas.Orders.Pages.PoQuote.Details
@using GlobalTrader2.SharedUI.Areas.Common.Pages.Shared.Components.LiteDatatable;
@using GlobalTrader2.SharedUI.Areas.Common.Pages.Shared.Components.OrderQuickJumpMenu
@using GlobalTrader2.SharedUI.Helper
@using Microsoft.Extensions.Localization
@model IndexModel

@inject IStringLocalizer<GlobalTrader2.SharedUI.CommonResources> _commonLocalizer
@inject IViewLocalizer _localizer
@inject IStringLocalizer<GlobalTrader2.SharedUI.MessageResources> _messageLocalizer
@inject SessionManager _sessionManager
@inject SettingManager _settingManager
@{

    ViewData["Title"] = $"{@_localizer["Price Request"]} {Model.Detail.POQuoteNumber}";
    var sectionBoxClasses = HtmlHelperExtensions.GetSectionBoxClass();
    var headerClasses = HtmlHelperExtensions.GetHeaderClass();
    var contentClasses = HtmlHelperExtensions.GetContentClass();
    var accordionHeaderIconClass = HtmlHelperExtensions.GetAccordionHeaderIconClass();
}

@section HeadBlock {
    @await Html.PartialAsync("Partials/_ThirdPartyStyleSheetsPartial")
}

@section LeftSideBar {
    @await Component.InvokeAsync(nameof(OrderQuickJumpMenu))
}

<div id="price-request-details-container" class="mb-3 page-content-container">
    <div class="d-flex justify-content-between align-items-start mt-2">
        <div>
            <h2 class="page-primary-title m-0">@_localizer["Price Request"] @Model.Detail.POQuoteNumber</h2>
            <div>
                <b class="status-text me-2">@_localizer["Status"]</b> <span class="status-text me-2" id="bom-details-status">@Model.Detail.PRStatus</span>
            </div>
        </div>
        @* <span class="flex-shrink-0">
            <a class="btn btn-primary" id="add-new-hub" href="/Orders/HUBRFQ/AddNewHUBRFQ">
                <img src="~/img/icons/plus.svg" alt="Add icon" width="18" height="18" />
                <span class="lh-base">@_localizer["AddNewHUBRFQ"]</span>
            </a>
        </span> *@
    </div>
    <div class="mb-3" id="main-information-wrapper">
        <div id="main-information-box" class="@sectionBoxClasses">
            <h3 class="@headerClasses">
                <span class="@accordionHeaderIconClass"></span>
                <span class="section-box-title">@_localizer["Main Information"]</span>
                <span class="section-box-button-group gap-2 flex-wrap">
                    @* @if (Model.ShowEditBomButton)
                    {
                        <button class="btn btn-primary" id="main-information-edit-btn" title="Edit">
                            <img src="~/img/icons/plus.svg" alt="Edit" width="18" height="18" />
                            <span class="lh-base">@_commonLocalizer["Edit"]</span>
                        </button>
                    }*@
                </span>
            </h3>

            <div id="main-information-content" class="row info-container @contentClasses">
                <div class="form-error-summary d-none mb-2 align-items-center">
                    <img src="/img/icons/x-octagon.svg" alt="X-icon">
                    <p class="m-0"></p>
                </div>
                <div class="col-6">
                    <div class="row">
                        <label for="divisionName" class="col-2 form-label fw-bold">@_localizer["Division"]</label>
                        <span name="divisionName" type="div" class="col-10 text-break" data-bind-name="divisionName"></span>
                    </div>
                </div>
                <div class="col-6">
                    <div class="row">
                        <label for="notes" class="col-2 form-label fw-bold">@_localizer["Notes"]</label>
                        <span name="notes" type="div" class="col-10 text-break" data-bind-name="notes"></span>
                    </div>
                    <div class="row">
                        <label for="buyer" class="col-2 form-label fw-bold">@_localizer["Buyers"]</label>
                        <span name="buyer" type="div" class="col-10 text-break" data-bind-name="salesPersonName"></span>
                    </div>
                </div>
                <div class="text-end">
                    <i name="last-updated" data-bind-name="lastUpdatedByInText" type="div"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="mb-3" id="hurfq-lines-wrapper">
        <div id="hurfq-lines-box" class="@sectionBoxClasses">
            <h3 class="@headerClasses">
                <span class="@accordionHeaderIconClass"></span>
                <span class="section-box-title">HUBRFQ Lines</span>
                <span class="section-box-button-group gap-2 flex-wrap">
                    <button class="btn btn-danger" id="hubrfq-lines-table-delete-btn" disabled>
                        <img src="~/img/icons/trash-can.svg" alt="Delete" width="18" height="18" />
                        <span class="lh-base">@_commonLocalizer["Delete"]</span>
                    </button>
                </span>
            </h3>

            <div id="hurfq-lines-content" class="row info-container @contentClasses">
                
                <table id="hurfq-lines-table" class="table simple-table display responsive">
                    <tr>
                        <th></th>
                    </tr>
                </table>
            </div>
        </div>
    </div>

    <div class="mb-3" id="price-request-quote-wrapper">
        <div id="price-request-quote-box" class="@sectionBoxClasses">
            <h3 class="@headerClasses">
                <span class="@accordionHeaderIconClass"></span>
                <span class="section-box-title">@_localizer["Purchase Request Line"]</span>
                <span class="section-box-button-group gap-2 flex-wrap">
                    <button class="btn btn-primary" id="price-request-add-btn" disabled>
                        <img src="~/img/icons/plus.svg" alt="Add" width="18" height="18" />
                        <span class="lh-base">@_commonLocalizer["Add"]</span>
                    </button>
                    <button class="btn btn-primary" id="price-request-edit-btn" title="Edit" disabled>
                        <img src="~/img/icons/plus.svg" alt="Edit" width="18" height="18" />
                        <span class="lh-base">@_commonLocalizer["Edit"]</span>
                    </button>
                </span>
            </h3>
            <div id="price-request-quote-content" class="row info-container @contentClasses">
                <table id="price-request-quote-table" class="table simple-table display responsive">
                    <tr>
                        <th></th>
                    </tr>
                </table>
            </div>
        </div>
    </div>

    <div class="mb-3" id="uploaded-document-wrapper">  
        <div id="uploaded-document-box" class="@sectionBoxClasses">
            <h3 class="@headerClasses">
                <span class="@accordionHeaderIconClass"></span>
                <span class="section-box-title">Uploaded Documents</span>
            </h3>
        </div>
    </div>

    <div class="mb-3" id="log-wrapper">
        <div id="log-box" class="@sectionBoxClasses">
            <h3 class="@headerClasses">
                <span class="@accordionHeaderIconClass"></span>
                <span class="section-box-title">Log</span>
            </h3>

            <div id="log-content" class="row info-container @contentClasses">
                <table id="log-table" class="table simple-table display responsive">
                    <tr>
                        <th></th>
                    </tr>
                </table>
            </div>
        </div>
    </div>
</div>

<div id="hubrfq-lines-table-delete-btn-dialog" class="dialog-container" title="Delete Price Request Line" style="display: none;">
    <div class="d-flex gap-2">
        <span>@_localizer["Are you sure you would like to delete the selected"] <strong>@_localizer["Price Request"]</strong>?</span>
    </div>
</div>

<div id="add-edit-price-request-dialog" class="dialog-container d-none" title="@_localizer["Add Price Request Line Detail"]">
    <div class="dialog-description">
        <div class="d-flex justify-content-between">
            <h5 id="add-edit-price-request-title" class="fw-bold fs-14 text-uppercase">
                @_localizer["Add New Price Request Line"]
            </h5>
            <span>
                <span class="fw-bold required me-1">*</span>@_commonLocalizer["denotes a required field"]
            </span>
        </div>

        <div class="line"></div>
    </div>

    <form method="post" id="add-edit-price-request-form" class="row common-form">
        <div class="form-error-summary" style="display: none;">
            <img src="~/img/icons/x-octagon.svg" alt="X-icon" />
            <div>
                <p>@_commonLocalizer["There were some problems with your form."]</p>
                <p>@_commonLocalizer["Please check below and try again."]</p>
                <p id="hubrfq-validation-message" class='d-none'></p>
            </div>
        </div>
        @Html.AntiForgeryToken()

        <input type="number" name="PriceRequestId" id="PriceRequestId" hidden aria-label="PriceRequestId" />
        <div class="col-md-12 form-control-wrapper" >
            <label for="MPNQuotedPriceRequest" class="form-label">@_localizer["MPN Quoted"]</label>
            <p id="MPNQuotedPriceRequest" class="form-p mb-0"></p>
        </div>

        <div class="col-md-12 form-control-wrapper" id="supplier-container">
            <label for="price-request-supplier-auto-search" class="form-label">@_localizer["Supplier"]<span class="required"> *</span></label>

            <div id="price-request-supplier-auto-search-wrapper" class="position-relative">
                <input type="text" id="price-request-supplier-auto-search" class="form-control form-input"
                       data-search-input="single"
                       data-input-value-id="PriceRequestSupplierId"
                       data-api-url="/companies/auto-search"
                       data-api-key-search="keyword"
                       data-bind-name="Supplier"
                       aria-label="company-auto-search" />

                <input type="number" id="PriceRequestSupplierId" name="SupplierNo" hidden aria-label="supplier-id" data-bind-name="SupplierNo" />
            </div>
        </div>

        <div class="col-md-12 form-control-wrapper d-none" id="label-supplier-container">
            <label for="supplier-auto-search" class="form-label">@_localizer["Supplier"]</label>
            <p id="SupplierName" class="form-p"></p>
        </div>

        <div class="col-md-12 form-control-wrapper">
            <label for="CurrencyNo" class="form-label">@_localizer["Currency"]<span class="required"> *</span></label>

            <select class="form-select" aria-label="agencySO" id="currency-dropdown" data-bind-name="CurrencyNo" name="CurrencyNo">
                <option selected value="">@_commonLocalizer["Select"]...</option>
            </select>
        </div>

        <div class="col-md-12 form-control-wrapper">
            <label for="Price" class="form-label">@_localizer["Unit Price"]<span class="required"> *</span></label>
            <div class="d-flex align-items-center">
                <input type="text" class="form-control form-input" name="price" id="price"
                       data-number-force-positive="true"
                       data-number-force-positive-round-decimal-place="true"
                       data-number-value-default="0"
                       data-number-force-positive-decimal-place="5"
                       data-bind-name="price"
                       data-decimal-number-force-in-range="true"
                       min="0" max="2000000000">
                <div style="margin-left: 2px;" id="price-request-currency-code" data-bind-name="PocurrencyCode"></div>
            </div>
        </div>

        <div class="col-md-12 form-control-wrapper">
            <label for="ManufacturerName" class="form-label">@_localizer["Manufacturer Name"]</label>
            <input type="text" name="ManufacturerName" id="ManufacturerName" class="form-control form-input" data-bind-name="ManufacturerName" maxlength="100" />
        </div>

        <div class="col-md-12 form-control-wrapper">
            <label for="DateCode" class="form-label">@_localizer["Date Code"]</label>
            <input type="text" name="DateCode" id="DateCode" class="form-control form-input" data-bind-name="DateCode" maxlength="100" />
        </div>

        <div class="col-md-12 form-control-wrapper">
            <label for="PackageType" class="form-label">@_localizer["Package Type"]</label>
            <input type="text" name="PackageType" id="PackageType" class="form-control form-input" data-bind-name="PackageType" maxlength="100" />
        </div>

        <div class="col-md-12 form-control-wrapper">
            <label for="ProductType" class="form-label">@_localizer["Product Type"]</label>
            <input type="text" name="ProductType" id="ProductType" class="form-control form-input" data-bind-name="ProductType" maxlength="100" />
        </div>

        <div class="col-md-12 form-control-wrapper">
            <label for="StandardPackQuantity" class="form-label">@_localizer["Standard Pack Quantity (SPQ)"]</label>
            <input type="text" name="StandardPackQuantity" id="StandardPackQuantity" class="form-control form-input" data-bind-name="StandardPackQuantity" maxlength="10" />
        </div>

        <div class="col-md-12 form-control-wrapper">
            <label for="MinimumOrderQuantity" class="form-label">@_localizer["Minimum Order Quantity (MOQ)"]</label>
            <input type="text" name="MinimumOrderQuantity" id="MinimumOrderQuantity" class="form-control form-input" data-bind-name="MinimumOrderQuantity" maxlength="100" />
        </div>

        <div class="col-md-12 form-control-wrapper">
            <label for="LeadTimeWeeks" class="form-label">@_localizer["Lead Time (Weeks)"]</label>
            <input type="text" name="LeadTimeWeeks" id="LeadTimeWeeks" class="form-control form-input" data-bind-name="LeadTimeWeeks" maxlength="50" />
        </div>

        <div class="col-md-12 form-control-wrapper">
            <label for="RohsCompliant" class="form-label">@_localizer["Rohs (Y/N)"]</label>
            <input type="text" name="RohsCompliant" id="RohsCompliant" class="form-control form-input" data-bind-name="RohsCompliant" maxlength="30" />
        </div>

        <div class="col-md-12 form-control-wrapper">
            <label for="TotalQuantityAvailable" class="form-label">@_localizer["Total quantity of stock available"]</label>
            <input type="text" name="TotalQuantityAvailable" id="TotalQuantityAvailable" class="form-control form-input" data-bind-name="TotalQuantityAvailable" maxlength="20" />
        </div>

        <div class="col-md-12 form-control-wrapper">
            <label for="LastTimeBuyLTB" class="form-label">@_localizer["Last time buy (LTB) - (Y/N)"]</label>
            <input type="text" name="LastTimeBuyLTB" id="LastTimeBuyLTB" class="form-control form-input" data-bind-name="LastTimeBuyLTB" maxlength="100" />
        </div>

        <div class="col-md-12 form-control-wrapper">
            <label for="Notes" class="form-label">@_localizer["Notes"]</label>
            <textarea name="Notes" id="Notes" class="form-control form-textarea height-auto" maxlength="500" rows="3" data-bind-name="Notes"></textarea>
        </div>

        <div class="col-md-12 form-control-wrapper">
            <label for="FactorySealed" class="form-label">@_localizer["Factory Sealed (Y/N)"]</label>
            <input type="text" name="FactorySealed" id="FactorySealed" class="form-control form-input" data-bind-name="FactorySealed" maxlength="50" />
        </div>

        <div class="col-md-12 form-control-wrapper">
            <label for="MslLevelNo" class="form-label">@_localizer["MSL"]</label>

            <select class="form-select" id="msl-dropdown" required data-bind-name="MslLevelNo" name="MslLevelNo">
                <option value="0">@_commonLocalizer["Select"]...</option>
            </select>
        </div>
    </form>
</div>
<script>
    var stateValue = {
        id: '@Model.PoQuoteId',
    }

    const localizedTitles = {
        noData: "@_localizer["NoData"]",
        save: "@_commonLocalizer["save"]",
        cancel: "@_commonLocalizer["cancel"]",
    }

    const localize = {
        yes: "@_localizer["Yes"]",
        no: "@_localizer["No"]",
        deleteSuccess: "@_localizer["Delete price request successfully"]",
        deleteFail: "@_localizer["Delete price request failed"]"
    }
</script>
@section Scripts {
    @await Html.PartialAsync("Partials/_DataTablesScriptsPartial")
   
    <script src="@_settingManager.GetCdnUrl("/lib/jquery-validation/dist/jquery.validate.js")"></script>
    <script src="@_settingManager.GetCdnUrl("/js/helper/datetime-helper.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/helper/dropdown-helper.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/helper/datatables-detail-helper.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/helper/string-helper.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/widgets/drop-down.js")" asp-append-version="true"></script>

    <environment include="Development">
        <script src="/js/modules/orders/purchase-quote/details/purchase-quote-detail.js" type="module" asp-append-version="true"></script>
    </environment>
    <environment exclude="Development">
        <script src="/dist/js/orders-purchase-quote-detail.bundle.js" type="module" asp-append-version="true"></script>
    </environment>

    <script src="@_settingManager.GetCdnUrl("/js/widgets/common-table.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/directives/custom-input-directive.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/directives/input-directive.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/widgets/custom-date-picker.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/helper/page-url-function-helper.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/components/base/search-table-page-base.js")" asp-append-version="true"></script>
    
    <script src="@_settingManager.GetCdnUrl("/js/widgets/loading-spinner.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/helper/html-helper.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/helper/form-helper.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/helper/number-validation.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/helper/sort-helper.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/widgets/dialog-custom-events.js")" asp-append-version="true"></script>    
    <script src="@_settingManager.GetCdnUrl("/js/widgets/resize-data-table-extensions.js")" asp-append-version="true"></script>
}
