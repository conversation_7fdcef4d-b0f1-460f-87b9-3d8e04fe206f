﻿import { FileUploaderComponent } from '../file-uploader/file-uploader.component.js?v=#{BuildVersion}#'
import { DocumentTypeConstant } from "./constants/document-type.constant.js?v=#{BuildVersion}#";
import { FileUploaderEvents } from '../../components/file-uploader/constants/file-uploader-events.constant.js?v=#{BuildVersion}#';
import { loadCSS } from '../../helper/load-css.helper.js?v=#{BuildVersion}#';
import { PdfDocument } from './models/pdf-document.model.js?v=#{BuildVersion}#';
import { ExcelDocument } from './models/excel-document.model.js?v=#{BuildVersion}#';
import { ImageDocument } from './models/image-document.model.js?v=#{BuildVersion}#';
import { ApplicationSettingsService } from './service/document-list.service.js?v=#{BuildVersion}#';
import { SettingItem } from '../../config/setting-item-enum.js?v=#{BuildVersion}#';
import { UploadDocumentDialog } from './upload-dialog.component.js?v=#{BuildVersion}#';
import { RemoveDocumentDialog } from './remove-dialog-component.js?v=#{BuildVersion}#';
export class DocumentsManager {
    static DocumentTypeFactory = {
        [DocumentTypeConstant.PDF_DOCUMENT]: function (id) {
            return new PdfDocument(id);
        },
        [DocumentTypeConstant.EXCEL_DOCUMENT]: function (id) {
            return new ExcelDocument(id);
        },
        [DocumentTypeConstant.IMAGE_DOCUMENT]: function (id) {
            return new ImageDocument(id);
        },
    }
    constructor (
        {
            documentSectionComponent = {
                documentTypeId: -1,
                documentTypeName: "",
                sectionName: "",
                canAdd: false,
                canDelete: false,
            },
            id,
            uploadDialogParams = {
                dialogSelector: "",
                formSelector: "",
                allowedFileExtensions: [],
            },
            removeDocumentDialogParams = {
                dialogSelector: "",
                sectionName: "",
            },
            getEndpoint = {
                url: "",
                params: {}
            },
            isDisplayMaxSize,
            refreshCallback
        }
    ) {
        //Retrieve from constructor
        this.documentSectionComponent = {
            ...documentSectionComponent,
            canAdd: String(documentSectionComponent.canAdd).toLowerCase() === "true",
            canDelete: String(documentSectionComponent.canDelete).toLowerCase() === "true",
        };
        this.documentTypeId = parseInt(documentSectionComponent.documentTypeId);
        this.uploadDialog = null;
        this.uploadDialogParams = uploadDialogParams;
        this.removeDocumentDialog = null;
        this.removeDocumentDialogParams = removeDocumentDialogParams;
        this.fileUploader = null;
        this.id = parseInt(id);
        this.isDisplayMaxSize = isDisplayMaxSize == null ? true : isDisplayMaxSize;

        this.documentInstance = null;
        //Internal values
        this.$DocumentsSectionBox = null;
        this.documentMaxFileSizeDto = null;
        this.documentsList = [];
        this.maxDocumentCount = null;
        this.currentDocumentCount = 0;
        this.getEndpoint = getEndpoint;
        this.refreshCallback = refreshCallback;
    }

    async initialize() {
        this.$DocumentsSectionBox = $(`#${this.documentSectionComponent.sectionId}-box`);
        loadCSS('/js/components/documents-list/document-list.css');

        let createDocumentInstanceFunction = DocumentsManager.DocumentTypeFactory[this.documentTypeId];
        this.documentInstance = createDocumentInstanceFunction(this.id);

        this.setupDocumentsSectionBox();
        this.updateNoFileMessageTooltip();
        await this.initDocumentSectionAsync();
        this.initUploadDocument();
        this.initDeleteDocument();

    }

    setupDocumentsSectionBox() {
        this.$DocumentsSectionBox.section_box({
            loading: true,
            loadingContentId: `${this.documentSectionComponent.sectionId}-content`,
            onRefreshClick: async () => {
                await this.refreshDocumentsSectionBox();
            }
        });
    }

    async initDocumentSectionAsync() {
        this.$DocumentsSectionBox.section_box("option", "loading", true);

        const maxPDFFileSizeData = await this.getMaxDocumentFileSize();
        if (this.isDisplayMaxSize) {
            this.$DocumentsSectionBox.find('#document-size').text(`(${maxPDFFileSizeData.documentMB} MB)`);
        } else {
            this.$DocumentsSectionBox.find('#document-size-container').hide();
        }

        const defaultParams = { sectionName: this.documentSectionComponent.sectionName };
        const documentsListData = await this.documentInstance.getDocumentsListAsync({
            endpoint: this.getEndpoint?.endpoint,
            params: { ...defaultParams, ...this.getEndpoint?.params }
        });
        this.documentsList = documentsListData;
        this.currentDocumentCount = documentsListData?.length || 0;
        this.renderDocumentsListAsync(documentsListData)

        this.$DocumentsSectionBox.section_box("option", "loading", false);

        let maxDocumentCountResponse = await ApplicationSettingsService.getMaxDocumentCountAsync(SettingItem.List.MaxPDFDocuments);
        if (maxDocumentCountResponse.success) {
            this.maxDocumentCount = maxDocumentCountResponse.data;
        }

    }

    async getMaxDocumentFileSize() {
        const documentMaxFileSize = await GlobalTrader.ApiClient.getAsync("/documents/file-size", {
            documentType: this.documentSectionComponent.documentTypeId,
        });

        if (documentMaxFileSize.success) {
            this.documentMaxFileSizeDto = documentMaxFileSize.data;
        }
        return documentMaxFileSize?.data
    }

    renderDocumentsListAsync(documentsList) {
        let $noDataMessageDiv = this.$DocumentsSectionBox.find(`#${this.documentSectionComponent.sectionId}-no-data-message`);
        let listHtml = '';
        if (documentsList?.length > 0) {
            $noDataMessageDiv.hide();
            listHtml = documentsList
                .map(document => {
                    let tooltipMessage = GlobalTrader.StringHelper.formatAdvisoryNotes(document.caption);
                    let imgSrc = this.documentInstance.getImageOfDocument(document.fileName);
                    let tooltipImageMessage = imgSrc == this.documentInstance.noDocumentImageUrl ? this.documentInstance.noFileTooltipMessage : "";
                    return `
                      <div class="col-2">
                          <div class="card text-center file-detail">
                              <span class="text-center p-1">
                              <img tabindex="0" src="${imgSrc}" class="card-img-top file-image" alt="file"
                                   style="cursor: pointer; height: 40px !important; width: 40px;"
                                   data-document-id="${document.documentId}" title="${tooltipImageMessage}">
                                  ${this.documentSectionComponent.canDelete && !this.documentSectionComponent.isReadOnly ?
                            `
                                        <button tabindex="0" type="button" class="btn-delete-document btn-close position-absolute right-0 top-0"
                                                aria-label="Delete document" data-document-id="${document.documentId}"></button>
                                      ` :
                            ""}
                                  
                              </span>
                              <div class="card-body p-1 file-description">
                                  <p class="card-title mb-1 line-clamp-3" data-bs-toggle="tooltip" data-bs-placement="bottom" title="${tooltipMessage}">
                                      ${GlobalTrader.StringHelper.stripScriptTags(document.caption)}
                                  </p>
                                  <p class="card-text mb-1">${document.dateUploadString}</p>
                                  <p class="card-text line-clamp-2">${document.updatedByName}</p>
                              </div>
                          </div>
                      </div>
                    `
                }
                )
                .join('');
        } else {
            $noDataMessageDiv.show();
            listHtml = ``;
        }
        this.bindOffClickEvents();
        this.bindOnClickEvents();
        $(`#${this.documentSectionComponent.sectionId}-list`).html(listHtml);
    }

    bindOffClickEvents() {
        const listDocumentId = `#${this.documentSectionComponent.sectionId}-list`;
        //Onclick document image
        this.$DocumentsSectionBox.find(`${listDocumentId}`).off("click", ".file-image");
        //Delete button
        this.$DocumentsSectionBox.find(`${listDocumentId}`).off("click", ".btn-delete-document");
    }

    bindOnClickEvents() {
        const listDocumentId = `#${this.documentSectionComponent.sectionId}-list`;
        //Onclick document image
        this.$DocumentsSectionBox.find(`${listDocumentId}`).off("click keydown").on("click keydown", ".file-image", (e) => {
            if (e.type === "click" || e.key === "Enter") {
                const documentId = $(e.currentTarget).data("document-id");
                const document = this.documentsList.find(document => document.documentId === documentId);
                this.onClickDocumentImage(document.fileName, GlobalTrader.StringHelper.stripScriptTags(document.caption));
            }
        });
        //Delete button
        this.$DocumentsSectionBox.find(`${listDocumentId}`).on("click keydown", ".btn-delete-document", (e) => {
            if (e.type === "click" || e.key === "Enter") {
                e.preventDefault();
                const documentId = $(e.currentTarget).data("document-id");
                const document = this.documentsList.find(document => document.documentId === documentId);
                this.onClickDeleteDocument(documentId, document.fileName);
            }
        });
    }

    initFileUploader() {
        this.fileUploader = new FileUploaderComponent(`${this.documentSectionComponent.sectionId}-file-upload`,
            {
                maxSizeMB: this.documentMaxFileSizeDto.documentMB,
                allowedTypes: this.uploadDialog.allowedFileExtensions,
                maxCount: this.maxDocumentCount
            });
        this.fileUploader.setCurrentCount(this.currentDocumentCount);

        this.fileUploader.on(FileUploaderEvents.CLICK, () => {
            if (this.currentDocumentCount < this.maxDocumentCount) {
                this.uploadDialog.$dialog.dialog("open");
            }
            else this.fileUploader.showError({
                code: "maxCountError",
                message: window.localizedStrings.CannotUploadMoreThan.replace('{0}', `${this.maxDocumentCount}`)
            });
        });

        this.fileUploader.on(FileUploaderEvents.FILE_CHANGE, (file) => {
            this.uploadDialog.$dialog.dialog("open");
            this.uploadDialog.setFileUpload(file);
        });
    }

    bindOnSubmitUpload() {
        this.uploadDialog.on('form:submit', async () => {
            this.uploadDialog.setLoading(true);
            const response = await this.documentInstance.uploadFileAsync(this.uploadDialog.$form);
            this.uploadDialog.setLoading(false);

            if (response.success) {
                this.uploadDialog.$dialog.dialog("close");
                showToast('success', window.localizedStrings.saveChangedMessage);
                await this.refreshDocumentsSectionBox();
            }
            else if (response.message === "FileSizeZero") {
                const $formError = this.uploadDialog.$dialog.find(".form-error-summary");
                if ($formError.find('#FileSizeZero').length === 0) {
                    $formError.find('#form-error-content').append(`<p id="FileSizeZero">Failed to upload. Please try again.</p>`);
                }

                $formError.show();
            }
            else {
                return showToast("danger", response.title);
            }
        });
    }

    bindOnSubmitRemove() {
        this.removeDocumentDialog.$dialog.on("saveSuccess", async () => {
            this.removeDocumentDialog.setLoading(true);
            const queryParams = `?id=${encodeURIComponent(this.removeDocumentDialog.id)}&sectionName=${encodeURIComponent(this.removeDocumentDialog.sectionName)}&fileName=${encodeURIComponent(this.removeDocumentDialog.fileName)}`;

            const response = await this.documentInstance.deleteFileAsync(queryParams);
            this.removeDocumentDialog.setLoading(false);
            if (!response?.success) return showToast("danger", response.title);

            this.removeDocumentDialog.$dialog.dialog("close");
            showToast('success', window.localizedStrings.saveChangedMessage);
            await this.refreshDocumentsSectionBox();
        })
    }

    async onClickDocumentImage(fileName, fileCaption) {
        if (!fileName) return;
        const folderName = this.documentSectionComponent.sectionName.split("_")[0];
        await this.documentInstance.openFileAsync(fileName, folderName, fileCaption);
    }

    onClickDeleteDocument(documentId, fileName) {
        this.removeDocumentDialog.$dialog.dialog("open");
        this.removeDocumentDialog.id = documentId;
        this.removeDocumentDialog.fileName = fileName;
    }

    async refreshDocumentsSectionBox() {
        if (this.fileUploader) {
            this.fileUploader.clearError();
        }
        await this.initDocumentSectionAsync();
        if (this.uploadDialog) {
            this.uploadDialog.destroyFormValidation();
            this.uploadDialog.initFormValidation(this.documentMaxFileSizeDto.documentMB);
            if (this.documentSectionComponent.canAdd && !this.documentSectionComponent.isReadOnly) {
                this.initFileUploader();
            }
        }
        if (this.refreshCallback) this.refreshCallback();
    }

    async reloadDocumentsSectionBox(id) {
        if (!id) return;
        this.documentInstance.updateEndpointDocuments(id);
        await this.refreshDocumentsSectionBox();
    }

    initUploadDocument() {
        if (this.documentSectionComponent.canAdd && !this.documentSectionComponent.isReadOnly) {
            this.uploadDialog = new UploadDocumentDialog(this.uploadDialogParams);
            this.uploadDialog.initFormValidation(this.documentMaxFileSizeDto.documentMB);
            this.uploadDialog.bindingSectionName(this.documentSectionComponent.sectionName);
            this.bindOnSubmitUpload();
            this.initFileUploader();
        };
    }

    initDeleteDocument() {
        if (this.documentSectionComponent.canDelete && this.removeDocumentDialogParams.dialogSelector) {
            this.removeDocumentDialog = new RemoveDocumentDialog(this.removeDocumentDialogParams);
            this.bindOnSubmitRemove();
        };
    }

    reloadUploadDocumentPermission(isAllow) {
        this.documentSectionComponent.canAdd = isAllow;
        if (isAllow) {
            if (this.fileUploader) {
                $(this.fileUploader.container).show();
            }
            else {
                this.initUploadDocument();
            }
        } else {
            if (this.fileUploader) {
                $(this.fileUploader.container).hide();
            }
        }
    }

    reloadDeleteDocumentPermission(isAllow) {
        this.documentSectionComponent.canDelete = isAllow;
        if (isAllow && !this.removeDocumentDialog) {
            this.initDeleteDocument();
        }
    }
    showSectionBox(isShow = true) {
        if (isShow) {
            this.$DocumentsSectionBox.show();
        } else {
            this.$DocumentsSectionBox.hide();
        }
    }
   
    updateNoFileMessageTooltip() {
        if (!this.documentInstance || !this.documentSectionComponent.noFileTooltipMessage) return;

        this.documentInstance.updateNoFileTooltipMessage(this.documentSectionComponent.noFileTooltipMessage);
    }
}
