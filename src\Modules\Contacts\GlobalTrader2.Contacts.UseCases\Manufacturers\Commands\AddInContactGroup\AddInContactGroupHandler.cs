﻿using GlobalTrader2.Core;
using GlobalTrader2.Core.Bases;
using GlobalTrader2.Core.Domain.Entities;
using GlobalTrader2.Core.Exceptions;
using GlobalTrader2.Core.StoreName;
using MediatR;
using Microsoft.Data.SqlClient;
using System.Data;

namespace GlobalTrader2.Contacts.UseCases.Manufacturers.Commands.AddInContactGroup
{
    public class AddInContactGroupHandler : IRequestHandler<AddInContactGroupCommand, BaseResponse<int>>
    {
        private readonly IBaseRepository<Manufacturer> _manufacturerRepository;

        public AddInContactGroupHandler(IBaseRepository<Manufacturer> manufacturerRepository)
        {
            _manufacturerRepository = manufacturerRepository;
        }

        public async Task<BaseResponse<int>> Handle(AddInContactGroupCommand command, CancellationToken cancellationToken)
        {
            var commaSeperatedValue = string.Join(",", command.Data.ManufacturersIds);

            var parameters = new List<SqlParameter>
            {
               new SqlParameter("@ContactName", SqlDbType.NVarChar) { Value = command.Data.GroupName },
               new SqlParameter("@Code", SqlDbType.NVarChar) { Value = command.Data.GroupCode},
               new SqlParameter("@ContactGroupType", SqlDbType.NVarChar) { Value = command.Data.ContactGroupType},
               new SqlParameter("@CommaSeperatedValue", SqlDbType.NVarChar) { Value = commaSeperatedValue},
               new SqlParameter("@RowsAffected", SqlDbType.Int) { Direction = ParameterDirection.Output},
            };
            var rowsAffected = await _manufacturerRepository.ExecuteSqlRawAsync(@$"{StoredProcedures.Insert_ContactGroup} @ContactName,@Code,@ContactGroupType,@CommaSeperatedValue,@RowsAffected OUTPUT",
                parameters.ToArray());

            if (rowsAffected == -1) throw new ConflictDataException([
                new BaseError { ErrorMessage = "Group Name or Group Code already exists!" }
            ]);

            if (rowsAffected == 0) throw new ConflictDataException([
                new BaseError { ErrorMessage = "Error in Inserting." }
            ]);

            return new BaseResponse<int>
            {
                Success = true,
                Data = rowsAffected,
            };
        }
    }
}
