﻿import { TableFilterComponent } from '../../../../../../../../components/table-filter/table-filter.component.js?v=#{BuildVersion}#'
import { InputElement } from '../../../../../../../../components/table-filter/models/input-element.model.js?v=#{BuildVersion}#'
InputElement.prototype.createWrapper = function (id, label, requiredCheckbox) {
    return `
            <div class="col-12 form-control-wrapper">
                <div class="row g-3 align-items-center">
                    <div class="col-3">
                        <label for="${id}" class="form-label">${label}</label>
                    </div>
                    <div class="col-8">
                        <div class="d-flex gap-1" id="${id}ElementContent">
                            ${requiredCheckbox ? '<span></span> <!-- Placeholder for checkbox -->' : ''}
                        </div>
                    </div>
                </div>
            </div>
        `;
}
export class SourceFilterComponent extends TableFilterComponent {

    constructor(container, title, options) {
        super(container, title, options);

    }
    getContainer(inputConfig, isTemplate) {
        return this.container.find(`.filter-form .${inputConfig.locatedInContainerByClass}`);
    }
    generateLayout() {
        return `
            <div class="filter-section-wrapper mb-10px d-none ${this.settings.wrapperClass ?? ''}" id="${this.container.attr('id')}-content-show">
                <div class="row common-form filter-form">
                     <div class="col-6 px-0 filter-column-1 ">
                    </div>
                    <div class="col-6 px-0 filter-column-2">
                    </div>
                </div>

                <span class="d-flex gap-2 align-items-center">
                    <button class="btn btn-primary apply-table-filter">
                        <img src="/img/icons/check.svg" alt="apply" width="18" height="18">
                        <span class="lh-base">Search</span>
                    </button>
                    <button class="btn btn-danger cancel-table-filter" style="display: none;">
                        <img src="/img/icons/slash.svg" alt="cancel" width="18" height="18">
                        <span class="lh-base">Cancel</span>
                    </button>
                </span>
            </div>
        `
    }

    async setupEventListeners() {
        // Apply button event
        this.container.find(".apply-table-filter").on("click", (e) => {
            e.preventDefault();
            this.trigger("applied.mtf", { event: e });
        });

        // Cancel button event
        this.container.find(".cancel-table-filter").on("click", (e) => {
            e.preventDefault();
            this.toggleApplyCancelButtons(true);
            this.trigger("cancel.mtf", { event: e });
        });

        const inputEnter = (e) => {
            this.trigger('inputEnter.mft', e);
        }

        const inits = this.inputs.map((input) => {
            input.on('inputEnter.mfi', inputEnter);
            input.init();
            return input;
        });
        this.disableButtons(['apply']);
        this.on('controlchanged.mtf', (data) => {
            if (this.hasAnyActiveFilter()) {
                this.enableButtons(['apply']);
            } else {
                this.disableButtons(['apply']);
            }
        });

        await Promise.all(inits); // Wait for all inputs to finish init
    }

    hasAnyActiveFilter() {
        return this.inputs.some(input => input.isChecked());
    }
}