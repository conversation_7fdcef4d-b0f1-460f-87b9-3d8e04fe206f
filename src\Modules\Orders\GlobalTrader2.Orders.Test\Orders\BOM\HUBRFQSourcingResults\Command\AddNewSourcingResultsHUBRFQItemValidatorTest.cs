using FluentValidation.TestHelper;
using GlobalTrader2.Orders.UserCases.Orders.BOM.HUBRFQSourcingResults.Command;

namespace GlobalTrader2.Orders.Test.Orders.BOM.HUBRFQSourcingResults.Command
{
    public class AddNewSourcingResultsHUBRFQItemValidatorTest
    {
        private readonly AddNewSourcingResultsHUBRFQItemValidator _validator;

        public AddNewSourcingResultsHUBRFQItemValidatorTest()
        {
            _validator = new AddNewSourcingResultsHUBRFQItemValidator();
        }

        [Fact]
        public void Validator_ShouldReturnValidationFailure_WhenCustomerRequirementNoIsEmpty()
        {
            // Arrange
            var command = new AddNewSourcingResultsHUBRFQItemCommand { CustomerRequirementNo = 0 };

            // Act
            var result = _validator.TestValidate(command);

            // Assert
            result.ShouldHaveValidationErrorFor(x => x.CustomerRequirementNo);
        }

        [Fact]
        public void Validator_ShouldReturnValidationFailure_WhenPartIsEmpty()
        {
            // Arrange
            var command = new AddNewSourcingResultsHUBRFQItemCommand { Part = "" };

            // Act
            var result = _validator.TestValidate(command);

            // Assert
            result.ShouldHaveValidationErrorFor(x => x.Part);
        }

        [Fact]
        public void Validator_ShouldReturnValidationFailure_WhenPartIsNull()
        {
            // Arrange
            var command = new AddNewSourcingResultsHUBRFQItemCommand { Part = null! };

            // Act
            var result = _validator.TestValidate(command);

            // Assert
            result.ShouldHaveValidationErrorFor(x => x.Part);
        }

        [Fact]
        public void Validator_ShouldReturnValidationFailure_WhenPartExceedsMaxLength()
        {
            // Arrange
            var command = new AddNewSourcingResultsHUBRFQItemCommand 
            { 
                Part = "A".PadRight(31, 'X') // 31 characters, exceeds limit of 30
            };

            // Act
            var result = _validator.TestValidate(command);

            // Assert
            result.ShouldHaveValidationErrorFor(x => x.Part);
        }

        [Fact]
        public void Validator_ShouldReturnValidationFailure_WhenQuantityIsNegative()
        {
            // Arrange
            var command = new AddNewSourcingResultsHUBRFQItemCommand { Quantity = -1 };

            // Act
            var result = _validator.TestValidate(command);

            // Assert
            result.ShouldHaveValidationErrorFor(x => x.Quantity);
        }

        [Fact]
        public void Validator_ShouldNotReturnValidationFailure_WhenQuantityIsNull()
        {
            // Arrange
            var command = new AddNewSourcingResultsHUBRFQItemCommand { Quantity = null };

            // Act
            var result = _validator.TestValidate(command);

            // Assert
            result.ShouldNotHaveValidationErrorFor(x => x.Quantity);
        }

        [Fact]
        public void Validator_ShouldReturnValidationFailure_WhenPriceIsNegative()
        {
            // Arrange
            var command = new AddNewSourcingResultsHUBRFQItemCommand { Price = -1.0 };

            // Act
            var result = _validator.TestValidate(command);

            // Assert
            result.ShouldHaveValidationErrorFor(x => x.Price);
        }

        [Fact]
        public void Validator_ShouldReturnValidationFailure_WhenEstimatedShippingCostIsNegative()
        {
            // Arrange
            var command = new AddNewSourcingResultsHUBRFQItemCommand { EstimatedShippingCost = -1.0 };

            // Act
            var result = _validator.TestValidate(command);

            // Assert
            result.ShouldHaveValidationErrorFor(x => x.EstimatedShippingCost);
        }

        [Fact]
        public void Validator_ShouldReturnValidationFailure_WhenSupplierPriceIsNegative()
        {
            // Arrange
            var command = new AddNewSourcingResultsHUBRFQItemCommand { SuplierPrice = -1.0 };

            // Act
            var result = _validator.TestValidate(command);

            // Assert
            result.ShouldHaveValidationErrorFor(x => x.SuplierPrice);
        }


        [Fact]
        public void Validator_ShouldPass_WhenAllRequiredFieldsAreValid()
        {
            // Arrange
            var command = new AddNewSourcingResultsHUBRFQItemCommand
            {
                CustomerRequirementNo = 123,
                Part = "VALID-PART",
                Price = 10.50,
                ClientNo = 456,
                EstimatedShippingCost = 5.25,
                Quantity = 100,
                SuplierPrice = 8.75,
                TypeName = "Valid Type",
                Notes = "Valid notes",
                DateCode = "2023A",
                DeliveryDate = DateTime.Now.AddDays(30),
                OriginalEntryDate = DateTime.Now.AddDays(-1)
            };

            // Act
            var result = _validator.TestValidate(command);

            // Assert
            result.ShouldNotHaveAnyValidationErrors();
        }
    }
}
