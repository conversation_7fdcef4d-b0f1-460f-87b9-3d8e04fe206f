﻿<Project Sdk="Microsoft.NET.Sdk.Razor">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <AddRazorSupportForMvc>true</AddRazorSupportForMvc>
    <GenerateDocumentationFile>false</GenerateDocumentationFile>
	<IsPackable>false</IsPackable>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="Areas\Orders\Pages\CustomerRequirement\Details\Index.cshtml.css" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="Areas\Orders\Pages\CustomerRequirement\Details\Index.cshtml.css" />
  </ItemGroup>

  <ItemGroup>
    <FrameworkReference Include="Microsoft.AspNetCore.App" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Localization" Version="9.0.5" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\..\Shared\GlobalTrader2.SharedUI\GlobalTrader2.SharedUI.csproj" />
    <ProjectReference Include="..\..\Contacts\GlobalTrader2.Contacts.UseCases\GlobalTrader2.Contacts.UseCases.csproj" />
    <ProjectReference Include="..\GlobalTrader2.Orders.UserCases\GlobalTrader2.Orders.UserCases.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Areas\Orders\Pages\Quotes\Details\AddQuoteLine\" />
  </ItemGroup>


</Project>
