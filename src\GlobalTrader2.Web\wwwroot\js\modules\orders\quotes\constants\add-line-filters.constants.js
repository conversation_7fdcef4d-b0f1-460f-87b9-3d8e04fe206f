﻿import { FieldType } from "../../../../components/table-filter/constants/field-type.constant.js?v=#{BuildVersion}#"
import { NumberType } from "../../../../components/table-filter/constants/number-type.constant.js?v=#{BuildVersion}#"

export const SELECT_ITEM_FROM_REQUIREMENTS_FILTER_INPUTS = [
    {
        fieldType: FieldType.NUMBER,
        label: 'Requirement No',
        id: 'RequirementNo',
        name: 'RequirementNo',
        attributes: {
            "data-input-type": "numeric",
            "data-input-format": "int",
            "data-input-min": 0,
            "data-input-max": 2147483647,
            "data-input-type-allow-empty": true
        },
        extraPros: {
            numberType: NumberType.INT
        },
        value: ''
    },
    {
        fieldType: FieldType.CHECKBOX,
        label: 'Include Closed?',
        id: 'IncludeClosed',
        name: 'IncludeClosed',
        value: ''
    },
    {
        fieldType: FieldType.TEXT,
        label: 'Part No',   
        id: 'PartNo',
        name: 'PartNo',
        value: ''
    },
    {
        fieldType: FieldType.DATE,
        label: 'Date Received From',
        id: 'DateReceivedFrom',
        name: 'DateReceivedFrom',
        value: '',
        pairWith: "DateReceivedTo"
    },
    {
        fieldType: FieldType.TEXT,
        label: 'Company',
        id: 'Company',
        name: 'Company',
        value: ''
    },
    {
        fieldType: FieldType.DATE,
        label: 'Date Received To',
        id: 'DateReceivedTo',
        name: 'DateReceivedTo',
        value: ''
    },
]

export const SELECT_ITEM_FROM_REQUIREMENTS_SOURCING_FILTER_INPUTS = [
    {
        fieldType: FieldType.NUMBER,
        label: 'Requirement No',
        id: 'RequirementNo',
        name: 'RequirementNo',
        attributes: {
            "data-input-type": "numeric",
            "data-input-format": "int",
            "data-input-min": 0,
            "data-input-max": 2147483647,
            "data-input-type-allow-empty": true
        },
        extraPros: {
            numberType: NumberType.INT
        },
        value: ''
    },
    {
        fieldType: FieldType.TEXT,
        label: 'Supplier',
        id: 'Supplier',
        name: 'Supplier',
        value: ''
    },
    {
        fieldType: FieldType.TEXT,
        label: 'Part No',
        id: 'PartNo',
        name: 'PartNo',
        value: ''
    },
    {
        fieldType: FieldType.TEXT,
        label: 'Company',
        id: 'Company',
        name: 'Company',
        value: ''
    },
    {
        fieldType: FieldType.CHECKBOX,
        label: 'PoHub?',
        id: 'PoHub',
        name: 'PoHub',
        value: ''
    },
    {
        fieldType: FieldType.TEXT,
        label: 'HUBRFQ Name',
        id: 'HUBRFQName',
        name: 'HUBRFQName',
        value: ''
    },
]