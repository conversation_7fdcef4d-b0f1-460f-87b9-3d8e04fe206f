﻿SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].[usp_select_Company_DefaultPurchasingInfo] -- 206732    
--******************************************************************************************      
--* Gets the default Purchasing info for a company, checking that tax and currency are valid      
--* (through table joins)      
--*       
--* RP 12.02.2010:      
--* - new proc      
--******************************************************************************************      
    @CompanyId int      
WITH RECOMPILE AS       
    SELECT  co.CompanyId      
          , sv.ShipViaId AS DefaultPurchaseShipViaNo      
          , sv.ShipViaName AS DefaultPurchaseShipViaName      
          , isnull(sv.Cost, 0) AS DefaultPurchaseShippingCost      
          , isnull(sv.Charge, 0) AS DefaultPurchaseFreightCharge      
          , co.DefaultPurchaseShipViaAccount      
          , cu.CurrencyId AS POCurrencyNo      
          , cu.CurrencyCode AS POCurrencyCode      
          , cu.CurrencyDescription AS POCurrencyDescription      
          , tx.TaxId AS POTaxNo      
          , tx.TaxName AS POTaxName      
          , tm.TermsId AS POTermsNo      
          , tm.TermsName AS POTermsName      
          , cn.ContactId AS DefaultPOContactNo      
          , cn.ContactName AS DefaultPOContactName      
    ,      cu.GlobalCurrencyNo      
  ,    co.POCurrencyNo    
  , co.UPLiftPrice    
  ,(isnull(country.ShippingCost,0)) as ESTShippingCost  -- to get EST Shipping Cost  
  , ISNULL(ct.NonPreferredCompany,0) as NonPreferredCompany 
  ,	ISNULL(co.SupplierWarranty,0) as SupplierWarranty   
    FROM    tbCompany co      
    LEFT JOIN dbo.tbCurrency cu ON co.POCurrencyNo = cu.CurrencyId      
                                   AND cu.Buy = 1      
                                   AND NOT cu.Inactive = 1      
    LEFT JOIN dbo.tbShipVia sv ON co.DefaultPurchaseShipViaNo = sv.ShipViaId      
    LEFT JOIN dbo.tbTax tx ON co.POTaxNo = tx.TaxId      
                              AND NOT tx.Inactive = 1      
    LEFT JOIN dbo.tbTerms tm ON co.POTermsNo = tm.TermsId      
                                AND tm.Buy = 1      
                                AND NOT tm.Inactive = 1      
    LEFT JOIN dbo.tbContact cn ON co.DefaultPOContactNo = cn.ContactId      
    LEFT JOIN dbo.tbCountry country on country.CountryId = co.DefaultPOShipCountryNo   AND co.ClientNo=country.ClientNo  --  to get EST Shipping Cost                            AND NOT cn.Inactive = 1      
    LEFT JOIN tbCompanyType ct on co.TypeNo = ct.CompanyTypeId    
    WHERE   CompanyId = @CompanyId 
