using GlobalTrader2.Core.Constants;

namespace GlobalTrader2.Orders.UserCases.Orders.SOLine.Commands.EditSalesOrderLine
{
    public class EditAllSalesOrderLinesHandler(IBaseRepository<AffectedRows> _repository) : IRequestHandler<EditAllSalesOrderLinesCommand, BaseResponse<int>>
    {
        public async Task<BaseResponse<int>> Handle(EditAllSalesOrderLinesCommand request, CancellationToken cancellationToken)
        {
            var parameters = new List<SqlParameter>()
            {
                new("@SalesOrderNo", SqlDbType.Int) { Value = request.SalesOrderNo },
                new("@DatePromised", SqlDbType.DateTime) { Value = request.DatePromised },
                new("@UpdatedBy", SqlDbType.Int) { Value = request.UpdatedBy },
                new("@IsFormChanged", SqlDbType.Bit) { Value = false },
                new("@IsReasonChanged", SqlDbType.Bit) { Value = request.IsReasonChanged },
                new("@PromiseReasonNo", SqlDbType.Int) { Value = request.PromiseReasonNo},
                new("@RowsAffected", SqlDbType.Int) { Direction = ParameterDirection.Output }

            };
           
            var result = await _repository.ExecuteSqlRawAsync(
                sql: $"{StoredProcedures.Update_AllSalesOrderLines} @SalesOrderNo, @DatePromised, @UpdatedBy, @IsFormChanged, @IsReasonChanged, @PromiseReasonNo, @RowsAffected OUTPUT",
                parameters: [.. parameters]
            );
           
            return new BaseResponse<int> { Data = result, Success = true };
        }
    }
}