namespace GlobalTrader2.Dto.SalesOrderLine
{
    public class GetQuoteLineDto
    {
        public string? Part { get; set; }
        public string? CustomerPart { get; set; }
        public string? DateCode { get; set; }
        public string? Price { get; set; }
        public int? Quantity { get; set; }
        public string? ManufacturerName { get; set; }
        public int? ManufacturerNo { get; set; }
        public int? ProductNo { get; set; }
        public string? ProductDescription { get; set; }
        public int? ProductSource { get; set; }
        public int? PackageNo { get; set; }
        public string? PackageDescription { get; set; }
        public DateTime? PODelDate { get; set; }
        public bool? IsIPO { get; set; }
        public bool? IsIPOExist { get; set; }
        public int? Rohs { get; set; }
        public string? LineNotes { get; set; }
        public string? ShippingInstructions { get; set; }
        public string? Msl { get; set; }
        public bool? AS6081 { get; set; }
    }
}