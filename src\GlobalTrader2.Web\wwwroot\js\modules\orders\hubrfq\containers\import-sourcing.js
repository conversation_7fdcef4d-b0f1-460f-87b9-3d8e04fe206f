﻿import { FileUploaderEvents } from "../../../../components/file-uploader/constants/file-uploader-events.constant.js?v=#{BuildVersion}#";
import { FileUploaderComponent } from "../../../../components/file-uploader/file-uploader.component.js?v=#{BuildVersion}#";
import { ResizableBoxComponent } from "../../../../components/resizable-box/resizable-box.component.js?v=#{BuildVersion}#";
import { ButtonHelper } from "../../../../helper/button-helper.js?v=#{BuildVersion}#";
import { DocumentTypeConstant } from "../../../../components/documents-list/constants/document-type.constant.js?v=#{BuildVersion}#";
import { ManufactureSearchSelectComponent } from "../../requirements/components/search-selects/manufacture-search-select.component.js?v=#{BuildVersion}#";
import { SearchSelectComponent } from "../../../../components/search-select/search-select.component.js?v=#{BuildVersion}#";
const detailDialogId = $('#hubrfq-import-sourcing-result-dialog');
const resetDialogId = $('#reset-import-sourcing-dialog');
const unsaveDialogId = $('#unsave-warning-dialog');
const yes = window.localizedStrings.yes;
const no = window.localizedStrings.no;
const unexpectedError = window.localizedStrings.unexpectedError;

const state = {
    bomNo: null,
    showMismatchedDataOnly: true,
    currentPageNumber: 1,
    currentPageSize: 20,
    rawDataTable: null,
    currentRawDataSource: null,
    resizableBox: null,
    fileUploader: null,
    supplierSearchSelect: null,
    manufacturerSearchSelect: null
};

const uploadHandlerConfig = (file) => {
    const timestamp = new Date().getTime();
    const fileNameWithTimestamp = `${timestamp}_${file.name}`;
    
    const customFormData = new FormData();
    customFormData.append('file', file);
    customFormData.append('fileName', fileNameWithTimestamp);

    return {
        url: '/api/documents/upload/sourcing-results-import-file',
        method: 'POST',
        headers: {
            "RequestVerificationToken": $('input[name="__RequestVerificationToken"]').val()
        },
        formData: customFormData,
        onload: (responseText, xhr) => {
            const response = JSON.parse(responseText);
            fileUploadedSuccess(response.data);
        },
        onerror: (xhr) => {
            let errorMessage = window.localizedStrings.unexpectedError;
            if (xhr.status == 403) {
                errorMessage = window.localizedStrings.unauthorized;
            }
            showToast('danger', errorMessage);
        }
    };
};

$(() => {
    state.bomNo = stateValue.id;
    setupDialogs();
    initFileUpload();
    initResizableBox();
    initSearchSelects();
    setupEventListener();
    $(`.ui-dialog-buttonpane .ui-dialog-buttonset button:contains('${yes}')`).addClass("btn btn-primary fw-normal").html(`<img src="/img/icons/check.svg" alt="${yes}"> ${yes}`);
    $(`.ui-dialog-buttonpane .ui-dialog-buttonset button:contains('${no}')`).addClass("btn btn-danger fw-normal").html(`<img src="/img/icons/xmark.svg" alt="${no}"> ${no}`);

})

function initDataTable() {
    state.rawDataTable = $('#hubrfq-import-sourcing-raw-data-table').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: `/api/orders/bom/import-sourcing-results/imported-raw-data`,
            type: 'GET',
            data: (data) => {
                return {
                    bomNo: state.bomNo,
                    showMissmatchOnly: state.showMismatchedDataOnly,
                    draw: data.draw,
                    pageNumber: (data.start / data.length) + 1,
                    pageSize: data.length,
                };
            },
            dataFilter: function (data) {
                const json = JSON.parse(data);
                loadDropdownComponent($("#hubrfq-import-sourcing-supplier-incorrect-dropdown"), json.data.incorrectDataList, "SUPPLIER");
                loadDropdownComponent($("#hubrfq-import-sourcing-manufacturer-incorrect-dropdown"), json.data.incorrectDataList, "MFR");
                state.manufacturerSearchSelect.resetSearchSelect(false);
                state.supplierSearchSelect.resetSearchSelect(false);
                $('#hubrfq-import-import-btn').prop('disabled', json.data?.totalRecordsError > 0);

                json.recordsTotal = state.showMismatchedDataOnly ? json.data.totalRecordsError : json.data.totalRecords;
                json.data = json.data?.rawDataList || [];

                state.currentRawDataSource = json.data;

                return JSON.stringify(json);
            },
        },
        lengthMenu: [20, 50, 100, 200],
        pageLength: 20,
        language: {
            emptyTable: `<i>${window.localizedStrings.noDataFound}</i>`,
            zeroRecords: `<i>${window.localizedStrings.noDataFound}</i>`,
        },
        scrollX: true,
        autoWidth: true,
        paging: true,
        info: true,
        searching: false,
        layout: {
            topStart: null,
            bottomStart: ['info', 'pageLength'],
            bottomEnd: 'paging'
        },
        responsive: false,
        colReorder: true,
        select: false,
        sort: false,
        scrollCollapse: true,
        rowId: 'bomImportSourcingId',
        columns: [
            {
                title: "bomImportSourcingId",
                width: "0%",
                data: 'bomImportSourcingId',
                visible: false
            },
            {
                title: renderEditableColumnHeader('Requirement'),
                width: "125",
                data: 'requirement',
                type: 'string',
                render: (data, type, row) => {
                    const isInvalid = row.requirementMessage != "";

                    return `
                        <div class="d-flex align-items-center gap-1">
                            ${isInvalid ? ButtonHelper.createAdvisoryNotesIcon(row.requirementMessage) : ''}
                            <input id="requirement-${row.bomImportSourcingId}" data-field="requirement" type="text" class="form-control form-input border border-0 bg-transparent ${isInvalid ? 'text-danger' : ''}" value="${data}">
                        </div>
                    `;
                }
            },
            {
                title: renderEditableColumnHeader('Customer Ref No.'),
                width: "130",
                data: 'customerRefNo',
                render: (data, type, row) => {
                    const isInvalid = row.customerRefNoMessage != "";

                    return `
                        <div class="d-flex align-items-center gap-1">
                            ${isInvalid ? ButtonHelper.createAdvisoryNotesIcon(row.customerRefNoMessage) : ''}
                            <input id="customerRefNo-${row.bomImportSourcingId}" data-field="customerRefNo" type="text" class="form-control form-input border border-0 bg-transparent ${isInvalid ? 'text-danger' : ''}" value="${data}">
                        </div>
                    `;
                }
            },
            {
                title: 'Supplier',
                width: "200",
                data: 'supplier',
                createdCell: function (td) {
                    td.classList.add('align-middle');
                    td.classList.add('cursor-default');
                },
                render: (data, type, row) => {
                    const isInvalid = row.supplierMessage != "";

                    return `
                        <div class="d-flex align-items-center gap-1 h-100">
                            ${isInvalid ? ButtonHelper.createAdvisoryNotesIcon(row.supplierMessage) : ''}
                            <span class="bg-transparent ${isInvalid ? 'text-danger' : ''}">${data}</span>
                        </div>
                    `;
                }
            },
            {
                title: renderEditableColumnHeader('Supplier Part No.'),
                data: 'supplierPart',
                width: "150",
                render: (data, type, row) => {
                    const isInvalid = row.supplierPartMessage != "";

                    return `
                        <div class="d-flex align-items-center gap-1">
                            ${isInvalid ? ButtonHelper.createAdvisoryNotesIcon(row.supplierPartMessage) : ''}
                            <input id="supplierPart-${row.bomImportSourcingId}" data-field="supplierPart" type="text" class="form-control form-input border border-0 bg-transparent ${isInvalid ? 'text-danger' : ''}" value="${data}">
                        </div>
                    `;
                }
            },
            {
                title: renderEditableColumnHeader('Supplier Cost'),
                width: "110",
                data: 'supplierCost',
                render: (data, type, row) => {
                    const isInvalid = row.supplierCostMessage != "";

                    return `
                        <div class="d-flex align-items-center gap-1">
                            ${isInvalid ? ButtonHelper.createAdvisoryNotesIcon(row.supplierCostMessage) : ''}
                            <input id="supplierCost-${row.bomImportSourcingId}" data-field="supplierCost" type="text" class="form-control form-input border border-0 bg-transparent ${isInvalid ? 'text-danger' : ''}" value="${data}">
                        </div>
                    `;
                }
            },
            {
                title: renderEditableColumnHeader('RoHS'),
                width: "70",
                data: 'rohs',
                render: (data, type, row) => {
                    const isInvalid = row.rohsMessage != "";

                    return `
                        <div class="d-flex align-items-center gap-1">
                            ${isInvalid ? ButtonHelper.createAdvisoryNotesIcon(row.rohsMessage) : ''}
                            <input id="rohs-${row.bomImportSourcingId}" type="text" data-field="rohs" class="form-control form-input border border-0 bg-transparent ${isInvalid ? 'text-danger' : ''}" value="${data}">
                        </div>
                    `;
                }
            },
            {
                title: 'MFR',
                width: "200",
                data: 'mfr',
                createdCell: function (td) {
                    td.classList.add('align-middle');
                    td.classList.add('cursor-default');
                },
                render: (data, type, row) => {
                    const isInvalid = row.mfrMessage != "";

                    return `
                        <div class="d-flex align-items-center gap-1 h-100">
                            ${isInvalid ? ButtonHelper.createAdvisoryNotesIcon(row.mfrMessage) : ''}
                            <span class="bg-transparent ${isInvalid ? 'text-danger' : ''}">${data}</span>
                        </div>
                    `;
                }
            },
            {
                title: renderEditableColumnHeader('Date Code'),
                width: "100",
                data: 'dateCode',
                render: (data, type, row) => {
                    const isInvalid = row.dateCodeMessage != "";

                    return `
                        <div class="d-flex align-items-center gap-1">
                            ${isInvalid ? ButtonHelper.createAdvisoryNotesIcon(row.dateCodeMessage) : ''}
                            <input id="dateCode-${row.bomImportSourcingId}" data-field="dateCode" type="text" class="form-control form-input border border-0 bg-transparent ${isInvalid ? 'text-danger' : ''}" value="${data}">
                        </div>
                    `;
                }
            },
            {
                title: renderEditableColumnHeader('Package'),
                width: "80",
                data: 'package',
                render: (data, type, row) => {
                    const isInvalid = row.packageMessage != "";

                    return `
                        <div class="d-flex align-items-center gap-1">
                            ${isInvalid ? ButtonHelper.createAdvisoryNotesIcon(row.packageMessage) : ''}
                            <input id="package-${row.bomImportSourcingId}" data-field="package" type="text" class="form-control form-input border border-0 bg-transparent ${isInvalid ? 'text-danger' : ''}" value="${data}">
                        </div>
                    `;
                }
            },
            {
                title: renderEditableColumnHeader('Offered Qty.'),
                width: "120",
                data: 'offeredQuantity',
                render: (data, type, row) => {
                    const isInvalid = row.offeredQuantityMessage != "";

                    return `
                        <div class="d-flex align-items-center gap-1" >
                            ${isInvalid ? ButtonHelper.createAdvisoryNotesIcon(row.offeredQuantityMessage) : ''}
                            <input id="offeredQuantity-${row.bomImportSourcingId}" data-field="offeredQuantity" type="text" class="form-control form-input border border-0 bg-transparent ${isInvalid ? 'text-danger' : ''}" value="${data}">
                        </div>
                    `;
                }
            },
            {
                title: renderEditableColumnHeader('Offer Status'),
                width: "120",
                data: 'offerStatus',
                render: (data, type, row) => {
                    const isInvalid = row.offerStatusMessage != "";

                    return `
                        <div class="d-flex align-items-center gap-1" >
                            ${isInvalid ? ButtonHelper.createAdvisoryNotesIcon(row.offerStatusMessage) : ''}
                            <input id="offerStatus-${row.bomImportSourcingId}" data-field="offerStatus" type="text" class="form-control form-input border border-0 bg-transparent ${isInvalid ? 'text-danger' : ''}" value="${data}">
                        </div>
                    `;
                }
            },
            {
                title: renderEditableColumnHeader('SPQ'),
                width: "100",
                data: 'spq',
                render: (data, type, row) => {
                    const isInvalid = row.spqMessage != "";

                    return `
                        <div class="d-flex align-items-center gap-1">
                            ${isInvalid ? ButtonHelper.createAdvisoryNotesIcon(row.spqMessage) : ''}
                            <input id="spq-${row.bomImportSourcingId}" data-field="spq" type="text" class="form-control form-input border border-0 bg-transparent ${isInvalid ? 'text-danger' : ''}" value="${data}">
                        </div>
                    `;
                }
            },
            {
                title: renderEditableColumnHeader('Factory Sealed'),
                width: "120",
                data: 'factorySealed',
                render: (data, type, row) => {
                    const isInvalid = row.factorySealedMessage != "";

                    return `
                        <div class="d-flex align-items-center gap-1" >
                            ${isInvalid ? ButtonHelper.createAdvisoryNotesIcon(row.factorySealedMessage) : ''}
                            <input id="factorySealed-${row.bomImportSourcingId}" data-field="factorySealed" type="text" class="form-control form-input border border-0 bg-transparent ${isInvalid ? 'text-danger' : ''}" value="${data}">
                        </div>
                    `;
                }
            },
            {
                title: renderEditableColumnHeader('Qty in Stock'),
                width: "120",
                data: 'qtyInStock',
                render: (data, type, row) => {
                    const isInvalid = row.qtyInStockMessage != "";

                    return `
                        <div class="d-flex align-items-center gap-1">
                            ${isInvalid ? ButtonHelper.createAdvisoryNotesIcon(row.qtyInStockMessage) : ''}
                            <input id="qtyInStock-${row.bomImportSourcingId}" data-field="qtyInStock" type="text" class="form-control form-input border border-0 bg-transparent ${isInvalid ? 'text-danger' : ''}" value="${data}">
                        </div>
                    `;
                }
            },
            {
                title: renderEditableColumnHeader('MOQ'),
                width: "60",
                data: 'moq',
                render: (data, type, row) => {
                    const isInvalid = row.moqMessage != "";

                    return `
                        <div class="d-flex align-items-center gap-1">
                            ${isInvalid ? ButtonHelper.createAdvisoryNotesIcon(row.moqMessage) : ''}
                            <input id="moq-${row.bomImportSourcingId}" data-field="moq" type="text" class="form-control form-input border border-0 bg-transparent ${isInvalid ? 'text-danger' : ''}" value="${data}">
                        </div>
                    `;
                }
            },
            {
                title: renderEditableColumnHeader('LTB'),
                width: "60",
                data: 'lastTimeBuy',
                render: (data, type, row) => {
                    const isInvalid = row.lastTimeBuyMessage != "";

                    return `
                        <div class="d-flex align-items-center gap-1">
                            ${isInvalid ? ButtonHelper.createAdvisoryNotesIcon(row.lastTimeBuyMessage) : ''}
                            <input id="lastTimeBuy-${row.bomImportSourcingId}" data-field="lastTimeBuy" type="text" class="form-control form-input border border-0 bg-transparent ${isInvalid ? 'text-danger' : ''}" value="${data}">
                        </div>
                    `;
                }
            },
            {
                title: renderEditableColumnHeader('Currency'),
                width: "90",
                data: 'currency',
                render: (data, type, row) => {
                    const isInvalid = row.currencyMessage != "";

                    return `
                        <div class="d-flex align-items-center gap-1">
                            ${isInvalid ? ButtonHelper.createAdvisoryNotesIcon(row.currencyMessage) : ''}
                            <input id="currency-${row.bomImportSourcingId}" data-field="currency" type="text" class="form-control form-input border border-0 bg-transparent ${isInvalid ? 'text-danger' : ''}" value="${data}">
                        </div>
                    `;
                }
            },
            {
                title: renderEditableColumnHeader('Buy Price'),
                width: "100",
                data: 'buyPrice',
                render: (data, type, row) => {
                    const isInvalid = row.buyPriceMessage != "";

                    return `
                        <div class="d-flex align-items-center gap-1">
                            ${isInvalid ? ButtonHelper.createAdvisoryNotesIcon(row.buyPriceMessage) : ''}
                            <input id="buyPrice-${row.bomImportSourcingId}" data-field="buyPrice" type="text" class="form-control form-input border border-0 bg-transparent ${isInvalid ? 'text-danger' : ''}" value="${data}">
                        </div>
                    `;
                }
            },
            {
                title: renderEditableColumnHeader('Sell Price'),
                width: "100",
                data: 'sellPrice',
                render: (data, type, row) => {
                    const isInvalid = row.sellPriceMessage != "";

                    return `
                        <div class="d-flex align-items-center gap-1">
                            ${isInvalid ? ButtonHelper.createAdvisoryNotesIcon(row.sellPriceMessage) : ''}
                            <input id="sellPrice-${row.bomImportSourcingId}" data-field="sellPrice" type="text" class="form-control form-input border border-0 bg-transparent ${isInvalid ? 'text-danger' : ''}" value="${data}">
                        </div>
                    `;
                }
            },
            {
                title: renderEditableColumnHeader('Shipping Cost'),
                width: "110",
                data: 'shippingCost',
                render: (data, type, row) => {
                    const isInvalid = row.shippingCostMessage != "";

                    return `
                        <div class="d-flex align-items-center gap-1">
                            ${isInvalid ? ButtonHelper.createAdvisoryNotesIcon(row.shippingCostMessage) : ''}
                            <input id="shippingCost-${row.bomImportSourcingId}" data-field="shippingCost" type="text" class="form-control form-input border border-0 bg-transparent ${isInvalid ? 'text-danger' : ''}" value="${data}">
                        </div>
                    `;
                }
            },
            {
                title: renderEditableColumnHeader('Lead Time'),
                width: "100",
                data: 'leadTime',
                render: (data, type, row) => {
                    const isInvalid = row.leadTimeMessage != "";

                    return `
                        <div class="d-flex align-items-center gap-1">
                            ${isInvalid ? ButtonHelper.createAdvisoryNotesIcon(row.leadTimeMessage) : ''}
                            <input id="leadTime-${row.bomImportSourcingId}" data-field="leadTime" type="text" class="form-control form-input border border-0 bg-transparent ${isInvalid ? 'text-danger' : ''}" value="${data}">
                        </div>
                    `;
                }
            },
            {
                title: renderEditableColumnHeader('Region'),
                width: "80",
                data: 'region',
                render: (data, type, row) => {
                    const isInvalid = row.regionMessage != "";

                    return `
                        <div class="d-flex align-items-center gap-1">
                            ${isInvalid ? ButtonHelper.createAdvisoryNotesIcon(row.regionMessage) : ''}
                            <input id="region-${row.bomImportSourcingId}" data-field="region" type="text" class="form-control form-input border border-0 bg-transparent ${isInvalid ? 'text-danger' : ''}" value="${data}">
                        </div>
                    `;
                }
            },
            {
                title: renderEditableColumnHeader('Delivery Date'),
                width: "120",
                data: 'deliveryDate',
                render: (data, type, row) => {
                    const isInvalid = row.deliveryDateMessage != "";

                    return `
                        <div class="d-flex align-items-center gap-1">
                            ${isInvalid ? ButtonHelper.createAdvisoryNotesIcon(row.deliveryDateMessage) : ''}
                            <input id="deliveryDate-${row.bomImportSourcingId}" data-field="deliveryDate" type="text" class="form-control form-input border border-0 bg-transparent ${isInvalid ? 'text-danger' : ''}" value="${data}">
                        </div>
                    `;
                }
            },
            {
                title: renderEditableColumnHeader('MSL'),
                width: "80",
                data: 'msl',
                render: (data, type, row) => {
                    const isInvalid = row.mslMessage != "";

                    return `
                        <div class="d-flex align-items-center gap-1">
                            ${isInvalid ? ButtonHelper.createAdvisoryNotesIcon(row.mslMessage) : ''}
                            <input id="msl-${row.bomImportSourcingId}" data-field="msl" type="text" class="form-control form-input border border-0 bg-transparent ${isInvalid ? 'text-danger' : ''}" value="${data}">
                        </div>
                    `;
                }
            },
            {
                title: renderEditableColumnHeader('Notes'),
                width: "300",
                data: 'notes',
                render: (data, type, row) => {
                    const isInvalid = row.notesMessage != "";

                    return `
                        <div class="d-flex align-items-center gap-1">
                            ${isInvalid ? ButtonHelper.createAdvisoryNotesIcon(row.notesMessage) : ''}
                            <input id="notes-${row.bomImportSourcingId}" data-field="notes" type="text" class="form-control form-input border border-0 bg-transparent ${isInvalid ? 'text-danger' : ''}" value="${data}">
                        </div>
                    `;
                }
            },
        ]
    });
    state.rawDataTable.columns.adjust().draw();

    $('#hubrfq-import-sourcing-raw-data-table tbody').on("change", "input.form-input", function () {
        const $input = $(this);
        const field = $input.data('field');
        const value = $input.val();

        const $row = $input.closest('tr');
        const rowData = state.rawDataTable.row($row).data();
        const rowIndex = state.currentRawDataSource.findIndex(item => item.bomImportSourcingId == rowData.bomImportSourcingId);
        const $cell = $input.closest('td');

        if (rowIndex !== -1 && rowData[field] != value) {
            $cell.addClass('dirty-cell');
            state.currentRawDataSource[rowIndex][field] = value;
        }
        else {
            $cell.removeClass('dirty-cell');
        }

        $("#hubrfq-import-save-check-btn").prop('disabled', !hasUnsavedChanges());
    });
}

function renderEditableColumnHeader(columnTitle) {
    return `${columnTitle} <img src="/img/icons/edit-personal.svg" class="column-header-icon" />`
}

function initSearchSelects(){
    state.manufacturerSearchSelect = new ManufactureSearchSelectComponent('hubrfq-import-sourcing-manufacturer-auto-search', 'hubrfq-import-sourcing-manufacturer', 'single', 'keyword', '/manufacturers/auto-search', 1);
    state.supplierSearchSelect = new SearchSelectComponent('hubrfq-import-sourcing-supplier-auto-search', 'hubrfq-import-sourcing-supplier', 'single', 'keyword', 'contact/manufacturers/suppliers/search-company', 1);
}

function loadDropdownComponent(dropdownComponent, incorrectDataList, type) {
    const dataList = incorrectDataList.filter(data => data.type == type);
    dropdownComponent.empty();

    // Add default option with value 0
    const totalIncorrectManufactures = dataList.reduce((sum, item) => sum + item.count, 0);
    dropdownComponent.append(
        $('<option>', {
            value: 0,
            text: '< Select >',
            'data-count': totalIncorrectManufactures,
            selected: true,
        })
    );
    dropdownComponent.trigger("change");

    // Add options
    dataList.forEach((item) => {
        dropdownComponent.append(
            $('<option>', {
                value: item.value,
                text: item.value,
                'data-count': item.count
            })
        );
    });
}

function initResizableBox() {
    state.resizableBox = new ResizableBoxComponent('hubrfq-import-sourcing-dynamic-box');

    state.resizableBox.on('onToggleFullScreen.mrb', (isFullScreen) => {
        if (state.rawDataTable) {
            state.rawDataTable.columns.adjust().draw();
        }

        if (!isFullScreen) {
            setWidthResizeBox();
        }
    });

    state.resizableBox.on('onToggleRefresh.mrb', async () => {
        if(hasUnsavedChanges() && !await confirmUnsaveWarningDialogAsync()) {
            $(this).prop('checked', !$(this).is(':checked'));
            return false;
        }
        reloadDataTable();
    });
}

async function initFileUpload() {
    const excelDocumentMaxSize = await GlobalTrader.ApiClient.getAsync("/documents/file-size", {
        documentType: DocumentTypeConstant.EXCEL_DOCUMENT,
    });

    state.fileUploader = new FileUploaderComponent('hubrfq-import-sourcing-csv-file-uploader', {
        maxSizeMB: excelDocumentMaxSize.data.documentMB,
        allowedTypes: ['.xlsx', '.xls', '.csv'],
        multiple: false
    });

    state.fileUploader.on(FileUploaderEvents.CLICK, () => {
        state.fileUploader.browserFile();
    });

    state.fileUploader.on(FileUploaderEvents.FILE_CHANGE, (file) => {
        state.fileUploader.startUploadWithXHR(file, uploadHandlerConfig(file));
    });
}

function setupDialogs() {
    detailDialogId.dialog({
        autoOpen: false,
        maxHeight: $(window).height(),
        width: '85vw',
        modal: true,
        draggable: false,
        buttons: [
            {
                text: window.localizedStrings.cancel,
                class: 'btn btn-danger fw-normal',
                html: `<img src="/img/icons/slash.svg" alt="${window.localizedStrings.cancel}">${window.localizedStrings.cancel}`,
                click: function () {
                    resetForm();
                    detailDialogId.dialog("close");
                },
            },
        ],
        open: (event, ui) => {
            $('.ui-dialog-titlebar-close').css('display', 'none');
            $('.ui-menu').addClass('ui-menu-custom');
            detailDialogId.removeClass('d-none');

            // Match the dynamic box width to dialog content width
            setWidthResizeBox();

            // Adjust DataTable columns after resize
            if (state.rawDataTable) {
                setTimeout(() => {
                    state.rawDataTable.columns.adjust().draw();
                }, 50);
            }
        },
        close: function () {
            state.fileUploader.clearError();
        }
    });

    resetDialogId.dialog({
        autoOpen: false,
        height: "auto",
        width: 350,
        modal: true,
        draggable: false,
        buttons: [
            {
                text: "Yes",
                click: function () {
                    resetForm();
                    $(this).dialog("close");
                }
            },
            {
                text: "No",
                click: function () {
                    $(this).dialog("close");
                }
            }
        ],
        open: function (event, ui) {
            $('.ui-dialog-titlebar-close').css('display', 'none');
        }
    });

    unsaveDialogId.dialog({
        autoOpen: false,
        height: "auto",
        width: 350,
        modal: true,
        draggable: false,
        buttons: [
            {
                text: "Yes",
                click: function () {
                    $(this).dialog("close");
                    if (typeof window._unsaveDialogResolve === "function") {
                        window._unsaveDialogResolve(true);
                        window._unsaveDialogResolve = null;
                    }
                }
            },
            {
                text: "No",
                click: function () {
                    $(this).dialog("close");
                    if (typeof window._unsaveDialogResolve === "function") {
                        window._unsaveDialogResolve(false);
                        window._unsaveDialogResolve = null;
                    }
                }
            }
        ],
        open: function (event, ui) {
            $('.ui-dialog-titlebar-close').css('display', 'none');
        }
    });
}

function setWidthResizeBox() {
    const dialogContentWidth = detailDialogId.closest('.ui-dialog').width();
    $('#hubrfq-import-sourcing-dynamic-box').width(dialogContentWidth - 30 * 2);
}

function resetForm() {
    state.fileUploader.enable();
    state.showMismatchedDataOnly = true;
    $('#hubrfq-import-sourcing-show-mismatched-only').prop('checked', true);
    $("#hubrfq-import-sourcing-csv-file-reset-btn").prop('disabled', true);
    $("#hubrfq-import-sourcing-csv-file-display-btn").prop('disabled', true);
    $('#hubrfq-import-display-wrapper').addClass('d-none');

    // Destroy DataTable if exists
    if (state.rawDataTable) {
        state.rawDataTable.destroy();
        $('#hubrfq-import-sourcing-raw-data-table').empty();
        state.rawDataTable = null;
    }

    state.currentRawDataSource = null;
}

function hasUnsavedChanges() {
    return $('#hubrfq-import-sourcing-raw-data-table td.dirty-cell').length > 0;
}

function confirmUnsaveWarningDialogAsync() {
    return new Promise((resolve) => {
        window._unsaveDialogResolve = resolve;
        unsaveDialogId.dialog('open');
    });
}

function setupEventListener() {
    $('#hubimportsr-btn').button().on('click', () => {
        detailDialogId.dialog('open', 'title', 'abc');
    });

    // Reset button click
    $('#hubrfq-import-sourcing-csv-file-reset-btn').button().on('click', async (e) => {
        e.preventDefault();
        if(hasUnsavedChanges()) {
            if(await confirmUnsaveWarningDialogAsync())
                resetForm();
        }
        else {
            resetDialogId.dialog('open');
        }
    });

    // Display button click
    $('#hubrfq-import-sourcing-csv-file-display-btn').button().on('click', function (e) {
        e.preventDefault();
        $(this).prop('disabled', true);
        reloadDataTable();
        $('#hubrfq-import-display-wrapper').toggleClass('d-none');
    });

    //Show mismatched only
    $('#hubrfq-import-sourcing-show-mismatched-only').on("change", async function() {
        const isChecked = $(this).is(':checked');
        if(hasUnsavedChanges() && !await confirmUnsaveWarningDialogAsync()) {
            $(this).prop('checked', !isChecked);
            return false;
        }
        state.showMismatchedDataOnly = isChecked;
        reloadDataTable();
    });

    //Correction section
    setUpCorrectionSectionEventListener("hubrfq-import-sourcing-manufacturer", state.manufacturerSearchSelect, "MFR");
    setUpCorrectionSectionEventListener("hubrfq-import-sourcing-supplier", state.supplierSearchSelect, "SUPPLIER");

    $('#hubrfq-import-save-check-btn').button().on('click', async function (e) {
        e.preventDefault();
        if(await saveImportSourcingDataAsync()) {
            $(this).prop('disabled', true);
            reloadDataTable();
        }
    });

    $('#hubrfq-import-import-btn').button().on('click', async (e) => {
        e.preventDefault();
        if(hasUnsavedChanges() && !await confirmUnsaveWarningDialogAsync()){
            return false;
        }
        if(await importSourcingResultsAsync()) {
            resetForm();
            detailDialogId.dialog("close");
            $('#hub-rfq-item-box').find('.section-box-refesh-button').trigger('click');
        }
    });

}

function reloadDataTable() {
    if (state.rawDataTable == null) {
        initDataTable();
    } else {
        state.rawDataTable.ajax.reload();
    }
}

function setUpCorrectionSectionEventListener(elementName, searchSelect, type) {
    //Replace all button
    $(`#${elementName}-replace-all-btn`).button().on('click', async (e) => {
        e.preventDefault();
        if(hasUnsavedChanges() && !await confirmUnsaveWarningDialogAsync())
            return false;
        const incorrectValue = $(`#${elementName}-incorrect-dropdown`).val();
        const newValue = $(`#${elementName}-auto-search`)
            .closest(".position-relative")
            .find(".selected-item .selected-item-content")
            .text();
        if(await updateIncorrectDataAsync(state.bomNo, incorrectValue, newValue, type))
            reloadDataTable();
    });

    //Drop-down change
    $(`#${elementName}-incorrect-dropdown`).on("change", function () {
        const count = $(this).find("option:selected").data("count");
        loadMismatchCount($(`#${elementName}-incorrect-count`), count);
        enableReplaceAllBtn(elementName);
    });

    // //Search select
    $(`#${elementName}-auto-search`).on("focus", function () {
        if($(`#${elementName}`).val().trim() !== '')
            return;
        searchSelect.adjustDropdownPosition();
        searchSelect.isSearchInputFocused = true;
        const searchInput = $(`#${elementName}-incorrect-dropdown`).find("option:selected").val() != 0 ?
            $(`#${elementName}-incorrect-dropdown`).find("option:selected").text().split(" ")[0].trim() : "";
        searchSelect.handleSearchInput(searchInput);
    });

    searchSelect.on("change", function () {
        enableReplaceAllBtn(elementName);
    });
}

function enableReplaceAllBtn(elementName) {
    const disabled = $(`#${elementName}-incorrect-dropdown`).find("option:selected").val() == 0 ||
        $(`#${elementName}`).val() == 0;
    $(`#${elementName}-replace-all-btn`).attr("disabled", disabled);
}

function loadMismatchCount(countElement, count) {
    countElement.text(count);
    if (count == 0) {
        countElement.parent().addClass("d-none");
    }
    else {
        countElement.parent().removeClass("d-none");
    }
}

async function fileUploadedSuccess(data) {
    const importResult = await importDataFileAsync(state.bomNo, data.originalFileName, data.uploadedFileFullPath);
    if (importResult) {
        state.fileUploader.disable();
        $("#hubrfq-import-sourcing-csv-file-reset-btn").prop('disabled', false);
        $("#hubrfq-import-sourcing-csv-file-display-btn").prop('disabled', false);

        showToast('success', 'Imported!');
    }
}

async function importDataFileAsync(bomNo, originalFileName, generatedFilename) {
    const header = { "RequestVerificationToken": $('input[name="__RequestVerificationToken"]').val() };
    const data = {
        bomNo,
        originalFileName,
        generatedFilename
    };

    const response = await GlobalTrader.ApiClient.postAsync(`orders/bom/import-sourcing-results/import-data-file`, data, header);
    if (!response.success && response.Errors) {
        const errorKey = Object.keys(response.Errors)[0];
        const messageError = response.Errors[errorKey][0];
        state.fileUploader.showError({
            code: "apiError",
            message: messageError
        });
    }

    return response.success;
}

async function updateIncorrectDataAsync(bomNo, incorrectValue, newValue, type){
    const header = { "RequestVerificationToken": $('input[name="__RequestVerificationToken"]').val() };
    const data = {
        bomNo,
        incorrectValue,
        newValue,
        type
    };

    const response = await GlobalTrader.ApiClient.putAsync(`orders/bom/import-sourcing-results/incorrect-data`, data, header);
    if (response.success) {
        showToast('success', localizedMessages.replaceSuccessfulMsg.replace('[#recordCount#]', response.data));
    }
    else{
        showToast('danger', unexpectedError);
    }

    return response.success;

}

async function saveImportSourcingDataAsync() {
    if(!hasUnsavedChanges() || !Array.isArray(state.currentRawDataSource) || !state.currentRawDataSource.length)
        return false;
    const header = { "RequestVerificationToken": $('input[name="__RequestVerificationToken"]').val() };
    const data = {
        dataList: state.currentRawDataSource.map(item => ( {
            bomImportSourcingId: item.bomImportSourcingId,
            bomNo: state.bomNo,
            customerRequirementNumber: cleanTextHelper(item.requirement),
            customerRefNo: cleanTextHelper(item.customerRefNo),
            supplierPart: cleanTextHelper(item.supplierPart),
            supplierCost: cleanTextHelper(item.supplierCost),
            rohs: cleanTextHelper(item.rohs),
            dateCode: cleanTextHelper(item.dateCode),
            package: cleanTextHelper(item.package),
            offeredQuantity: cleanTextHelper(item.offeredQuantity),
            offerStatus: cleanTextHelper(item.offerStatus),
            spq: cleanTextHelper(item.spq),
            factorySealed: cleanTextHelper(item.factorySealed),
            qtyInStock: cleanTextHelper(item.qtyInStock),
            moq: cleanTextHelper(item.moq),
            lastTimeBuy: cleanTextHelper(item.lastTimeBuy),
            currency: cleanTextHelper(item.currency),
            buyPrice: cleanTextHelper(item.buyPrice),
            sellPrice: cleanTextHelper(item.sellPrice),
            shippingCost: cleanTextHelper(item.shippingCost),
            leadTime: cleanTextHelper(item.leadTime),
            region: cleanTextHelper(item.region),
            deliveryDate: cleanTextHelper(item.deliveryDate),
            msl: cleanTextHelper(item.msl),
            notes: cleanTextHelper(item.notes)
        }))};

    const response = await GlobalTrader.ApiClient.postAsync('orders/bom/import-sourcing-results/save-data', data, header);
    if(response.success) {
        showToast('success', localizedMessages.saveSuccessfulMsg.replace('[#recordCount#]', response.data));
    }
    else {
        showToast('danger', unexpectedError);
    }
    return response.success;
}

async function importSourcingResultsAsync() {
    if(hasUnsavedChanges())
        return false;
    const header = { "RequestVerificationToken": $('input[name="__RequestVerificationToken"]').val() };
    const data = {
        bomNo: state.bomNo
    };

    const response = await GlobalTrader.ApiClient.postAsync('orders/bom/import-sourcing-results', data, header);
    if(response.success) {
        showToast('success', localizedMessages.importSuccessfulMsg.replace('[#recordCount#]', response.data));
    }
    else {
        showToast('danger', unexpectedError)
    }
    return response.success;
}

function cleanTextHelper(text) {
    return text && text.toString().trim() !== "" ? text.toString().trim() : null; 
}