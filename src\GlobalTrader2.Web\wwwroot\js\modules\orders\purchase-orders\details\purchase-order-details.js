﻿import { PurchaseOrderMainInfoManager } from './components/main-info/main-info.js?v=#{BuildVersion}#';
import { SupplierApprovalsManager } from './components/supplier-approvals/supplier-approvals.js?v=#{BuildVersion}#';
import { POLinesManager } from './components/po-lines/po-lines.js?v=#{BuildVersion}#';

$(() => {
    const purchaseOrderDetailsManager = new PurchaseOrderDetailsManager();
    purchaseOrderDetailsManager.initialize();
});

class PurchaseOrderDetailsManager {
    constructor() {
        this.purchaseOrderId = getParameterByName('po');
        this.purchaseOrderMainInfoManager = new PurchaseOrderMainInfoManager(this.purchaseOrderId);
        this.poLinesModel = {
            purchaseOrderId: this.purchaseOrderId
        }
        this.poLinesManager = new POLinesManager(this.poLinesModel);
        this.supplierApprovalManager = new SupplierApprovalsManager(this.purchaseOrderId);
    }

    initialize() {
        this.purchaseOrderMainInfoManager.initialize();
        this.poLinesManager.initialize();
        this.supplierApprovalManager.initialize();
    }
}
