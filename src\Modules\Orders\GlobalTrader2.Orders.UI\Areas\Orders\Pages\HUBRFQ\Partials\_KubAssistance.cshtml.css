﻿.kub-assistant-container {
    position: absolute;
    z-index: 10;
}

.kub-scroll-content {
    max-height: 300px;
    overflow-y: scroll;
    overflow-x: hidden;
}

::deep .simple-table.dataTable thead {
    background-color: #47863F;
    text-align: center;
    font-weight: bold;
    color: white;
}

::deep tr {
    cursor: default !important;
}

::deep tr.hover-row {
    background-color: transparent !important;
}

::deep .dt-layout-row {
    margin: 0px !important;
}

.kub-detail-container {
    padding: 0 10px;
}

.kub-seleted-part-no {
    background-color: #47863F;
    text-align: center;
    font-weight: bold;
    color: white;
    padding: 6px 0px;
}

.kub-expandable-row {
    cursor: pointer;
}

.kub-view-details {
    color: #5E6EFF;
    font-weight: bold;
    background-image: url('../../img/kub/icon_down.svg');
    background-repeat: no-repeat;
    background-position: right center;
    padding-right: 19px;
}

.kub-view-details-active {
    background-image: url('../../img/kub/icon_up.svg');
    background-repeat: no-repeat;
    background-position: right center;
    padding-right: 19px;
}

.kub-value {
    margin-right: 10px;
}

::deep .actualCurrency {
    margin-left: 2%;
    color: green;
}

::deep .documentachor {
    color: blue;
    margin-left: 4%;
    font-style: italic;
}

::deep .border-bottom {
    border-bottom: 1px solid #000000 !important;
}
