﻿namespace GlobalTrader2.Orders.UserCases.Orders.PurchaseOrder.Queries.GetEprById;

public class GetEprByIdHandler : IRequestHandler<GetEprByIdQuery, BaseResponse<EprDetailsReadModel>>
{
    private readonly IBaseRepository<EprDetailsReadModel> _eprRepository;

    public GetEprByIdHandler(IBaseRepository<EprDetailsReadModel> eprRepository)
    {
        _eprRepository = eprRepository;
    }

    public async Task<BaseResponse<EprDetailsReadModel>> Handle(GetEprByIdQuery request, CancellationToken cancellationToken)
    {
        var response = new BaseResponse<EprDetailsReadModel>();
        var parameters = new List<SqlParameter>()
        {
            new SqlParameter("EprId", SqlDbType.Int)
            {
                Value = request.EprId
            }
        };

        var result = await _eprRepository
            .SqlQueryRawAsync($"{StoredProcedures.Select_EPR} @EprId", parameters.ToArray());

        if (result is null)
            throw new ArgumentException($"Cannot find EPR with id {request.EprId}");

        response.Data = result[0];
        response.Success = true;

        return response;
    }
}
