﻿using GlobalTrader2.Core.Domain.Entities;
using GlobalTrader2.Core.StoreName;
using GlobalTrader2.Dto.BOM;
using Microsoft.Data.SqlClient;
using System.Data;

namespace GlobalTrader2.Orders.UserCases.Orders.BOM
{
    /// <summary>
    /// Implementation for BLL.BOM.DataListNugget 
    /// </summary>
    public class BOMDLNDetailQueryHandler : IRequestHandler<BOMDLNDetailQuery, BaseResponse<IEnumerable<BOMDLNDetailDTO>>>
    {
        private readonly IBaseRepository<BOMDLNDetailModel> _repository;
        private readonly IBaseRepository<BOMPHDetailModel> _bomPOHubRepository;
        private readonly IBaseRepository<BOMPHAssignModel> _bomPHAssignRepository;
        private readonly IBaseRepository<BOMPHDetailAssignModel> _bomPHDetailAssignRepository;
        private readonly IBaseRepository<BOMPHAS6081AssignModel> _bomPHAS6081AssignRepository;
        private readonly IBaseRepository<BOMPHAS6081DetailAssignModel> _bomPHAS6081DetailAssignRepository; 
        private readonly IMapper _mapper;

        public BOMDLNDetailQueryHandler(IBaseRepository<BOMDLNDetailModel> bomRepository,
            IBaseRepository<BOMPHDetailModel> bomPOHubRepository,
            IBaseRepository<BOMPHAssignModel> bomPHAssignRepository,
            IBaseRepository<BOMPHDetailAssignModel> bomPHDetailAssignRepository,
            IBaseRepository<BOMPHAS6081AssignModel> bomPHAS6081AssignRepository,
            IBaseRepository<BOMPHAS6081DetailAssignModel> bomPHAS6081DetailAssignRepository,
            IMapper mapper)
        {
            _repository = bomRepository;
            _mapper = mapper;
            _bomPHAssignRepository = bomPHAssignRepository;
            _bomPOHubRepository = bomPOHubRepository;
            _bomPHDetailAssignRepository = bomPHDetailAssignRepository;
            _bomPHAS6081AssignRepository = bomPHAS6081AssignRepository;
            _bomPHAS6081DetailAssignRepository = bomPHAS6081DetailAssignRepository;
        }

        public async Task<BaseResponse<IEnumerable<BOMDLNDetailDTO>>> Handle(BOMDLNDetailQuery request, CancellationToken cancellationToken)
        {
            var response = new BaseResponse<IEnumerable<BOMDLNDetailDTO>>();

            string storedProcedure = DetermineSPName(request);

            var parameters = new List<SqlParameter>
            {
                new("@ClientId", SqlDbType.Int){Value = request.ClientId ?? (object)DBNull.Value},
                new("@OrderBy", SqlDbType.Int){Value = request.OrderBy ?? (object)DBNull.Value},
                new("@SortDir", SqlDbType.Int){Value = request.SortDir ?? (object)DBNull.Value},
                new("@PageIndex", SqlDbType.Int){Value = request.PageIndex ?? (object)DBNull.Value},
                new("@PageSize", SqlDbType.Int){Value = request.PageSize ?? (object)DBNull.Value},
                new("@BOMId", SqlDbType.Int){Value = null ?? (object)DBNull.Value},
                new("@BOMCode", SqlDbType.NVarChar){Value = request.BOMCode ?? (object)DBNull.Value},
                new("@BOMName", SqlDbType.NVarChar){Value = request.BOMName ?? (object)DBNull.Value},
                new("@IsPoHub", SqlDbType.Bit){Value = request.IsPOHub ?? (object)DBNull.Value},
                new("@ClientNo", SqlDbType.Int){Value = request.SelectedClientNo ?? (object)DBNull.Value},
                new("@BomStatus", SqlDbType.Int){Value = request.BOMStatus},
                new("@TeamId", SqlDbType.Int){Value = request.TeamId ?? (object)DBNull.Value},
                new("@DivisionId", SqlDbType.Int){Value = request.DivisionId ?? (object)DBNull.Value},
                new("@LoginId", SqlDbType.Int){Value = request.LoginId ?? (object)DBNull.Value},
                new("@AssignedUser", SqlDbType.Int){Value = request.AssignedUser ?? (object)DBNull.Value},
                new("@ClientDivisionNo", SqlDbType.Int){Value = request.IntDivision ?? (object)DBNull.Value},
            };

            // Handle order change of CompanyTypeid and SalesPerson parameter in Stored Procedures
            if (storedProcedure is StoredProcedures.Select_AS6081_CustomerRequirementAssignforAS6081Tab
                or StoredProcedures.Select_AS6081_DataListNugget_PHBOMAssign
                or StoredProcedures.Select_AS6081_DataListNugget_CustomerRequirementAssign
                or StoredProcedures.Select_DataListNugget_PHBOMAssign) 
            {
                parameters.Add(new("@CompanyTypeid", SqlDbType.Int){Value = request.CompanyTypeId ?? (object)DBNull.Value});
                parameters.Add(new("@SalesPerson", SqlDbType.Int){Value = request.SalesPerson ?? (object)DBNull.Value});
            } 
            else 
            {
                parameters.Add(new("@SalesPerson", SqlDbType.Int){Value = request.SalesPerson ?? (object)DBNull.Value});
                parameters.Add(new("@CompanyTypeid", SqlDbType.Int){Value = request.CompanyTypeId ?? (object)DBNull.Value});
            }

            // Add remaining parameters
            parameters.AddRange(new[]
            {
                new SqlParameter("@StartDate", SqlDbType.DateTime){Value = request.StartDate ?? (object)DBNull.Value},
                new SqlParameter("@EndDate", SqlDbType.DateTime){Value = request.EndDate ?? (object)DBNull.Value},
                new SqlParameter("@RequiredStartDate", SqlDbType.DateTime){Value = request.RequiredStartDate ?? (object)DBNull.Value},
                new SqlParameter("@RequiredEndDate", SqlDbType.DateTime){Value = request.RequiredEndDate ?? (object)DBNull.Value},
                new SqlParameter("@AS6081Required", SqlDbType.Int){Value = request.AS6081Required ?? (object)DBNull.Value},
            });

            if (storedProcedure is StoredProcedures.Select_DataListNugget_BOM or StoredProcedures.Select_DataListNugget_CustomerRequirementForHUBRFQ or StoredProcedures.Select_DataListNugget_PHBOM)
            {
                parameters.Add(new("@SelectedLoginId", SqlDbType.Int) { Value = request.SelectedLoginId ?? (object)DBNull.Value });
            }

            var queryResult = await ExecuteStoredProcedureAsync(storedProcedure, parameters);
            var data = _mapper.Map<IEnumerable<BOMDLNDetailDTO>>(queryResult).ToList();
            response.Success = true;

            data.ForEach(x => x.ClearPartInfo(!request.IsSearchFromRequirements.HasValue || !request.IsSearchFromRequirements.Value));

            response.Data = data;
            return response;
        }

        private string DetermineSPName(BOMDLNDetailQuery request)
        {
            if (request.IsPOHub == true)
            {
                if (request.IsAssignToMe == 0)
                {
                    return StoredProcedures.Select_DataListNugget_PHBOM;
                }

                if (request.IsAssignToMe == 1)
                {
                    // Update PageSize from MyPageSize if needed
                    if (request.MyPageSize.HasValue)
                    {
                        request.PageSize = request.MyPageSize;
                    }

                    if (request.IsAS6081Tab == true)
                    {
                        return request.IsSearchFromRequirements == true ? StoredProcedures.Select_AS6081_CustomerRequirementAssignforAS6081Tab : StoredProcedures.Select_AS6081_DataListNugget_PHBOMAssign;
                    }
                    else
                    {
                        return request.IsSearchFromRequirements == true ? StoredProcedures.Select_AS6081_DataListNugget_CustomerRequirementAssign : StoredProcedures.Select_DataListNugget_PHBOMAssign;
                    }
                }
            }

            return StoredProcedures.Select_DataListNugget_BOM;
        }

        private async Task<object> ExecuteStoredProcedureAsync(string storedProcedure, List<SqlParameter> parameters)
        {
            var parameterString = string.Join(", ", parameters.Select(p => p.ParameterName));

            return storedProcedure switch
            {
                StoredProcedures.Select_DataListNugget_BOM =>
                    await _repository.SqlQueryRawAsync($"{storedProcedure} {parameterString}", [.. parameters]),
                StoredProcedures.Select_DataListNugget_PHBOMAssign =>
                    await _bomPHAssignRepository.SqlQueryRawAsync($"{storedProcedure} {parameterString}", [.. parameters]),
                StoredProcedures.Select_DataListNugget_PHBOM =>
                    await _bomPOHubRepository.SqlQueryRawAsync($"{storedProcedure} {parameterString}", [.. parameters]),
                StoredProcedures.Select_AS6081_DataListNugget_CustomerRequirementAssign => 
                    await _bomPHDetailAssignRepository.SqlQueryRawAsync($"{storedProcedure} {parameterString}", [.. parameters]),
                StoredProcedures.Select_AS6081_DataListNugget_PHBOMAssign =>
                    await _bomPHAS6081AssignRepository.SqlQueryRawAsync($"{storedProcedure} {parameterString}", [.. parameters]),
                StoredProcedures.Select_AS6081_CustomerRequirementAssignforAS6081Tab =>
                    await _bomPHAS6081DetailAssignRepository.SqlQueryRawAsync($"{storedProcedure} {parameterString}", [.. parameters]),
                _ => throw new NotImplementedException()
            };
        }
    }
}
