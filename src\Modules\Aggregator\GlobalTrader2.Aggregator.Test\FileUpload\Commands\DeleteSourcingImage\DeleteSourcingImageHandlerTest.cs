﻿using GlobalTrader2.Aggregator.UseCases.FileUpload.Commands.DeleteSourcingImage;
using GlobalTrader2.Core.Domain.Entities;
using GlobalTrader2.Core;
using Moq;
using System.Linq.Expressions;

namespace GlobalTrader2.Aggregator.Test.FileUpload.Commands.DeleteSourcingImage
{

    public class DeleteSourcingImageHandlerTest
    {
        private readonly Mock<IBaseRepository<SourcingImage>> _repositoryMock;
        private readonly DeleteSourcingImageHandler _handler;

        public DeleteSourcingImageHandlerTest()
        {
            _repositoryMock = new Mock<IBaseRepository<SourcingImage>>();
            _handler = new DeleteSourcingImageHandler(_repositoryMock.Object);
        }

        [Fact]
        public async Task Handle_ShouldReturnSuccess_WhenImageIsFoundAndDeleted()
        {
            // Arrange
            var sourcingImage = new SourcingImage { SourcingImageId = 1 };
            _repositoryMock
                .Setup(r => r.GetAsync(It.IsAny<Expression<Func<SourcingImage, bool>>>()))
                .ReturnsAsync(sourcingImage);

            _repositoryMock
                .Setup(r => r.DeleteAsync(It.IsAny<SourcingImage>()))
                .ReturnsAsync(1);

            var request = new DeleteSourcingImageCommand(1);

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.True(result.Success);
            Assert.True(result.Data);
            _repositoryMock.Verify(r => r.DeleteAsync(sourcingImage), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldReturnFailure_WhenImageNotFound()
        {
            // Arrange
            _repositoryMock
                .Setup(r => r.GetAsync(It.IsAny<Expression<Func<SourcingImage, bool>>>()))
                .ReturnsAsync((SourcingImage?)null);

            var request = new DeleteSourcingImageCommand (99);

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.False(result.Success);
            Assert.False(result.Data);
            _repositoryMock.Verify(r => r.DeleteAsync(It.IsAny<SourcingImage>()), Times.Never);
        }
    }
}
