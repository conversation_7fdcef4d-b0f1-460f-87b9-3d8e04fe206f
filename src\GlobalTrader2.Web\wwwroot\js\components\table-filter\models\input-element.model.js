import { InputElementBase } from "../../base/input-models/input-element-base.model.js?v=#{BuildVersion}#";

export class InputElement extends InputElementBase {
    constructor(element, name, label, id, invisible = false, requiredCheckbox = true) {
        super(element, name, label, id, invisible, requiredCheckbox);

        this.requiredCheckbox = requiredCheckbox ? $('<input>', {
            type: "checkbox",
            class: "form-check-input form-control mt-0 p-0",
            name: `${this.id}Chk`,
            id: `${this.id}Chk`,
            "data-disable-target": this.id
        }) : null;

        if (this.requiredCheckbox) {
            this.wrapper.find("span:first").append(this.requiredCheckbox);
            this.setupCheckboxEventListeners();
        }
    }

    createWrapper(id, label, requiredCheckbox) {
        return `
            <div class="col-6 form-control-wrapper my-auto">
                <div class="row g-3 align-items-center">
                    <div class="col-2">
                        <label for="${id}" class="form-label">${label}</label>
                    </div>
                    <div class="col-8">
                        <div class="d-flex gap-1" id="${id}ElementContent">
                            ${requiredCheckbox ? '<span></span>' : ''}
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    setupCheckboxEventListeners() {
        this.requiredCheckbox.on('change', (e) => {
            this.syncInputState();
            this.trigger('checkboxChanged.mfi', { target: { checked: e.target.checked } });
            this.triggerControlChanged();
        });
    }

    setupElementEventListeners() {
        this.on('blur.mfi', () => {
            this.toggleInputState()
        });
        this.on('focus.mfi', () => {
            this.setRequiredCheckbox(true);
            this.syncInputState();
        });
        this.on('change.mfi', (e) => {
            this.triggerControlChanged();
            this.toggleInputState(e.target.value);
        });
    }

    getMetaData() {
        return {
            isShown: this.isChecked(),
            isOn: this.isChecked(),
            value: this.getValue(),
            fieldType: this.type, // for maintain model in V1
            inputValue: this.getInputValue(),
        }
    }

    toggleInputState(value) {
        const hasValue = String(value || this.getMetaData().inputValue || '').trim();
        this.setRequiredCheckbox(!!hasValue);
        this.syncInputState();
    }

    syncInputState() {
        if (!this.requiredCheckbox) return;
        const isChecked = this.requiredCheckbox.prop('checked');
        this.element.toggleClass("input-disabled", !isChecked);
    }

    setRequiredCheckbox(isChecked) {
        if (this.isChecked() === isChecked) return;
        if (this.requiredCheckbox) {
            this.requiredCheckbox.prop('checked', isChecked);
            this.requiredCheckbox.trigger("change");
        }
    }

    isChecked() {
        return this.requiredCheckbox?.prop('checked') ?? false;
    }

    setState(valueObj) {
        if (valueObj.isOn) {
            this.setValue(valueObj.value);

            if (this.requiredCheckbox) {
                this.requiredCheckbox.prop('checked', valueObj.isShown);
                this.element.toggleClass("input-disabled", !valueObj.isShown);
            }
        }
    }

    getFilterValue() {
        return {
            name: this.id,
            value: this.isChecked() ? this.getValue().value : null,
            fieldType: this.type,
            isOn: this.isChecked(),
            isShown: this.isChecked()
        };
    }

    // Override 
    registerEvents() {
        super.registerEvents();

        this.requiredCheckbox?.on('change', (e) => {
            this.trigger('checkboxChanged.mfi', e);
        });
    }
}
