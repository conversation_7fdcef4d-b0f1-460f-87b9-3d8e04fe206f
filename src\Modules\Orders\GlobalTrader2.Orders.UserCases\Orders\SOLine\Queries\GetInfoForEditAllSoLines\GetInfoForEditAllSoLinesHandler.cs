using GlobalTrader2.Core.Interfaces;
using GlobalTrader2.Dto.SalesOrderLine;

namespace GlobalTrader2.Orders.UserCases.Orders.SOLine.Queries.GetInfoForEditAllSoLines
{
    public class GetInfoForEditAllSoLinesHandler(IBaseRepository<SalesOrder> _soRepo, IBaseRepository<SalesOrderLine> _soLineRepo, IQueryableAsyncService _queryableAsyncService)
        : IRequestHandler<GetInfoForEditAllSoLinesQuery, BaseResponse<SoForEditAllLines>>
    {
        public async Task<BaseResponse<SoForEditAllLines>> Handle(GetInfoForEditAllSoLinesQuery request, CancellationToken cancellationToken)
        {

            var soQueryable = _soRepo.ListAsQueryable();
            var soLineQueryable = _soLineRepo.ListAsQueryable();

            var query = from so in soQueryable
                        where so.SalesOrderId == request.SalesOrderId
                        join line in soLineQueryable
                        on so.SalesOrderId equals line.SalesOrderNo
                        where !line.Closed
                        group line by new { so.SalesOrderId, so.ClientNo, so.AuthorisedBy } into g
                        select new SoForEditAllLines
                        {
                            SalesOrderId = g.Key.SalesOrderId,
                            ClientNo = g.Key.ClientNo,
                            IsAuthorized = g.Key.AuthorisedBy != null,
                            EarliestDatePromised = g.Min(x => x.DatePromised)
                        };

            var result = await _queryableAsyncService.FirstOrDefaultAsync(query);

            return new BaseResponse<SoForEditAllLines>
            {
                Data = result,
                Success = true
            };
            
        }
    }
}
