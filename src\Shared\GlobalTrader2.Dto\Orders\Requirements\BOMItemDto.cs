﻿namespace GlobalTrader2.Dto.Orders.Requirements
{
    public class BomItemDto
    {
        public int CustomerRequirementId { get; set; }
        public int CustomerRequirementNumber { get; set; }
        public string? PartNo { get; set; }
        public string? Part { get; set; }
        public bool Closed { get; set; }
        public string? ClosedReason { get; set; }
        public int? MfrNo { get; set; }
        public string? Mfr { get; set; }
        public string? MfrAdvisoryNotes { get; set; }
        public string? Product { get; set; }
        public string? DC { get; set; }
        public string? Package { get; set; }
        public string? Price { get; set; }
        public string? TPriceInBom { get; set; }
        public string? PriceInBase { get; set; }
        public int Quantity { get; set; }
        public bool Alt { get; set; }
        public byte? ROHS { get; set; }
        public string? Date { get; set; }
        public string? CustomerPart { get; set; }
        public string? Company { get; set; }
        public string? CompanyAdvisoryNotes { get; set; }
        public int CompanyNo { get; set; }
        public string? SalesmanName { get; set; }
        public int Salesman { get; set; }
        public string? BOMCode { get; set; }
        public string? BOMFullName { get; set; }
        public string? IsAs6081Required { get; set; }
        public string? AssignedTo { get; set; }
        public bool IsAssignedToMe { get; set; }
        public string? Instructions { get; set; }
        public string? DisplayStatus { get; set; }
        public int? BOMNo { get; set; }
        public bool Released { get; set; }
        public int CMNo { get; set; }
        public bool HasSourcingResult { get; set; }
        public bool IsRequestToPurchaseQuote { get; set; }
        public string? PurchaseQuoteNumber { get; set; }
        public int? PurchaseQuoteId { get; set; }
        public bool IsPurchaseRequestCreated { get; set; }
        public string? FactorySealed { get; set; }
        public string? MSL { get; set; }
        public bool SourcingResult { get; set; }
        public string? BOMStatus { get; set; }
        public int IPOClientNo { get; set; }
        public bool IsNoBid { get; set; }
        public string? IsExpeditDate { get; set; }
        public int? UpdateByPH { get; set; }
        public int? RequestToPOHubBy { get; set; }
        public int? SupportTeamMemberNo { get; set; }
        public string? SupportTeamMemberName { get; set; }
        public bool PartWatchHUBIPO { get; set; }
        public int? PriceIssueBuyAndSell { get; set; }
        public bool IsAllocated { get; set; }
        public bool IsReleased { get; set; }
        public int? PackageNo { get; set; }
        public int? ProductNo { get; set; }
    }
}