﻿using GlobalTrader2.Core.Domain.Entities;
using GlobalTrader2.Dto.AutoSourcing;
using GlobalTrader2.Dto.Base;
using GlobalTrader2.Dto.MasterCountries;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GlobalTrader2.Aggregator.UseCases.DataList.GlobalCountyListDropDown
{
    public class GetGlobalCountryListDropDownHandler : IRequestHandler<GetGlobalCountryListDropDownQuery, BaseResponse<List<DropDownDto>>>
    {
        private readonly IBaseRepository<MasterCountryDropdownReadModel> _baseRepository;

        public GetGlobalCountryListDropDownHandler(IBaseRepository<MasterCountryDropdownReadModel> baseRepository)
        {
            _baseRepository = baseRepository;
        }

        public async Task<BaseResponse<List<DropDownDto>>> Handle(GetGlobalCountryListDropDownQuery request, CancellationToken cancellationToken)
        {
            request.IncludeSelected = request.IncludeSelected.HasValue && request.IncludeSelected.Value ? request.IncludeSelected : true;
            var procedureName = StoredProcedures.Dropdown_GlobalCountryList;
            var queryStr = $"{procedureName} @IncludeSelected, @ClientNo";
            SqlParameter[] param =
            [
                new SqlParameter("@IncludeSelected", SqlDbType.Int ) { Value = request.IncludeSelected},
                new SqlParameter("@ClientNo", SqlDbType.Int ) { Value = request.ClientNo},
                ];

            var countryList = await _baseRepository.SqlQueryRawAsync(queryStr, param);

            var result = countryList.Select(ps => new DropDownDto
            {
                Id = ps.GlobalCountryId,
                Name = !string.IsNullOrEmpty(ps.GlobalCountryName) ? ps.GlobalCountryName : string.Empty
            }).ToList();

            return new BaseResponse<List<DropDownDto>>
            {
                Success = true,
                Data = result
            };
        }
    }
}
