﻿using FluentAssertions.Execution;
using GlobalTrader2.Orders.UserCases.Orders.PurchaseOrderQuote.Queries;
using GlobalTrader2.Orders.UserCases.Orders.PurchaseOrderQuote.Queries.DTO;

namespace GlobalTrader2.Orders.UserCases.Test.Orders.PurchaseOrderQuote.Queries
{
    public class GetPOQuoteDetailsHandlerTest
    {
        private readonly GetPOQuoteDetailsHandler _quoteDetailsHandler;
        private readonly Mock<IBaseRepository<POQuoteReadModel>> _repositoryMock;
        private readonly Mock<IMapper> _mapperMock;

        public GetPOQuoteDetailsHandlerTest()
        {
            _repositoryMock = new Mock<IBaseRepository<POQuoteReadModel>>();
            _mapperMock = new Mock<IMapper>();

            _quoteDetailsHandler = new GetPOQuoteDetailsHandler(_repositoryMock.Object, _mapperMock.Object);
        }

        [Fact]
        public async Task Handle_WhenFindPOQuoteDetail_ReturnMappedObject()
        {
            // arrange
            var input = new GetPOQuoteDetailsQuery(123);

            _repositoryMock.Setup(x => x.SqlQueryRawAsync(It.IsAny<string>(), It.IsAny<object[]>())).ReturnsAsync(new List<POQuoteReadModel>()
            {
                new POQuoteReadModel()
                {
                    PurchaseRequestId = 123,
                    Buyer = 1,
                    Notes = "note1",
                }
            });
            _mapperMock.Setup(x => x.Map<POQuoteDetailsDto>(It.IsAny<POQuoteReadModel>())).Returns(new POQuoteDetailsDto()
            {
                POQuoteId = 123,
                SalesPersonNo = 1,
                Notes = "note1"
            });

            // act
            var result = await _quoteDetailsHandler.Handle(input, default(CancellationToken));

            // assert
            using var scope = new AssertionScope();
            result.Data.Should().NotBeNull();
            result.Success.Should().BeTrue();
            result.Data!.POQuoteId.Should().Be(input.POQuoteId);
        }

        [Fact]
        public async Task Handle_WhenFindNothing_ReturnNull()
        {
            // arrange
            var input = new GetPOQuoteDetailsQuery(123);

            _repositoryMock.Setup(x => x.SqlQueryRawAsync(It.IsAny<string>(), It.IsAny<object[]>())).ReturnsAsync(new List<POQuoteReadModel>()
            {});

            // act
            var result = await _quoteDetailsHandler.Handle(input, default(CancellationToken));

            // assert
            using var scope = new AssertionScope();
            result.Data.Should().BeNull();
            result.Success.Should().BeTrue();
        }
    }
}
