﻿@using GlobalTrader2.Dto.SalesOrderLine
@inject IViewLocalizer _localizer
@model LinesSectionViewModel

<div class="row form-control-wrapper mb-1" id="form-add-so-line-step1">
    <span class="mb-1">
        @_localizer["Select the source for the new Line"]
    </span>
    <div class="col-3">
        <label for="SelectSource" class="form-label d-inline-block fw-bold">
            @_localizer["Select Source"]
        </label>
    </div>
    <div class="col-6 d-flex flex-column">
        @if(Model.CanAddNewLine)
        {
            <div class="form-check mb-2">
                <input class="form-check-input check-md" name="addSoLineSource" type="radio" value="NEW" id="NewLineItem" />
                <label class="form-check-label" for="NewLineItem">
                    @_localizer["New Line Item"]
                </label>
            </div>
        }
        @if (Model.CanAddReq)
        {
            <div class="form-check mb-2">
                <input class="form-check-input check-md" name="addSoLineSource" type="radio" value="CUSREQ" id="FromRequirements" />
                <label class="form-check-label" for="FromRequirements">
                    @_localizer["From Requirements"]
                </label>
            </div>
        }
        @if (Model.CanAddQuote)
        {
            <div class="form-check mb-2">
                <input class="form-check-input check-md" name="addSoLineSource" type="radio" value="QUOTE" id="FromQuotes" />
                <label class="form-check-label" for="FromQuotes">
                    @_localizer["From Quotes"]
                </label>
            </div>
        }
        @if (Model.CanAddSO)
        {
            <div class="form-check mb-2">
                <input class="form-check-input check-md" name="addSoLineSource" type="radio" value="SO" id="FromSalesOrders" />
                <label class="form-check-label" for="FromSalesOrders">
                    @_localizer["From Sales Orders"]
                </label>
            </div>
        }
        @if (Model.CanAddStock)
        {
            <div class="form-check mb-2">
                <input class="form-check-input check-md" name="addSoLineSource" type="radio" value="STOCK" id="FromStock" />
                <label class="form-check-label" for="FromStock">
                    @_localizer["From Stock"]
                </label>
            </div>
        }
        @if (Model.CanAddService)
        {
            <div class="form-check mb-2">
                <input class="form-check-input check-md" name="addSoLineSource" type="radio" value="SERVICE" id="FromService" />
                <label class="form-check-label" for="FromService">
                    @_localizer["From Service"]
                </label>
            </div>
        }
        @if (Model.CanAddLot)
        {
            <div class="form-check mb-2">
                <input class="form-check-input check-md" name="addSoLineSource" type="radio" value="NEWLOT" id="FromLot" />
                <label class="form-check-label" for="FromLot">
                    @_localizer["From Lot"]
                </label>
            </div>
        }
    </div>
</div>