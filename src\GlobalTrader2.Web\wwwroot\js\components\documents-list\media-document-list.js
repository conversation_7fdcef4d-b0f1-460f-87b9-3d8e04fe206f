﻿import { DocumentTypeConstant } from "./constants/document-type.constant.js?v=#{BuildVersion}#";
import { FileUploaderEvents } from '../../components/file-uploader/constants/file-uploader-events.constant.js?v=#{BuildVersion}#';
import { loadCSS } from '../../helper/load-css.helper.js?v=#{BuildVersion}#';
import { PdfDocument } from './models/pdf-document.model.js?v=#{BuildVersion}#';
import { ExcelDocument } from './models/excel-document.model.js?v=#{BuildVersion}#';
import { ImageDocument } from './models/image-document.model.js?v=#{BuildVersion}#';
import { MediaUploadDocumentDialog } from './media-upload-dialog.component.js?v=#{BuildVersion}#';
import { RemoveDocumentDialog } from './remove-dialog-component.js?v=#{BuildVersion}#';
import { MediaUploaderComponent } from '../file-uploader/media-uploader.component.js?v=#{BuildVersion}#';
export class MediaDocumentManager {
    static DocumentTypeFactory = {
        [DocumentTypeConstant.PDF_DOCUMENT]: function (id) {
            return new PdfDocument(id);
        },
        [DocumentTypeConstant.EXCEL_DOCUMENT]: function (id) {
            return new ExcelDocument(id);
        },
        [DocumentTypeConstant.IMAGE_DOCUMENT]: function (id) {
            return new ImageDocument(id);
        },
    }

    constructor (
        {
            documentSectionComponent = {
                documentTypeId: -1,
                documentTypeName: "",
                sectionName: "",
                canAdd: false,
                canDelete: false,
            },
            id,
            uploadDialogParams = {
                dialogSelector: "",
                formSelector: "",
                allowedFileExtensions: [],
            },
            removeDocumentDialogParams = {
                dialogSelector: "",
                sectionName: "",
            },
            getEndpoint = {
                url: "",
                params: {}
            },
            refreshCallback
        }
    ) {
        //Retrieve from constructor
        this.documentSectionComponent = {
            ...documentSectionComponent,
            canAdd: String(documentSectionComponent.canAdd).toLowerCase() === "true",
            canDelete: String(documentSectionComponent.canDelete).toLowerCase() === "true",
        };
        this.documentTypeId = parseInt(documentSectionComponent.documentTypeId);
        this.uploadDialog = null;
        this.uploadDialogParams = uploadDialogParams;
        this.removeDocumentDialog = null;
        this.removeDocumentDialogParams = removeDocumentDialogParams;
        this.fileUploader = null;
        this.id = parseInt(id);

        this.documentInstance = null;
        //Internal values
        this.$DocumentsSectionBox = null;
        this.documentMaxFileSizeDto = null;
        this.documentsList = [];
        this.maxDocumentCount = null;
        this.currentDocumentCount = 0;
        this.getEndpoint = getEndpoint;
        this.refreshCallback = refreshCallback;
    }

    async initialize() {
        this.$DocumentsSectionBox = $(`#${this.documentSectionComponent.sectionId}-box`);
        loadCSS('/js/components/documents-list/document-list.css');

        this.documentInstance = new ImageDocument(this.id);

        this.setupDocumentsSectionBox();
        this.updateNoFileMessageTooltip();
        await this.initDocumentSectionAsync();

        this.initUploadDocument();
        this.initDeleteDocument();

        if (this.fileUploader?.uploadButton) {
            if (this.currentDocumentCount >= this.maxDocumentCount) {
                this.fileUploader.uploadButton.disabled = true;
            } else {
                this.fileUploader.uploadButton.disabled = false;
            }
        }
    }

    setupDocumentsSectionBox() {
        this.$DocumentsSectionBox.section_box({
            loading: true,
            loadingContentId: `${this.documentSectionComponent.sectionId}-content`,
            onRefreshClick: async () => {
                await this.refreshDocumentsSectionBox();
                if (this.refreshCallback) this.refreshCallback();
            }
        });
    }

    initFileUploader() {
        this.fileUploader = new MediaUploaderComponent(
            `${this.uploadDialogParams.formSelector}`,
            `${this.uploadDialogParams.dialogSelector}`,
            `${this.documentSectionComponent.sectionId}`,
            {
                maxSizeMB: this.documentMaxFileSizeDto.documentMB,
                allowedTypes: this.uploadDialog.allowedFileExtensions,
                maxCount: this.maxDocumentCount,
                multiple: true
            },
        );
        this.fileUploader.setCurrentCount(this.currentDocumentCount);

        this.fileUploader.on(FileUploaderEvents.CLICK, () => {
            if (this.currentDocumentCount < this.maxDocumentCount) {
                this.uploadDialog.$dialog.dialog("open");
            }
            else this.fileUploader.showError({
                code: "maxCountError",
                message: window.localizedStrings.CannotUploadMoreThan.replace('{0}', `${this.maxDocumentCount}`)
            });
        });
    }


    bindOnSubmitUpload() {
        this.uploadDialog.on('form:submit', async () => {
            //validate before called api
            const fileInput = this.uploadDialog.$form.find('input[id="FileInput"]')[0];
            if (this.fileUploader.uploadedFiles == 0 || this.fileUploader.uploadedFiles.length + this.currentDocumentCount > this.maxDocumentCount)
            {
                return;
            }
            if (!fileInput?.files?.length) {
                return { success: false, message: "FileSizeZero" };
            }

            this.uploadDialog.setLoading(true);
            const response = await this.documentInstance.uploadFileAsync(this.uploadDialog.$form);
            this.uploadDialog.setLoading(false);

            if (response.success) {
                this.uploadDialog.$dialog.dialog("close");
                showToast('success', window.localizedStrings.saveChangedMessage);
                await this.refreshDocumentsSectionBox();
            }
            else {
                return showToast("danger", response.title);
            }
        })
    }

    bindOnCloseForm() {
        this.uploadDialog.on('form:close', () => {
            this.fileUploader.uploadedFiles = [];
            this.fileUploader.lastRenderedFileCount = 0;
            this.fileUploader.$filesToUploadContainer.empty();
            this.fileUploader.fileInput.value = '';
            this.fileUploader.clearError();
        });
    }

    bindOnSubmitRemove() {
        this.removeDocumentDialog.$dialog.on("saveSuccess", async () => {
            this.removeDocumentDialog.setLoading(true);
            const queryParams = `?id=${encodeURIComponent(this.removeDocumentDialog.id)}&sectionName=${encodeURIComponent(this.removeDocumentDialog.sectionName)}&fileName=${encodeURIComponent(this.removeDocumentDialog.fileName)}`;

            const response = await this.documentInstance.deleteFileAsync(queryParams);
            this.removeDocumentDialog.setLoading(false);
            if (!response?.success) return showToast("danger", response.title);

            this.removeDocumentDialog.$dialog.dialog("close");
            showToast('success', window.localizedStrings.saveChangedMessage);
            await this.refreshDocumentsSectionBox();
        })
    }

    async initDocumentSectionAsync() {
        this.$DocumentsSectionBox.section_box("option", "loading", true);

        const maxPDFFileSizeData = await this.getMaxDocumentFileSize();
        this.$DocumentsSectionBox.find('#document-size').text(`(${maxPDFFileSizeData.documentMB} MB)`)

        const defaultParams = { sectionName: this.documentSectionComponent.sectionName };
        const documentsListData = await this.documentInstance.getDocumentsListAsync({
            endpoint: this.getEndpoint?.endpoint,
            params: { ...defaultParams, ...this.getEndpoint?.params }
        });
        this.documentsList = documentsListData;
        this.currentDocumentCount = documentsListData?.length || 0;

        if (this.fileUploader) {
            this.fileUploader.setCurrentCount(this.currentDocumentCount);
        }

        this.renderDocumentsListAsync(documentsListData)

        this.$DocumentsSectionBox.section_box("option", "loading", false);

        this.maxDocumentCount = 5;// HARD CODED same as V1
        this.$DocumentsSectionBox.find('#max-document-file').text(`(${this.maxDocumentCount} files)`)

        if (this.fileUploader?.uploadButton) {
            if (this.currentDocumentCount >= this.maxDocumentCount) {
                this.fileUploader.uploadButton.disabled = true;
            } else {
                this.fileUploader.uploadButton.disabled = false;
            }
        }
    }

    async getMaxDocumentFileSize() {
        const documentMaxFileSize = await GlobalTrader.ApiClient.getAsync("/documents/file-size", {
            documentType: this.documentSectionComponent.documentTypeId,
        });

        if (documentMaxFileSize.success) {
            this.documentMaxFileSizeDto = documentMaxFileSize.data;
        }
        return documentMaxFileSize?.data
    }

    async reloadDocumentsSectionBox(id) {
        if (!id) return;
        this.documentInstance.UpdateRefId(id);
        await this.refreshDocumentsSectionBox();
    }

    async refreshDocumentsSectionBox() {
        if (this.fileUploader) {
            this.fileUploader.clearError();
            this.fileUploader.resetFileUploadValues()
        }
        await this.initDocumentSectionAsync();
        if (this.uploadDialog) {
            this.uploadDialog.destroyFormValidation();
            this.uploadDialog.initFormValidation(this.documentMaxFileSizeDto.documentMB);
        }
    }

    _renderUI() {
        this.container.querySelector('#uploadBtn').addEventListener('click', (e) => {
            e.preventDefault();
            this._handleClick();
        });
    }

    renderDocumentsListAsync(documentsList) {
        let $noDataMessageDiv = this.$DocumentsSectionBox.find(`#${this.documentSectionComponent.sectionId}-no-data-message`);
        let listHtml = '';
        let appResolutions = documentImageConstant.ImageResolutions;
        if (documentsList?.length > 0) {
            $noDataMessageDiv.hide();
            listHtml = documentsList
                .map(document => {
                    let tooltipMessage = GlobalTrader.StringHelper.formatAdvisoryNotes(document.caption);
                    return `
                        <div class="col-2">
                            <div class="card text-center file-detail">
                                <span class="text-center p-1">
                                    <img tabindex="0"
                                        src="${this.getImageOfDocument(document, appResolutions.Thumbnail.Code)}" 
                                        class="card-img-top file-image" 
                                        alt="${document.caption}"
                                        style="cursor: pointer; height: 105px !important;width: 100%; object-fit: contain;"    
                                        data-document-id="${document.documentId}"
                                        onerror="
                                            this.style.display='none'; 
                                            this.nextElementSibling.style.display='flex';
                                            const linkBlock = document.getElementById('image-actions-${document.documentId}');
                                            if (linkBlock) linkBlock.style.visibility = 'hidden';
                                        ">
                                    <div class="not-found-resource-message" style="display: none;">${window.localizedStrings.cantLoadImageMsg}</div>
                                    ${this.documentSectionComponent.canDelete && !this.documentSectionComponent.isReadOnly ?
                            `<button tabindex="0" type="button" class="btn-delete-document btn-close position-absolute right-0 top-0"
                                                aria-label=${window.localizedStrings.deleteDocument} data-document-id="${document.documentId}"></button>
                                        ` : ""}
                                </span>
                                <div class="card-body p-1 file-description">
                                    <p class="card-title mb-1 line-clamp-3" data-bs-toggle="tooltip" data-bs-placement="bottom" title="${tooltipMessage}">
                                        ${GlobalTrader.StringHelper.stripScriptTags(document.caption) || '&nbsp;'}
                                    </p>
                                    <p class="card-text mb-1">
                                        ${document.dateUploadString}
                                    </p>
                                    <p class="card-text line-clamp-2 mb-1">
                                        ${document.updatedByName}
                                    </p>
                  
                                    <div id="image-actions-${document.documentId}" class="mb-1">
                                        <a class="dt-hyper-link" href="#" onclick="event.preventDefault(); window.open('${this.getImageOfDocument(document, appResolutions.Medium.Code)}', '_blank', 'width=${appResolutions.Medium.Width},height=${appResolutions.Medium.Height}');">${window.localizedStrings.medium}</a>
                                        |
                                        <a class="dt-hyper-link" href="#" onclick="event.preventDefault(); window.open('${this.getImageOfDocument(document, appResolutions.Full.Code)}', '_blank', 'width=${appResolutions.Full.Width},height=${appResolutions.Full.Height}');">${window.localizedStrings.large}</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `
                }).join('');
        } else {
            $noDataMessageDiv.show();
            listHtml = ``;
        }
        this.bindOffClickEvents();
        this.bindOnClickEvents();
        $(`#${this.documentSectionComponent.sectionId}-list`).html(listHtml);
    }

    bindOffClickEvents() {
        const listDocumentId = `#${this.documentSectionComponent.sectionId}-list`;
        //Onclick document image
        this.$DocumentsSectionBox.find(`${listDocumentId}`).off("click", ".file-image");
        //Delete button
        this.$DocumentsSectionBox.find(`${listDocumentId}`).off("click", ".btn-delete-document");
    }

    bindOnClickEvents() {
        const listDocumentId = `#${this.documentSectionComponent.sectionId}-list`;
        //Onclick document image
        this.$DocumentsSectionBox.find(`${listDocumentId}`).off("click keydown").on("click keydown", ".file-image", (e) => {
            if (e.type === "click" || e.key === "Enter") {
                const documentId = $(e.currentTarget).data("document-id");
                const document = this.documentsList.find(document => document.documentId === documentId);
                this.onClickDocumentImage(document.fileName, GlobalTrader.StringHelper.stripScriptTags(document.caption));
            }
        });
        //Delete button
        this.$DocumentsSectionBox.find(`${listDocumentId}`).on("click keydown", ".btn-delete-document", (e) => {
            if (e.type === "click" || e.key === "Enter") {
                e.preventDefault();
                const documentId = $(e.currentTarget).data("document-id");
                const document = this.documentsList.find(document => document.documentId === documentId);
                this.onClickDeleteDocument(documentId, document.fileName);
            }
        });
    }

    async onClickDocumentImage(fileName, fileCaption) {
        if (!fileName) return;
        const folderName = this.documentSectionComponent.sectionName.split("_")[0];
        await this.documentInstance.openFileAsync(fileName, folderName, fileCaption);
    }

    getImageOfDocument(document, sizeType) {

        return this.generateImageSourceLink(document.fileName, document.stockImageId, sizeType);
    }

    onClickDeleteDocument(documentId, fileName) {
        this.removeDocumentDialog.$dialog.dialog("open");
        this.removeDocumentDialog.id = documentId;
        this.removeDocumentDialog.fileName = fileName;
    }

    generateImageSourceLink(ImageName, ImageId, Type) {
        const params = new URLSearchParams({
            ImageSourceFrom: "SOURCEIMAGE",
            ImageName: ImageName,
            ImageId: ImageId,
            Type: Type
        });

        return `${this.documentInstance.imageUrl}?${params.toString()}`;
    }

    initUploadDocument() {
        if (this.documentSectionComponent.canAdd && !this.documentSectionComponent.isReadOnly) {
            this.uploadDialog = new MediaUploadDocumentDialog(this.uploadDialogParams);
            this.uploadDialog.initFormValidation(this.documentMaxFileSizeDto.documentMB);
            this.uploadDialog.bindingSectionName(this.documentSectionComponent.sectionName);
            this.bindOnSubmitUpload();
            this.bindOnCloseForm();
            this.initFileUploader();
        };
    }

    initDeleteDocument() {
        if (this.documentSectionComponent.canDelete && this.removeDocumentDialogParams.dialogSelector) {
            this.removeDocumentDialog = new RemoveDocumentDialog(this.removeDocumentDialogParams);
            this.bindOnSubmitRemove();
        };
    }

    updateNoFileMessageTooltip() {
        if (!this.documentInstance || !this.documentSectionComponent.noFileTooltipMessage) return;

        this.documentInstance.updateNoFileTooltipMessage(this.documentSectionComponent.noFileTooltipMessage);
    }
}