﻿SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER OFF
GO
/*  
===========================================================================================  
TASK			    UPDATED BY         DATE         ACTION    DESCRIPTION  
[US-228547]		    Phat.NguyenTien	   31-Jul-2025  CREATE       Create new V2
===========================================================================================  
*/
CREATE OR ALTER PROCEDURE [dbo].[usp_insert_IPOOffer_V2]
    @Part NVARCHAR(30) = NULL,
    @ManufacturerNo INT = NULL,
    @DateCode NVARCHAR(5) = NULL,
    @ProductNo INT = NULL,
    @PackageNo INT = NULL,
    @Quantity INT = NULL,
    @Price FLOAT = NULL,
    @CurrencyNo INT = NULL,
    @OriginalEntryDate DATETIME = NULL,
    @Salesman INT = NULL,
    @SupplierNo INT = NULL,
    @SupplierName NVARCHAR(128) = NULL,
    @ROHS TINYINT = NULL,
    @OfferStatusNo INT = NULL,
    @Notes NVARCHAR(128) = NULL,
    @UpdatedBy INT = NULL,
    @ClientNo INT = NULL,
    @SupplierTotalQSA NVARCHAR(50) = NULL,
    @SupplierLTB NVARCHAR(50) = NULL,
    @SupplierMOQ NVARCHAR(50) = NULL,
    @MSL NVARCHAR(50) = NULL,
    @SPQ NVARCHAR(50) = NULL,
    @LeadTime NVARCHAR(50) = NULL,
    @FactorySealed NVARCHAR(50) = NULL,
    @ROHSStatus NVARCHAR(50) = NULL,
    @OfferId INT output,
    @IsPoHUB BIT = 0,
    @MSLLevelNo INT = NULL,
	@SellPriceLessReason NVARCHAR(128) = NULL
AS
BEGIN



    Declare @IsOffer int
    set @IsOffer = 1
    INSERT INTO [BorisGlobalTraderImports].dbo.tboffer
    (
        fullpart,
        part,
        manufacturerno,
        datecode,
        productno,
        packageno,
        quantity,
        price,
        currencyno,
        originalentrydate,
        salesman,
        supplierno,
        suppliername,
        rohs,
        notes,
        dlup,
        offerstatusno,
        offerstatuschangedate,
        offerstatuschangeloginno,
        updatedby,
        clientno,
        suppliertotalqsa,
        supplierltb,
        suppliermoq,
        msl,
        spq,
        leadtime,
        factorysealed,
        rohsstatus,
        ispohub,
        msllevelno,
		sellpricelessreason
    )
    VALUES
    (dbo.Ufn_get_fullpart(@Part),
     @Part,
     @ManufacturerNo,
     @DateCode,
     @ProductNo,
     @PackageNo,
     @Quantity,
     @Price,
     @CurrencyNo,
     @OriginalEntryDate,
     @Salesman,
     @SupplierNo,
     @SupplierName,
     @ROHS,
     @Notes,
     CURRENT_TIMESTAMP,
     @OfferStatusNo,
     CURRENT_TIMESTAMP,
     @UpdatedBy,
     @UpdatedBy,
     @ClientNo,
     @SupplierTotalQSA,
     @SupplierLTB,
     @SupplierMOQ,
     @MSL,
     @SPQ,
     @LeadTime,
     @FactorySealed,
     @ROHSStatus,
     @IsPoHUB,
     @MSLLevelNo,
	 @SellPriceLessReason
    )
    SET @OfferId = Scope_identity()
    -- Insert offer for other server          
    -- if(@IsPoHUB=1)          
    --Begin            
    --EXEC Usp_insert_ipooffertotransferoffer             
    --@IsOffer,            
    --@SupplierNo,             
    --@Part,             
    --@ManufacturerNo,             
    --@Quantity,             
    --@Price,             
    --@DateCode,           
    --@ProductNo,             
    --@PackageNo,             
    --@CurrencyNo,             
    --@Notes,             
    --@ClientNo,             
    --@ROHS,          
    --@OfferStatusNo,             
    --@SupplierTotalQSA,             
    --@SupplierLTB,             
    --@SupplierMOQ,            
    --@SPQ,             
    --@LeadTime,             
    --@FactorySealed,             
    --@ROHSStatus,            
    --@MSLLevelNo ,          
    --@OfferId,          
    --'I'             
    --End          


    /*Code started to send mail and notication to client for part availability*/
    --select @Part      
    Create table #tempUserMailId
    (
        CustomerRequirementId int,
        CustomerRequirementNumber int,
        PartWatch_HUBIPOBy int,
        PartWatch_HUBIPOByName varchar(200),
        PartWatch_HUBIPOByEmail varchar(200),
        Salesman int,
        SalesmanName varchar(200),
        SalesmanEmail varchar(200),
        PartWatch bit,
        PartWatch_HUBIPO bit,
        SendFlag char(1),
        BOMNo int
    )

    insert into #tempUserMailId
    select b.customerrequirementid,
           b.customerrequirementNumber,
           b.PartWatch_HUBIPOBy,
           null,
           null,
           b.salesman,
           null,
           null,
           partwatch,
           PartWatch_HUBIPO,
           'N',
           BOMNo
    from tbbom a
        join tbcustomerrequirement b
            on a.bomid = b.bomno
    where b.part = @Part
          and (
                  b.partwatch = 1
                  or b.PartWatch_HUBIPO = 1
              )
    --select a.PartWatch_HUBIPOBy,b.salesman from tbbom a join tbcustomerrequirement b on a.bomid=b.bomno where b.fullpart = @Part and b.PartWatch_HUBIPO = 1       
    --select * from tbcustomerrequirement where fullpart = @Part and PartWatch_HUBIPO = 1       

    --select * from #tempUserMailId      
    -- updating email according to hub person  
    update a
    set a.PartWatch_HUBIPOByEmail = b.EMail,
        a.PartWatch_HUBIPOByName = b.EmployeeName
    from #tempUserMailId a
        join tblogin b
            on a.PartWatch_HUBIPOBy = b.loginid
    -- updating email according to salesperson  
    update a
    set a.SalesmanEmail = b.EMail,
        a.SalesmanName = b.EmployeeName
    from #tempUserMailId a
        join tblogin b
            on a.Salesman = b.loginid

    --select * from #tempUserMailId      

    while ((select count(1) from #tempUserMailId where SendFlag = 'N') > 0)
    begin
        Declare @custnum int
        Declare @custid int
        Declare @partWatchClient bit
        Declare @partWatchHub bit
        Declare @HubId int
        Declare @salesmanId int
        Declare @PartWatch_HUBIPOByEmail varchar(200)
        Declare @SalesmanEmail varchar(200)
        Declare @PartWatch_HUBIPOByName varchar(200)
        Declare @SalesmanName varchar(200)
        Declare @Appurl varchar(100)
        Declare @BOMNo int

        select @Appurl = AppUrl
        from tbApplicationUrl
        where AppName = 'GT'


        select @custid = customerrequirementid,
               @custnum = customerrequirementnumber,
               @HubId = PartWatch_HUBIPOBy,
               @salesmanId = salesman,
               @partWatchClient = partwatch,
               @partWatchHub = partwatch_hubipo,
               @PartWatch_HUBIPOByEmail = PartWatch_HUBIPOByEmail,
               @SalesmanEmail = SalesmanEmail,
               @PartWatch_HUBIPOByName = PartWatch_HUBIPOByName,
               @SalesmanName = SalesmanName,
               @BOMNo = BOMNo
        from #tempUserMailId
        where SendFlag = 'N'
        order by customerrequirementnumber

        --select      
        --@custid,       
        --@custnum,      
        --@HubId,      
        --@salesmanId,      
        --@partWatchClient,      
        --@partWatchHub,      
        --@PartWatch_HUBIPOByEmail,      
        --@SalesmanEmail,      
        --@PartWatch_HUBIPOByName,      
        --@SalesmanName      

        Declare @body varchar(500)


        if (@partWatchClient = 1)
        begin
            --select 'send to client'      
            set @body
                = 'PartWatch Match Details: <a onclick="javascript:void(0);" target="_blank" href="Ord_CusReqDetail.aspx?req='
                  + convert(varchar(100), @custid) + '">PartWatch Match  (' + convert(varchar(100), @custid)
                  + ')</a><br/><br/>Hi,<br/>You have one partwatch match
 found for the requirement:' + convert(varchar(100), @custnum) + '<br/>Emailed to: ' + @SalesmanName

            insert into tbMailMessage
            (
                FromLoginNo,
                ToLoginNo,
                Subject,
                Body,
                DateSent,
                CompanyNo,
                UpdatedBy,
                DLUP,
                RecipientHasBeenNotified,
                HasBeenRead
            )
            values
            (0, @salesmanId, 'You have a new Partwatch Match', @body, getdate(), 0, @UpdatedBy, Getdate(), 1, 0)


            set @body
                = 'PartWatch Match Details: <a onclick="javascript:void(0);" target="_blank" href="' + @Appurl
                  + 'Ord_CusReqDetail.aspx?req=' + convert(varchar(100), @custid) + '">PartWatch Match  ('
                  + convert(varchar(100), @custid)
                  + ')</a><br/><br/>Hi,<br/>You have one part
 watch match found for the requirement  :' + convert(varchar(100), @custnum) + '<br/>Emailed to: ' + @SalesmanName

            --EXEC [msdb].dbo.sp_send_dbmail @profile_name = 'GTAdmin', -- 'GTServer'                
            --@recipients = '<EMAIL>',            
            --@subject = 'You have a new Partwatch Match',            
            --@body = @body,              
            ----@from_address = '<EMAIL>',            
            ----@reply_to = @SalesnameEmail,            
            --@body_format = 'HTML'            

            --select * from  tbEmailer      
            insert into tbEmailer
            (
                FromEmail,
                ToEmail,
                Subject,
                Body,
                DateSent,
                UpdatedBy,
                DLUP,
                process
            )
            values
            (null, @SalesmanEmail, 'You have a new Partwatch Match', @body, null, @UpdatedBy, GETDATE(), 0)



        end
        if (@partWatchHub = 1)
        begin
            --select 'send to Hub'      

            set @body
                = 'PartWatch Match Details: <a onclick="javascript:void(0);" target="_blank" href="Ord_BOMDetail.aspx?BOM='
                  + convert(varchar(100), @BOMNo) + '">PartWatch Match  (' + convert(varchar(100), @custid)
                  + ')</a><br/><br/>Hi,<br/>You have one partwatch match fou
nd for the requirement:' + convert(varchar(100), @custnum) + '<br/>Emailed to: ' + @PartWatch_HUBIPOByName

            insert into tbMailMessage
            (
                FromLoginNo,
                ToLoginNo,
                Subject,
                Body,
                DateSent,
                CompanyNo,
                UpdatedBy,
                DLUP,
                RecipientHasBeenNotified,
                HasBeenRead
            )
            values
            (0, @HubId, 'You have a new Partwatch Match', @body, getdate(), 0, @UpdatedBy, Getdate(), 1, 0)

            set @body
                = 'PartWatch Match Details: <a onclick="javascript:void(0);" target="_blank" href="' + @Appurl
                  + 'Ord_BOMDetail.aspx?BOM=' + convert(varchar(100), @BOMNo) + '">PartWatch Match  ('
                  + convert(varchar(100), @custid)
                  + ')</a><br/><br/>Hi,<br/>You have one part wat
ch match found for the requirement :' + convert(varchar(100), @custnum) + '<br/>Emailed to: ' + @SalesmanName


            --EXEC [msdb].dbo.sp_send_dbmail @profile_name = 'GTAdmin', -- 'GTServer'                
            --@recipients = @PartWatch_HUBIPOByEmail,            
            --@subject = 'You have a new Partwatch Match',            
            --@body = @body,              
            ----@from_address = '<EMAIL>',            
            ----@reply_to = @SalesnameEmail,            
            --@body_format = 'HTML'           

            insert into tbEmailer
            (
                FromEmail,
                ToEmail,
                Subject,
                Body,
                DateSent,
                UpdatedBy,
                DLUP,
                process
            )
            values
            (null, @PartWatch_HUBIPOByEmail, 'You have a new Partwatch Match', @body, null, @UpdatedBy, GETDATE(), 0)

        end


        update #tempUserMailId
        set SendFlag = 'Y'
        where customerrequirementnumber = @custnum
    end
/*Code started to send mail and notication to client for part availability*/



END

