﻿using GlobalTrader2.Dto.Base;

namespace GlobalTrader2.Dto.BOM
{
    public class GetBOMAssignRequest : PageRequestBase
    {
        public int ViewLevelList { get; set; }
        public string? HeaderOrDetail { get; set; }
        public string? Code { get; set; }
        public string? Name { get; set; }
        public int? Status { get; set; }
        public int? AssignedUser { get; set; }
        public int? Division { get; set; }
        public int? CompanyTypeId { get; set; }
        public int? SalesPersonId { get; set; }
        public int? SelectedClientId { get; set; }
        public bool? IsAS6081Tab { get; set; }
        public int? MyPageSize { get; set; }
    }
}
