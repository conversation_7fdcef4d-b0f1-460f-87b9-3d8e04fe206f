using System.Globalization;
using GlobalTrader2.Core.Helpers;
using GlobalTrader2.Dto.PurchaseRequisition;
using GlobalTrader2.Dto.SalesOrderLine;
using GlobalTrader2.Orders.UserCases.Orders.Product.Queries.GetProductStatusMessage;
using GlobalTrader2.Orders.UserCases.Orders.PurchaseRequisition.Queries;
using GlobalTrader2.Orders.UserCases.Orders.Quotes.Queries.GetQuoteLineDetails;
using GlobalTrader2.Orders.UserCases.Orders.Requirements.ManufacturerAdvisoryNote.Queries;
using GlobalTrader2.Orders.UserCases.Orders.Warehouse.Queries.CheckIsSoLineExistInSourcingResult;

namespace GlobalTrader2.Orders.UserCases.Orders.SOLine.Queries.GetSoLineDetails
{
    public class GetSoLineDetailsHandler(ISender _sender) : IRequestHandler<GetSoLineDetailsQuery, BaseResponse<GetSoLineDetailsDto>>
    {
        public async Task<BaseResponse<GetSoLineDetailsDto>> Handle(GetSoLineDetailsQuery request, CancellationToken cancellationToken)
        {
            var cultureInfo = CultureInfo.CurrentCulture;
            var lineDetails = await GetSoLineDetailsAsync(request.salesOrderLineId, cancellationToken);
            var solineDetailsDto = new GetSoLineDetailsDto();

            // Map core business information
            await MapCoreInformation(solineDetailsDto, lineDetails, request, cancellationToken);
            
            // Map product and financial details
            MapProductAndFinancialInfo(solineDetailsDto, lineDetails, cultureInfo);
            
            // Map status and company information
            MapStatusAndCompanyInfo(solineDetailsDto, lineDetails, cultureInfo);
            
            // Map compliance and regulatory information
            await MapComplianceAndRegulatoryInfo(solineDetailsDto, lineDetails, request, cancellationToken);

            return new BaseResponse<GetSoLineDetailsDto>()
            {
                Success = true,
                Data = solineDetailsDto
            };
        }

        private async Task MapCoreInformation(GetSoLineDetailsDto solineDetailsDto, SalesOrderLineDto lineDetails, GetSoLineDetailsQuery request, CancellationToken cancellationToken)
        {
            // Basic line information
            solineDetailsDto.IsSourcingResultExist = await _sender.Send(new CheckIsSoLineExistInSourcingResultQuery(lineDetails.SalesOrderNo, lineDetails.SalesOrderLineId), cancellationToken);
            solineDetailsDto.SONo = lineDetails.SalesOrderNo;
            solineDetailsDto.Part = lineDetails.Part ?? string.Empty;
            solineDetailsDto.Instructions = lineDetails.Instructions;
            solineDetailsDto.StockNo = lineDetails.StockNo;
            solineDetailsDto.ServiceNo = lineDetails.ServiceNo;
            solineDetailsDto.Taxable = lineDetails.Taxable;
            solineDetailsDto.SalesOrderNumber = lineDetails.SalesOrderNumber;
            solineDetailsDto.LineNotes = Functions.ReplaceLineBreaks(lineDetails.LineNotes);

            
            // Manufacturer information
            await MapManufacturerInfo(solineDetailsDto, lineDetails, request, cancellationToken);
            
            // Quantity properties
            MapQuantityProperties(solineDetailsDto, lineDetails);
            
            // Quote information
            await MapQuoteInfo(solineDetailsDto, lineDetails, cancellationToken);
        }

        private static void MapProductAndFinancialInfo(GetSoLineDetailsDto solineDetailsDto, SalesOrderLineDto lineDetails, CultureInfo cultureInfo)
        {
            // Product information
            solineDetailsDto.DateCd = lineDetails.DateCode ?? string.Empty;
            solineDetailsDto.PackageName = lineDetails.PackageName ?? string.Empty;
            solineDetailsDto.Package = lineDetails.PackageDescription ?? string.Empty;
            solineDetailsDto.PackageNo = lineDetails.PackageNo;
            solineDetailsDto.ProductName = lineDetails.ProductName ?? string.Empty;
            solineDetailsDto.Product = lineDetails.ProductDescription ?? string.Empty;
            solineDetailsDto.ProductNo = lineDetails.ProductNo;
            solineDetailsDto.CustomerPart = lineDetails.CustomerPart ?? string.Empty;
            MapDescription(solineDetailsDto, lineDetails);
            
            // Financial information
            solineDetailsDto.Quantity = lineDetails.Quantity;
            solineDetailsDto.Price = lineDetails.Price ?? string.Empty;
            solineDetailsDto.PriceVal = lineDetails.PriceVal ?? string.Empty;
            solineDetailsDto.CurrencyCode = lineDetails.CurrencyCode ?? string.Empty;
            solineDetailsDto.LineValue = Functions.FormatCurrency(0, cultureInfo, lineDetails.CurrencyCode, 5, false);
            solineDetailsDto.AveragePrice = lineDetails.AveragePrice ?? 0;
            solineDetailsDto.Cost = Functions.FormatCurrency(lineDetails.Cost, cultureInfo, "", 5, false);
            
            // Packaging information
            solineDetailsDto.Packaging = lineDetails.Packing ?? string.Empty;
            solineDetailsDto.PackagingSize = lineDetails.PackagingSize ?? string.Empty;
        }

        private static void MapStatusAndCompanyInfo(GetSoLineDetailsDto solineDetailsDto, SalesOrderLineDto lineDetails, CultureInfo cultureInfo)
        {
            // Date information
            solineDetailsDto.DatePromised = lineDetails.DatePromised;
            solineDetailsDto.RequiredDate = lineDetails.RequiredDate;
            solineDetailsDto.DatePromisedRawValue = lineDetails.DatePromisedRawValue;
            solineDetailsDto.DateRequiredRawValue = lineDetails.DateRequiredRawValue;
            solineDetailsDto.DeliveryDate = string.Empty;
            solineDetailsDto.DateConfirmed = Functions.FormatDate(lineDetails.DateConfirmed);
            solineDetailsDto.PoDelDate = Functions.FormatDate(lineDetails.PODeliveryDate);
            
            // Status flags
            solineDetailsDto.Closed = lineDetails.Closed;
            solineDetailsDto.Posted = lineDetails.Posted;
            solineDetailsDto.ShipASAP = lineDetails.ShipASAP;
            solineDetailsDto.IsAllocated = lineDetails.IsAllocated;
            solineDetailsDto.IsShipped = lineDetails.IsShipped;
            solineDetailsDto.Inactive = lineDetails.Inactive;
            solineDetailsDto.Status = lineDetails.QuantityShippedNumber < lineDetails.QuantityNumber ? "Open" : "Closed";
            
            // IPO flags
            solineDetailsDto.isIpoApproved = lineDetails.IPOApprovedBy <= 0;
            solineDetailsDto.IsIpoExist = Convert.ToBoolean(lineDetails.IsIPOCreated);
            solineDetailsDto.IsIpoOpen = Convert.ToBoolean(lineDetails.IsIPOAndPOOpen);
            solineDetailsDto.IsIpo = lineDetails.IsIPO ?? false;
            
            // Company information
            solineDetailsDto.CompanyNo = lineDetails.CompanyNo;
            solineDetailsDto.CompanyName = lineDetails.CompanyName ?? string.Empty;
            solineDetailsDto.SourcingResultUsedByOther = Convert.ToBoolean(lineDetails.SourcingResultUsedByOther);
            
            // Serial numbers
            solineDetailsDto.CloneSerialNo = Functions.FormatNumeric(lineDetails.SOSerialNumber, cultureInfo);
            solineDetailsDto.SOSerialNo = Functions.FormatNumeric(lineDetails.SOSerialNo, cultureInfo);
            
            // Customer requirements
            solineDetailsDto.CustomerRequirementId = lineDetails.CustomerRequirementId;
            solineDetailsDto.CustomerRequirementNumber = lineDetails.CustomerRequirementNumber;
        }

        private async Task MapComplianceAndRegulatoryInfo(GetSoLineDetailsDto solineDetailsDto, SalesOrderLineDto lineDetails, GetSoLineDetailsQuery request, CancellationToken cancellationToken)
        {
            // Product source and compliance codes
            solineDetailsDto.ProductSource = lineDetails.ProductSource;
            solineDetailsDto.ProductSourceName = lineDetails.ProductSourceName ?? string.Empty;
            solineDetailsDto.ProdInactive = lineDetails.ProductInactive;
            solineDetailsDto.DutyCodeAndRate = $"{lineDetails.ProductDutyCode} ({Functions.FormatNumeric(lineDetails.ProductDutyRate, 5, CultureInfo.CurrentCulture)})";
            solineDetailsDto.MSLLevel = lineDetails.MSLLevel ?? string.Empty;
            solineDetailsDto.ContractNo = lineDetails.ContractNo ?? string.Empty;
            solineDetailsDto.CountryOfOrigin = lineDetails.IHSCountryOfOrigin ?? string.Empty;
            solineDetailsDto.LifeCycleStage = lineDetails.LifeCycleStage ?? string.Empty;
            solineDetailsDto.HTSCode = lineDetails.HTSCode ?? string.Empty;
            solineDetailsDto.ECCNCode = lineDetails.ECCNCode ?? string.Empty;
            solineDetailsDto.PoECCNCode = lineDetails.PoECCNCode;
            solineDetailsDto.BlankECCNCode = lineDetails.BlankECCNCode;
            solineDetailsDto.IHSProduct = lineDetails.IHSProduct ?? string.Empty;
            solineDetailsDto.ROHS = lineDetails.ROHS;
            solineDetailsDto.AS6081 = lineDetails.AS6081;
            
            // Hazardous and restriction flags
            solineDetailsDto.IsProdHaz = lineDetails.IsProdHazardous;
            solineDetailsDto.IsPrintHaz = lineDetails.PrintHazardous ?? false;
            solineDetailsDto.IsOrderViaIpoOnly = lineDetails.IsOrderViaIPOonly;
            solineDetailsDto.IsRestrictedProduct = lineDetails.IsRestrictedProduct;
            
            // Product status messages
            await MapProductStatus(solineDetailsDto, lineDetails, cancellationToken);
            
            // Additional detail fields
            solineDetailsDto.IhsEccnsCodeDefination = Functions.ReplaceLineBreaks(lineDetails.IHSECCNCodeDefination);
            solineDetailsDto.StockAvailableDetail = Functions.ReplaceLineBreaks(lineDetails.StockAvailableDetail);


            // Request-specific properties
            solineDetailsDto.IsPoHub = Convert.ToBoolean(request.isPoHub);
            solineDetailsDto.ClientCurrency = request.clientCurrencyCode ?? string.Empty;
        }

        private async Task<SalesOrderLineDto> GetSoLineDetailsAsync(int salesOrderLineId, CancellationToken cancellationToken)
        {
            var soLineDetailsResponse = await _sender.Send(new GetSalesOrderLineQuery()
            {
                SalesOrderLineId = salesOrderLineId,
                CultureInfo = CultureInfo.CurrentCulture
            }, cancellationToken);

            return soLineDetailsResponse.Success && soLineDetailsResponse.Data != null 
                ? soLineDetailsResponse.Data 
                : new SalesOrderLineDto();
        }

        private async Task<string> GetManufacturerNotes(int? manufacturerId, int clientId, CancellationToken cancellationToken)
        {
            if (!manufacturerId.HasValue)
                return string.Empty;

            var response = await _sender.Send(new GetManufacturerAdvisoryNoteQuery()
            {
                ManufacturerId = manufacturerId.Value,
                ClientID = clientId
            }, cancellationToken);

            return response.Success && response.Data != null
                ? response.Data.AdvisoryNotes ?? string.Empty
                : string.Empty;
        }

        private async Task MapManufacturerInfo(GetSoLineDetailsDto dto, SalesOrderLineDto lineDetails, GetSoLineDetailsQuery request, CancellationToken cancellationToken)
        {
            var isRestrictedManufacturer = lineDetails.RestrictedMfrNo > 0 && !Convert.ToBoolean(lineDetails.RestrictedMfrInactive);
            
            dto.Mfr = isRestrictedManufacturer
                ? $@"<span style=""color: red !important;"">{lineDetails.ManufacturerName}</span>"
                : lineDetails.ManufacturerName ?? string.Empty;
                
            dto.MfrNo = lineDetails.ManufacturerNo;
            dto.MfrAdvisoryNotes = await GetManufacturerNotes(lineDetails.ManufacturerNo, request.clientId, cancellationToken);
        }

        private static void MapQuantityProperties(GetSoLineDetailsDto dto, SalesOrderLineDto lineDetails)
        {
            if (lineDetails.IsService)
            {
                dto.Allocated = lineDetails.Quantity;
                dto.Shipped = lineDetails.ServiceShipped ? lineDetails.Quantity : "0";
                dto.BackOrder = "0";
            }
            else
            {
                dto.Allocated = lineDetails.QuantityAllocated;
                dto.Shipped = lineDetails.QuantityShipped;
                dto.BackOrder = lineDetails.BackOrderQuantity;
            }
        }

        private async Task MapQuoteInfo(GetSoLineDetailsDto dto, SalesOrderLineDto lineDetails, CancellationToken cancellationToken)
        {
            if (lineDetails.QuoteLineNo > 0)
            {
                var getQuoteLineDetails = await _sender.Send(new GetQuoteLineDetailsQuery(lineDetails.QuoteLineNo ?? 0), cancellationToken);
                if (getQuoteLineDetails.Success && getQuoteLineDetails.Data != null)
                {
                    dto.QuoteId = getQuoteLineDetails.Data.QuoteNo;
                    dto.QuoteLineNo = getQuoteLineDetails.Data.QuoteLineId;
                    dto.QuoteNumber = getQuoteLineDetails.Data.QuoteNumber ?? 0;
                }
            }
            else
            {
                dto.QuoteId = 0;
                dto.QuoteLineNo = 0;
                dto.QuoteNumber = 0;
            }
        }

        private static void MapDescription(GetSoLineDetailsDto dto, SalesOrderLineDto lineDetails)
        {
            var description = lineDetails.Descriptions ?? string.Empty;
            dto.Descriptions = Functions.ReplaceLineBreaks(description);
            
            if (string.IsNullOrEmpty(description))
            {
                dto.DescShort = string.Empty;
            }
            else if (description.Length <= 10)
            {
                dto.DescShort = Functions.ReplaceLineBreaks(description);
            }
            else
            {
                dto.DescShort = Functions.ReplaceLineBreaks(description.Substring(0, 10));
            }
        }

        private async Task MapProductStatus(GetSoLineDetailsDto dto, SalesOrderLineDto lineDetails, CancellationToken cancellationToken)
        {
            var productStatusMessageDto = await _sender.Send(new GetProductStatusMessageQuery(
                lineDetails.ProductNo,
                lineDetails.IsProdHazardous,
                lineDetails.IsOrderViaIPOonly,
                lineDetails.ClientNo,
                lineDetails.IsRestrictedProduct), cancellationToken);

            if (productStatusMessageDto != null)
            {
                dto.ProductMessage = Functions.ReplaceLineBreaks(productStatusMessageDto.ProductMessage);
                dto.MsgHazardous = Functions.ReplaceLineBreaks(productStatusMessageDto.HazardousMsg);
                dto.MsgIpo = Functions.ReplaceLineBreaks(productStatusMessageDto.IopMsg);
                dto.MsgRestricted = Functions.ReplaceLineBreaks(productStatusMessageDto.RestrictedMsg);
            }
            else
            {
                dto.ProductMessage = string.Empty;
                dto.MsgHazardous = string.Empty;
                dto.MsgIpo = string.Empty;
                dto.MsgRestricted = string.Empty;
            }
        }
    }
}