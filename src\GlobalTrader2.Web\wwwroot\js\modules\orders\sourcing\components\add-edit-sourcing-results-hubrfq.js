﻿import { PageSearchSelectComponent } from '../../../../components/search-select/page-search-select.component.js?v=#{BuildVersion}#';
import { ManufactureSearchSelectComponent } from '../../../orders/requirements/components/search-selects/manufacture-search-select.component.js?v=#{BuildVersion}#';
import { ProductSearchSelectComponent } from '../../../orders/requirements/components/search-selects/product-search-select.component.js?v=#{BuildVersion}#';
import { SupplierSearchSelectComponent } from '../components/search-selects/supplier-search-select.component.js?v=#{BuildVersion}#';
import { IhsHelper } from "../../../../helper/ihs-helper.js?v=#{BuildVersion}#";
import { ButtonHelper } from "../../../../helper/button-helper.js?v=#{BuildVersion}#";

const save = window.localizedStrings.save;
const cancel = window.localizedStrings.cancel;
const maxLengthMessage = window.localizedStrings.rangelength;
const requiredErrorMessage = window.localizedStrings.requiredField;
const saveChangedMessage = window.localizedStrings.saveChangedMessage;
const defaultIdValue = "0";
const pleaseCheckBelowAndTryAgain = window.localizedStrings.pleaseCheckBelowAndTryAgain;
const thereWereSomeProblemsWithYourForm = window.localizedStrings.thereWereSomeProblemsWithYourForm;
const apiEndpoints = {
    currency: "/lists/buy-currencies/",
    rohs: "/lists/rohs-statuses",
    msl: "lists/msls",
    offerStatus: "offer-statuses/dropdown",
    region: "lists/regions",
    countryOfOrigin: "lists/global-country",
    countryWarning: "lists/country-warning-message/",
    companyPurchasingInfo: "lists/company-purchasing-info/",
    typeOfSupplier: "setup/global-settings/as6081-type-of-supplier",
    reasonForChosenSupplier: "setup/global-settings/as6081-reason-for-chosen-supplier",
    riskOfSupplier: "setup/global-settings/as6081-risk-of-supplier",
    countryDetails: "lists/country-details/",
    location: "lists/client-countries",
};

const maxIntegerValue = window.constants.MaxIntegerValue;

let isSubmitting = false;
let sourcingResultsPackageSearchSelect;
let sourcingResultsProductSearchSelect;
let sourcingResultsSupplierSearchSelect;
let sourcingResultsManufacturerSearchSelect;

let companyId = defaultIdValue;

let shouldSelectSupplierDefaultBuyCurrency = false;

let dataDetailRequirement = null;
let upliftSellPrice = 0;
let inHouseAS6081;
const header = { "RequestVerificationToken": $('input[name="__RequestVerificationToken"]').val() };

const option = {
    minDate: 0,
    dateFormat: "dd/mm/yy",
};

$(() => {
    $("#addSourcingResultQuoteToClient").button().on("click", async function (event) {
        event.stopPropagation();

        $("#add-edit-sourcing-results-hubrfq-dialog").dialog("open");
        $("#add-edit-sourcing-results-hubrfq-title").text(sourcingResultsString.addSourcingResults);
        $("#add-edit-sourcing-results-hubrfq-message").text(sourcingResultsString.addSourcingResultsMessage);
        await reloadCurrencyDropdown();

        $("#add-edit-sourcing-results-hubrfq-form #label-supplier-container").addClass("d-none");
        $("#add-edit-sourcing-results-hubrfq-form #supplier-container").removeClass("d-none");
        $("#add-edit-sourcing-results-hubrfq-form #part-watch-wrapper").addClass("d-none");

        fetchDropdownsAsync();

        await fetchAddSourcingResults();

        $('#add-edit-sourcing-results-hubrfq-form #uplift-sell-price').on('input', function () {
            checkPrices()
        });
        $('#add-edit-sourcing-results-hubrfq-form #buy-price').on('input', function () {
            caculateUpliftSellPrice();
            checkPrices()
        });

        inHouseAS6081 = $("#inhouse-as6081").text();
        if (inHouseAS6081 !== 'No') {
            $("#type-of-supplier-container").removeClass("d-none");
            $("#reason-for-chosen-container").removeClass("d-none");
            $("#risk-of-supplier-container").removeClass("d-none");
            $("#location-container").removeClass("d-none");
        }

        $("#add-edit-sourcing-results-hubrfq-form #manufacturer-text").addClass("d-none");
        $("#add-edit-sourcing-results-hubrfq-form #product-text").addClass("d-none");
        $("#add-edit-sourcing-results-hubrfq-form #package-text").addClass("d-none");

        $("#delivery-date").datepicker2(option);
    });

    $("#editSourcingResultQuoteToClient").button().on("click", async function (event) {
        event.stopPropagation();

        $("#add-edit-sourcing-results-hubrfq-dialog").dialog("open");

        const currentDialog = $("#add-edit-sourcing-results-hubrfq-dialog").dialog("instance");
        currentDialog.setLoading(true);

        $("#add-edit-sourcing-results-hubrfq-title").text(sourcingResultsString.editSourcingResults);
        $("#add-edit-sourcing-results-hubrfq-message").text(sourcingResultsString.editSourcingResultsMessage);

        await fetchDropdownsAsync();

        await fetchSourcingResults();

        $('#add-edit-sourcing-results-hubrfq-form #uplift-sell-price').on('input', function () {
            checkPrices()
        });
        $('#add-edit-sourcing-results-hubrfq-form #buy-price').on('input', function () {
            caculateUpliftSellPrice();
            checkPrices()
        });

        inHouseAS6081 = $("#inhouse-as6081").text();
        if (inHouseAS6081 !== 'No') {
            $("#type-of-supplier-container").removeClass("d-none");
            $("#reason-for-chosen-container").removeClass("d-none");
            $("#risk-of-supplier-container").removeClass("d-none");
            $("#location-container").removeClass("d-none");
        }

        $("#delivery-date").datepicker2(option);
    });

    $("#coo-dropdown").on("change", async function () {
        if (this.value) {
            const response = await GlobalTrader.ApiClient.getAsync(
                `${apiEndpoints.countryWarning}${this.value}`
            );
            if (response?.data?.length > 0) {
                $("#coo-warning").html(IhsHelper.showIHSECCNCodeDefi(
                    '',
                    response.data
                ));
            }
            else {
                $("#coo-warning").html('');
            }
        }
    });

    $('#add-edit-sourcing-results-hubrfq-form').validate({
        onfocusout: false,
        onkeyup: false,
        onclick: false,
        ignore: [],
        rules: {
            SupplierNo: {
                notEmptyOrWhiteSpace: true,
            },
            PartNoKeyword: {
                notEmptyOrWhiteSpace: true,
                maxlength: 30
            },
            ManufacturerNo: {
                notEmptyOrWhiteSpace: true,
            },
            ProductNo: {
                notEmptyOrWhiteSpace: true,
            },
            Quantity: {
                notEmptyOrWhiteSpace: true,
                integerRangeCheck: {
                    min: 0, max: maxIntegerValue
                }
            },
            SupplierMOQ: {
                integerRangeCheck: {
                    min: 0, max: maxIntegerValue
                }
            },
            SupplierTotalQSA: {
                integerRangeCheck: {
                    min: 0, max: maxIntegerValue
                }
            },
            CurrencyNo: {
                notEmptyOrWhiteSpace: true,
                min: 1
            },
            BuyPrice: {
                notEmptyOrWhiteSpace: true,
                decimalRangeCheck: {
                    min: `0.00000`, max: `100000000000000000.00000`, decimalPartLength: 5
                }
            },
            UPLiftPrice: {
                notEmptyOrWhiteSpace: true,
                decimalRangeCheck: {
                    min: `0.00000`, max: `100000000000000000.00000`, decimalPartLength: 5
                }
            },
            EstimatedShippingCostValue: {
                decimalRangeCheck: {
                    min: `0.00000`, max: `100000000000000000.00000`, decimalPartLength: 5
                }
            },
            SupplierWarranty: {
                required: function () {
                    return !$("#warranty-required").hasClass("d-none");
                },
                integerRangeCheck: {
                    min: 0, max: maxIntegerValue
                }
            },
            CountryNo: {
                required: function () {
                    return inHouseAS6081 !== 'No';
                },
                min: {
                    param: 1,
                    depends: function () {
                        return inHouseAS6081 !== 'No';
                    }
                }
            }
        },
        messages: {
            SupplierNo: {
                notEmptyOrWhiteSpace: requiredErrorMessage,
            },
            PartNoKeyword: {
                notEmptyOrWhiteSpace: requiredErrorMessage,
                maxlength: maxLengthMessage,
            },
            ManufacturerNo: {
                notEmptyOrWhiteSpace: requiredErrorMessage,
            },
            ProductNo: {
                notEmptyOrWhiteSpace: requiredErrorMessage,
            },
            Quantity: {
                notEmptyOrWhiteSpace: requiredErrorMessage,
            },
            CurrencyNo: {
                notEmptyOrWhiteSpace: requiredErrorMessage,
                min: requiredErrorMessage
            },
            BuyPrice: {
                notEmptyOrWhiteSpace: requiredErrorMessage,
            },
            UPLiftPrice: {
                notEmptyOrWhiteSpace: requiredErrorMessage,
            },
            SupplierWarranty: {
                required: requiredErrorMessage,
            },
            CountryNo: {
                required: requiredErrorMessage,
                min: requiredErrorMessage
            },
            SupplierMOQ: {
            },
            SupplierTotalQSA: {
            },
            EstimatedShippingCostValue: {
            }
        },
        invalidHandler: function (event, validator) {
            displayError("");
        },
        errorPlacement: function (error, element) {
            error.appendTo(element.parent().parent());
        },
    });

    const dialogAddEditSourcingResultsForm = $("#add-edit-sourcing-results-hubrfq-dialog").dialog({
        autoOpen: false,
        maxHeight: $(window).height(),
        modal: true,
        width: "40vw",
        height: "auto",
        create: function (event, ui) {
            const dialog = $(this).dialog("widget");
            dialog.css("maxWidth", "800px");
        },
        buttons: [
            {
                text: "Save",
                click: async function () {
                    if (isSubmitting == true)
                        return;
                    const currentDialog = $("#add-edit-sourcing-results-hubrfq-dialog").dialog("instance");
                    const isValid = $("#add-edit-sourcing-results-hubrfq-form").valid();

                    if (!isValid) {
                        focusFirstErrorInput();
                        return;
                    }

                    const formObject = getFormData();
                    delete formObject.IsTestingRecommended;
                    let isChecked = $('#testing-recommended-checkbox-input').is(':checked');
                    formObject.isTestingRecommended = isChecked ? true : false;

                    isSubmitting = true;
                    currentDialog.setLoading(true);

                    const sourcingResultsId = $("#SourcingResultsId").val();
                    let response;

                    if (sourcingResultsId == "") {
                        response = await GlobalTrader.ApiClient.postAsync("orders/bom/sourcing-results-add", formObject, header);
                        isSubmitting = false;
                    }
                    else {
                        response = await GlobalTrader.ApiClient.putAsync(`/orders/bom/sourcing-results/${sourcingResultsId}`, formObject, header);
                        isSubmitting = false;
                    }

                    if (response.success && !response.message) {
                        $("#SourcingResultsId").val("");
                        dialogAddEditSourcingResultsForm.dialog("close");
                        currentDialog.setLoading(false);
                        showToast('success', saveChangedMessage);
                        $("#customer-requrement-sourcing-results-box .section-box-refesh-button").trigger("click");
                    }
                    else {
                        currentDialog.setLoading(false);
                        showToast("danger", response.message);
                        displayError("");
                    }

                }
            },
            {
                text: "Cancel",
                click: function () {
                    $(this).dialog("close");
                }
            }
        ],
        close: function (event, ui) {
            $("#add-edit-sourcing-results-hubrfq-form")[0].reset();
            $("#add-edit-sourcing-results-hubrfq-form").validate().resetForm();
            $("#add-edit-sourcing-results-hubrfq-form-error-summary").hide();

            $("#sourcing-results-currency-code").addClass("d-none");
            companyId = defaultIdValue;

            $("#SourcingResultsSupplierId").removeClass("is-invalid");
            $("#SourcingResultsSupplierId-error").remove();
            $("#SourcingResultsPartNo").removeClass("is-invalid");
            $("#SourcingResultsPartNo-error").remove();

            resetSearchSelects();
            resetDropDowns();

            shouldSelectSupplierDefaultBuyCurrency = false;
        },
        open: function (event, ui) {
            $(this).removeClass('d-none');
            $('.ui-dialog-titlebar-close').css('display', 'none');

            $("#add-edit-sourcing-results-hubrfq-form #currency-dropdown").empty();
            $("#add-edit-sourcing-results-hubrfq-form #currency-dropdown").append(`<option value="0">Select...</option>`)
        },
    });

    initSearchSelects();
    initDropdowns();
    allowPositiveIntegerInput('#add-edit-sourcing-results-hubrfq-form input[id="Quantity"]');
    allowPositiveDecimalInput('#add-edit-sourcing-results-hubrfq-form input[id="Price"]', true, 5);

    $('#add-edit-sourcing-results-hubrfq-form input[id="Price"]').on("focusout", function () {
        if ($(this).val() === "") {
            $(this).val(0);
        }
    });

    $("#sourcing-results-part-no-auto-search").on("input", function () {
        $(this).val($(this).val().toUpperCase());
    });

    $(`.ui-dialog-buttonpane .ui-dialog-buttonset button:contains('${save}')`).addClass("btn btn-primary fw-normal").html(`<img src="/img/icons/save.svg" alt="${save}"> ${save}`);
    $(`.ui-dialog-buttonpane .ui-dialog-buttonset button:contains('${cancel}')`).addClass("btn btn-danger fw-normal").html(`<img src="/img/icons/slash.svg" alt="${cancel}"> ${cancel}`);

});

function displayError(message) {
    const errorContainer = $("#add-edit-sourcing-results-hubrfq-form-error-summary");
    errorContainer.find("div").html(`<p>${thereWereSomeProblemsWithYourForm}</p><p>${pleaseCheckBelowAndTryAgain}</p><p>${message}</p>`);
    errorContainer.show();
}

function resetDropDowns() {
    $("#add-edit-sourcing-results-hubrfq-form").find("#rohs-dropdown").val(0).trigger("change");
    $("#add-edit-sourcing-results-hubrfq-form").find("#msl-dropdown").val(0).trigger("change");
    $("#add-edit-sourcing-results-hubrfq-form").find("#offer-status-dropdown").val(0).trigger("change");
}

function getFormData() {
    const formData = $("#add-edit-sourcing-results-hubrfq-form").serializeArray();
    let formObject = {};

    formData.forEach(function (field) {
        if (formObject.hasOwnProperty(field.name)) {
            return;
        }

        if (field.name === "MslLevelNo") {
            formObject[field.name] = field.value === defaultIdValue ? null : field.value;
            return;

        }

        if (field.name === "OfferStatusNo") {
            formObject[field.name] = field.value === defaultIdValue ? null : field.value;
            return;
        }

        if (field.name === "ROHS") {
            formObject[field.name] = field.value === "" ? null : field.value;
            return;
        }

        if (field.value.trim() != "") {
            formObject[field.name] = field.value.trim();
        }

        if (field.name === "DeliveryDate") {
            if (field.value.trim() === "") {
                formObject[field.name] = null;
            } else {
                const parts = field.value.split('/');
                if (parts.length === 3) {
                    const day = parts[0];
                    const month = parts[1];
                    const year = parts[2];

                    const formattedDate = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
                    formObject[field.name] = formattedDate;
                } else {
                    formObject[field.name] = null;
                }
            }
            return;
        }


    });
    if (dataDetailRequirement) {
        formObject["customerRequirementNo"] = dataDetailRequirement.customerRequirementId;
    }

    return formObject;
}

function initDropdowns() {
    $("#add-edit-sourcing-results-hubrfq-form #currency-dropdown").dropdown({
        serverside: false,
        deferLoad: true,
        endpoint: apiEndpoints.companyPurchasingInfo + defaultIdValue,
        valueKey: 'currencyId',
        textKey: 'currencyValue',
        handleResponse: (res) => {
            if (shouldSelectSupplierDefaultBuyCurrency && res.data?.selected) {
                $("#add-edit-sourcing-results-hubrfq-form #currency-dropdown").dropdown("select", res.data.selected ?? 0);
            }

            if (res.data?.selected) {
                $("#sourcing-results-currency-code").text(res.data.data.find(x => x.currencyId === res.data.selected)?.currencyCode);
                $("#sourcing-results-currency-code").removeClass("d-none");
                $("#message-currency").addClass("d-none");

                return res.data.data;

            } else if (res.data?.data && res.data?.data.length > 0) {
                $("#sourcing-results-currency-code").text("");
                $("#sourcing-results-currency-code").addClass("d-none");
                $("#message-currency").removeClass("d-none");

                if (!res.data?.selected) {
                    $("#add-edit-sourcing-results-hubrfq-form").find("#currency-dropdown").val(0).trigger("change");
                }

                return res.data.data;

            } else {
                $("#sourcing-results-currency-code").text("");
                $("#sourcing-results-currency-code").addClass("d-none");
                $("#message-currency").addClass("d-none");

                return [];
            }
        },
        isHideRefresButton: true,
        isCacheApplied: false,
        refreshButton: $("#add-edit-sourcing-results-hubrfq-form #CurrencyNo-refresh-btn")
    });

    $("#add-edit-sourcing-results-hubrfq-form #offer-status-dropdown").dropdown({
        serverside: false,
        deferLoad: true,
        endpoint: apiEndpoints.offerStatus,
        valueKey: 'offerStatusId',
        textKey: 'name',
        isHideRefresButton: true,
        isCacheApplied: false,
        refreshButton: $("#add-edit-sourcing-results-hubrfq-form #OfferStatusNo-dropdown-refresh-btn")
    });

    $("#add-edit-sourcing-results-hubrfq-form #rohs-dropdown").dropdown({
        serverside: false,
        deferLoad: true,
        endpoint: apiEndpoints.rohs,
        valueKey: 'rohsStatusId',
        textKey: 'description',
        isHideRefresButton: true,
        isCacheApplied: false,
        refreshButton: $("#add-edit-sourcing-results-hubrfq-form #Rohs-refresh-btn")
    });

    $("#add-edit-sourcing-results-hubrfq-form #CurrencyNo-refresh-button").on("click", () => {
        if (companyId === defaultIdValue) {
            return;
        }

        shouldSelectSupplierDefaultBuyCurrency = false;

        $("#add-edit-sourcing-results-hubrfq-form #currency-dropdown").dropdown("refresh");
    });

    $("#add-edit-sourcing-results-hubrfq-form #msl-dropdown").dropdown({
        serverside: false,
        deferLoad: true,
        endpoint: apiEndpoints.msl,
        isHideRefresButton: true,
        isCacheApplied: false,
        refreshButton: $("#add-edit-sourcing-results-hubrfq-form #MslLevelNo-refresh-btn")
    });

    $("#add-edit-sourcing-results-hubrfq-form #region-dropdown").dropdown({
        serverside: false,
        deferLoad: true,
        endpoint: apiEndpoints.region,
        isHideRefresButton: true,
        isCacheApplied: false,
        refreshButton: $("#add-edit-sourcing-results-hubrfq-form #region-no-refresh-button")
    });

    $("#add-edit-sourcing-results-hubrfq-form #coo-dropdown").dropdown({
        serverside: false,
        deferLoad: true,
        endpoint: apiEndpoints.countryOfOrigin,
        isHideRefresButton: true,
        isCacheApplied: false,
        refreshButton: $("#add-edit-sourcing-results-hubrfq-form #coo-refresh-button")
    });

    $("#add-edit-sourcing-results-hubrfq-form #type-of-supplier-dropdown").dropdown({
        serverside: false,
        deferLoad: true,
        endpoint: apiEndpoints.typeOfSupplier,
        valueKey: 'typeOfSupplierId',
        textKey: 'name',
        isHideRefresButton: true,
        isCacheApplied: false,
        refreshButton: $("#add-edit-sourcing-results-hubrfq-form #type-of-supplier-refresh-button")
    });

    $("#add-edit-sourcing-results-hubrfq-form #reason-for-chosen-dropdown").dropdown({
        serverside: false,
        deferLoad: true,
        endpoint: apiEndpoints.reasonForChosenSupplier,
        valueKey: 'reasonForChosenSupplierId',
        textKey: 'name',
        isHideRefresButton: true,
        isCacheApplied: false,
        refreshButton: $("#add-edit-sourcing-results-hubrfq-form #reason-for-chosen-refresh-button")
    });

    $("#add-edit-sourcing-results-hubrfq-form #risk-of-supplier-dropdown").dropdown({
        serverside: false,
        deferLoad: true,
        endpoint: apiEndpoints.riskOfSupplier,
        valueKey: 'riskOfSupplierId',
        textKey: 'name',
        isHideRefresButton: true,
        isCacheApplied: false,
        refreshButton: $("#add-edit-sourcing-results-hubrfq-form #risk-of-supplier-refresh-button")
    });

    $("#add-edit-sourcing-results-hubrfq-form #location-dropdown").dropdown({
        serverside: false,
        deferLoad: true,
        endpoint: apiEndpoints.riskOfSupplier,
        valueKey: 'countryId',
        textKey: 'countryName',
        isHideRefresButton: true,
        isCacheApplied: false,
        refreshButton: $("#add-edit-sourcing-results-hubrfq-form #location-refresh-button")
    });

    setupDropdownRefresh("#add-edit-sourcing-results-hubrfq-form #OfferStatusNo-refresh-button", "#add-edit-sourcing-results-hubrfq-form #offer-status-dropdown", apiEndpoints.offerStatus, { refreshData: true }, "offerStatusId", "name");
    setupDropdownRefresh("#add-edit-sourcing-results-hubrfq-form #Rohs-refresh-button", "#add-edit-sourcing-results-hubrfq-form #rohs-dropdown", apiEndpoints.rohs, { refreshData: true }, "rohsStatusId", "description");
    setupDropdownRefresh("#add-edit-sourcing-results-hubrfq-form #MslLevelNo-refresh-button", "#add-edit-sourcing-results-hubrfq-form #msl-dropdown", apiEndpoints.msl, { refreshData: true }, "id", "name");
    setupDropdownRefresh("#add-edit-sourcing-results-hubrfq-form #region-no-refresh-button", "#add-edit-sourcing-results-hubrfq-form #region-dropdown", apiEndpoints.region, { refreshData: true }, "id", "name");
    setupDropdownRefresh("#add-edit-sourcing-results-hubrfq-form #coo-refresh-button", "#add-edit-sourcing-results-hubrfq-form #coo-dropdown", apiEndpoints.countryOfOrigin, { refreshData: true }, "id", "name");
    setupDropdownRefresh("#add-edit-sourcing-results-hubrfq-form #type-of-supplier-button", "#add-edit-sourcing-results-hubrfq-form #type-of-supplier-dropdown", apiEndpoints.typeOfSupplier, { refreshData: true }, "typeOfSupplierId", "name");
    setupDropdownRefresh("#add-edit-sourcing-results-hubrfq-form #reason-for-chosen-refresh-button", "#add-edit-sourcing-results-hubrfq-form #reason-for-chosen-dropdown", apiEndpoints.reasonForChosenSupplier, { refreshData: true }, "reasonForChosenSupplierId", "name");
    setupDropdownRefresh("#add-edit-sourcing-results-hubrfq-form #risk-of-supplier-refresh-button", "#add-edit-sourcing-results-hubrfq-form #risk-of-supplier-dropdown", apiEndpoints.riskOfSupplier, { refreshData: true }, "riskOfSupplierId", "name");
    setupDropdownRefresh("#add-edit-sourcing-results-hubrfq-form #location-refresh-button", "#add-edit-sourcing-results-hubrfq-form #location-dropdown", apiEndpoints.location, { refreshData: true }, "countryId", "countryName");
}

async function fetchDropdownsAsync() {
    const rohsPromise = $("#add-edit-sourcing-results-hubrfq-form #rohs-dropdown").dropdown("reload", apiEndpoints.rohs, { refreshData: false });
    const mslDropdownPromise = $("#add-edit-sourcing-results-hubrfq-form #msl-dropdown").dropdown("reload", apiEndpoints.msl, { refreshData: false });
    const offerStatusDropdownPromise = $("#add-edit-sourcing-results-hubrfq-form #offer-status-dropdown").dropdown("reload", apiEndpoints.offerStatus, { refreshData: false });
    const region = $("#add-edit-sourcing-results-hubrfq-form #region-dropdown").dropdown("reload", apiEndpoints.region, { refreshData: false });
    const countryOfOrigin = $("#add-edit-sourcing-results-hubrfq-form #coo-dropdown").dropdown("reload", apiEndpoints.countryOfOrigin, { refreshData: false });
    const typeOfSupplier = $("#add-edit-sourcing-results-hubrfq-form #type-of-supplier-dropdown").dropdown("reload", apiEndpoints.typeOfSupplier, { refreshData: false });
    const reasonForChosen = $("#add-edit-sourcing-results-hubrfq-form #reason-for-chosen-dropdown").dropdown("reload", apiEndpoints.reasonForChosenSupplier, { refreshData: false });
    const riskOfSupplier = $("#add-edit-sourcing-results-hubrfq-form #risk-of-supplier-dropdown").dropdown("reload", apiEndpoints.riskOfSupplier, { refreshData: false });
    const location = $("#add-edit-sourcing-results-hubrfq-form #location-dropdown").dropdown("reload", apiEndpoints.location, { refreshData: false });

    await Promise.all([
        rohsPromise,
        mslDropdownPromise,
        offerStatusDropdownPromise,
        region,
        countryOfOrigin,
        typeOfSupplier,
        reasonForChosen,
        riskOfSupplier,
        location
    ]);

    //NOTE: this is needed because rohs-dropdown options has value 0, also default "Select..." has value 0
    $("#add-edit-sourcing-results-hubrfq-form #rohs-dropdown option:first").attr("value", "");
}

async function loadCountryDetails() {
    const sourcingResultsId = $("#SourcingResultsId").val();
    if (sourcingResultsId != '') return;

    if (companyId === defaultIdValue) {
        return;
    }
    const response = await GlobalTrader.ApiClient.getAsync(
        `${apiEndpoints.countryDetails}${companyId}`
    );
    if (response.data.countryNo) {
        $("#add-edit-sourcing-results-hubrfq-form #location-dropdown").dropdown("select", response.data.countryNo);
    }
}

async function reloadCurrencyDropdown() {
    if (companyId === defaultIdValue) {
        return;
    }

    const currencyPromise = $("#add-edit-sourcing-results-hubrfq-form #currency-dropdown").dropdown("reload", apiEndpoints.companyPurchasingInfo + companyId, { refreshData: true });
    await Promise.all([currencyPromise]);
    await handleShowCurrency(companyId);
}

async function handleShowCurrency(companyId) {
    const response = await GlobalTrader.ApiClient.getAsync(
        `${apiEndpoints.companyPurchasingInfo}${companyId}`
    );
    if (response.data.currency) {
        $("#add-edit-sourcing-results-hubrfq-form #sourcing-results-buy-price").text(response.data.currency);
        $("#add-edit-sourcing-results-hubrfq-form #sourcing-results-uplift-sell-price").text(renderUpliftSellPrice(response.data));
    }
    else {
        $("#sourcing-results-buy-price").text('');
        $("#sourcing-results-uplift-sell-price").text('');
    }
    if (response.data.uPLiftPrice) {
        upliftSellPrice = response.data.uPLiftPrice;
    }
    if (response.data.estShippingCost) {
        $("#add-edit-sourcing-results-hubrfq-form #estimated-ship-cost").val(response.data.estShippingCost.toFixed(5));
    }
    if (response.data.supplierWarranty) {
        $("#add-edit-sourcing-results-hubrfq-form #supplier-warranty").val(response.data.supplierWarranty);
    }
    if (response.data.nonPreferredCompany) {
        $("#warranty-required").removeClass("d-none");
        $("#warranty-required").addClass("required");
    }
    else {
        $("#warranty-required").addClass("d-none");
        $("#warranty-required").removeClass("required");
    }
    setTimeout(() => {
        if ($("#add-edit-sourcing-results-hubrfq-form #currency-dropdown")[0].length == 1) {
            $("#no-currency-message").removeClass("d-none");
            $("#currency-select").addClass("d-none");
        }
        else {
            $("#no-currency-message").addClass("d-none");
            $("#currency-select").removeClass("d-none");
            $("#add-edit-sourcing-results-hubrfq-form #currency-dropdown").dropdown("select", response.data.data[0].currencyId);
        }
    }, 200);
}

function renderUpliftSellPrice(company) {
    if (!company)
        return;
    return company.uPLiftPrice ? `${company.currency} ( ${company.uPLiftPrice}% )` : `${company.currency} ( 0% )`
}

function initSearchSelects() {
    sourcingResultsPackageSearchSelect = new PageSearchSelectComponent('quote-to-client-package-auto-search', 'QuoteToClientPackageId', 'single', 'keyword', '/packages/auto-search');
    sourcingResultsProductSearchSelect = new ProductSearchSelectComponent('quote-to-client-product-auto-search', 'QuoteToClientProductId', 'single', 'keyword', '/products/auto-search');
    sourcingResultsManufacturerSearchSelect = new ManufactureSearchSelectComponent('quote-to-client-manufacturer-auto-search', 'QuoteToClientManufacturerId', 'single', 'keyword', '/manufacturers/auto-search');
    sourcingResultsSupplierSearchSelect = new SupplierSearchSelectComponent('quote-to-client-supplier-auto-search', 'QuoteToClientSupplierId', 'single', 'keyword', '/companies/auto-search');
    sourcingResultsSupplierSearchSelect.on(
        "change", async ({ _, hiddenValue }) => {
            const currentCompanyId = companyId;
            companyId = hiddenValue || companyId;

            if (hiddenValue == '') {
                $("#sourcing-results-currency-code").addClass("d-none");
                $("#sourcing-results-currency-code").text("");
                $("#message-currency").addClass("d-none");
            }
            else if (hiddenValue != '' && companyId == currentCompanyId) {
                await reloadCurrencyDropdown();
                if (inHouseAS6081 !== 'No') {
                    await loadCountryDetails();
                }
            }

            if (companyId != currentCompanyId && companyId != defaultIdValue) {
                shouldSelectSupplierDefaultBuyCurrency = true;

                await reloadCurrencyDropdown();
                if (inHouseAS6081 !== 'No') {
                    await loadCountryDetails();
               
                }            }
    });
}

function resetSearchSelects() {
    sourcingResultsPackageSearchSelect.resetSearchSelect();
    sourcingResultsProductSearchSelect.resetSearchSelect();
    sourcingResultsManufacturerSearchSelect.resetSearchSelect();
    sourcingResultsSupplierSearchSelect.resetSearchSelect();
}

async function fetchSourcingResults() {
    const selectedRowSourcingResults = $("#customer-requirement-sourcing-results-table")
        .DataTable()
        .row({ selected: true })
        .data();
    if (!selectedRowSourcingResults) return;

    const response = await GlobalTrader.ApiClient.getAsync(
        `/orders/bom/sourcing-results/${selectedRowSourcingResults.id}`
    );
    if (!response?.success) {
        $("#add-edit-sourcing-results-hubrfq-dialog").dialog("close");
        return;
    }

    $("#SourcingResultsId").val(selectedRowSourcingResults.id);

    await handleSupplierInfo(response.data);
    await selectSourcingResultsPart(response.data.part);
    handleManufacturerInfo(response.data);
    handleProductInfo(response.data);
    handlePackageInfo(response.data);

    $("#add-edit-sourcing-results-hubrfq-form #rohs-dropdown").dropdown("select", response.data.rohs);
    $("#add-edit-sourcing-results-hubrfq-form #offer-status-dropdown").dropdown("select", response.data.offerStatusNo);
    $("#add-edit-sourcing-results-hubrfq-form #msl-dropdown").dropdown("select", response.data.mslLevelNo);
    $("#add-edit-sourcing-results-hubrfq-form #type-of-supplier-dropdown").dropdown("select", response.data.typeOfSupplierNo);
    $("#add-edit-sourcing-results-hubrfq-form #reason-for-chosen-dropdown").dropdown("select", response.data.reasonForSupplierNo);
    $("#add-edit-sourcing-results-hubrfq-form #risk-of-supplier-dropdown").dropdown("select", response.data.riskOfSupplierNo);
    $("#add-edit-sourcing-results-hubrfq-form #coo-dropdown").dropdown("select", response.data.countryOfOriginNo);
    $("#add-edit-sourcing-results-hubrfq-form #region-dropdown").dropdown("select", response.data.regionNo);
    $("#add-edit-sourcing-results-hubrfq-form #currency-dropdown").dropdown("select", response.data.currencyNo);

    $("#add-edit-sourcing-results-hubrfq-form #DateCode").val(response.data.dateCode);
    $("#add-edit-sourcing-results-hubrfq-form #Notes").val(response.data.notes);
    $("#add-edit-sourcing-results-hubrfq-form #SPQ").val(response.data.spq);
    $("#add-edit-sourcing-results-hubrfq-form #factory-sealed").val(response.data.factorySealed);
    $("#add-edit-sourcing-results-hubrfq-form #total-quantity").val(response.data.supplierTotalQSA);
    $("#add-edit-sourcing-results-hubrfq-form #moq").val(response.data.supplierMOQ);
    $("#add-edit-sourcing-results-hubrfq-form #ltb").val(response.data.supplierLTB);
    $("#add-edit-sourcing-results-hubrfq-form #buy-price").val(response.data.price.toFixed(5));
    $("#add-edit-sourcing-results-hubrfq-form #uplift-sell-price").val(response.data.supplierPrice.toFixed(5));
    $("#add-edit-sourcing-results-hubrfq-form #estimated-ship-cost").val(response.data.estimatedShippingCost.toFixed(5));
    $("#add-edit-sourcing-results-hubrfq-form #lead-time").val(response.data.leadTime);
    $("#add-edit-sourcing-results-hubrfq-form #rohs-status").val(response.data.rohsStatus);
    $("#add-edit-sourcing-results-hubrfq-form #supplier-warranty").val(response.data.supplierWarranty);
    $("#add-edit-sourcing-results-hubrfq-form #notes").val(response.data.notes);
    $("#add-edit-sourcing-results-hubrfq-form #reason").val(response.data.sellPriceLessReason);


    $("#add-edit-sourcing-results-hubrfq-form #manufacturer-text").removeClass("d-none");
    $("#add-edit-sourcing-results-hubrfq-form #product-text").removeClass("d-none");
    $("#add-edit-sourcing-results-hubrfq-form #package-text").removeClass("d-none");

    if (selectedRowSourcingResults.manufacturerName) {
        $("#add-edit-sourcing-results-hubrfq-form #manufacturer-text").text(`(${selectedRowSourcingResults.manufacturerName})`);
    }

    if (selectedRowSourcingResults.supplierProductType) {
        $("#add-edit-sourcing-results-hubrfq-form #product-text").text(`(${selectedRowSourcingResults.supplierProductType})`);
    }

    if (selectedRowSourcingResults.supplierPackageType) {
        $("#add-edit-sourcing-results-hubrfq-form #package-text").text(`(${selectedRowSourcingResults.supplierPackageType})`);
    }

    const deliveryDate = response.data.deliveryDate;

    if (deliveryDate) {
        const date = new Date(deliveryDate);
        const formatted =
            date.getDate().toString().padStart(2, '0') + '/' +
            (date.getMonth() + 1).toString().padStart(2, '0') + '/' +
            date.getFullYear();

        $("#add-edit-sourcing-results-hubrfq-form #delivery-date").val(formatted);
    }

    if (response.data.isTestingRecommended) {
        $('#testing-recommended-checkbox-input').prop('checked', true);
    }
    else {
        $('#testing-recommended-checkbox-input').prop('checked', false);
    }

    if (response.data.quantity) {
        $("#add-edit-sourcing-results-hubrfq-form #Quantity").val(response.data.quantity);
        $("#add-edit-sourcing-results-hubrfq-form #sourcing-results-quantity").text(`[ Qty Req: ${response.data.quantity} ]`);
    }

    $("#add-edit-sourcing-results-hubrfq-dialog").dialog("instance").setLoading(false);
}

async function handleSupplierInfo(data) {
    if (data.supplierNo && data.supplierName) {
        sourcingResultsSupplierSearchSelect.selectItem({
            value: data.supplierNo,
            label: data.supplierName
        });
        const advisoryNote = (await GlobalTrader.ApiClient.getAsync(`companies/${data.supplierNo}/advisory-note`)).data;
        $("#add-edit-sourcing-results-hubrfq-form #label-supplier-container #SupplierName").html(`${data.supplierName}
                ${advisoryNote ? ButtonHelper.createAdvisoryNotesIconLabel(advisoryNote) : ""}`);
        $("#add-edit-sourcing-results-hubrfq-form").find("#label-supplier-container").removeClass("d-none");
        $("#add-edit-sourcing-results-hubrfq-form").find("#supplier-container").addClass("d-none");

        companyId = `${data.supplierNo}`;
        await reloadCurrencyDropdown();
        $("#add-edit-sourcing-results-hubrfq-form #currency-dropdown").dropdown("select", data.currencyNo);
    }
}

function handleManufacturerInfo(data) {
    if (data.manufacturerNo && data.manufacturerName) {
        sourcingResultsManufacturerSearchSelect.selectItem({
            value: data.manufacturerNo,
            label: data.manufacturerName
        });
    }
}

function handleProductInfo(data) {
    if (data.productNo && data.productDescription) {
        sourcingResultsProductSearchSelect.selectItem({
            value: data.productNo,
            label: data.productDescription
        });
    }
}

function handlePackageInfo(data) {
    if (data.packageNo && data.packageDescription) {
        sourcingResultsPackageSearchSelect.selectItem({
            value: data.packageNo,
            label: data.packageDescription
        });
    }
}

async function fetchAddSourcingResults() {
    const selectedRows = $("#hub-rfq-items-table")
        .DataTable()
        .rows({ selected: true })
        .data()
        .toArray();

    dataDetailRequirement = selectedRows.length === 1 ? selectedRows[0] : null;

    const $select = $("#add-edit-sourcing-results-hubrfq-form #msl-dropdown");

    await selectSourcingResultsPart(dataDetailRequirement.part);

    if (dataDetailRequirement.mfrNo != null && dataDetailRequirement.mfr != null) {
        sourcingResultsManufacturerSearchSelect.selectItem({
            value: dataDetailRequirement.mfrNo,
            label: dataDetailRequirement.mfr
        });
    }

    if (dataDetailRequirement.product != null && dataDetailRequirement.productNo != null) {
        sourcingResultsProductSearchSelect.selectItem({
            value: dataDetailRequirement.productNo,
            label: dataDetailRequirement.product
        });
    }

    if (dataDetailRequirement.package != null && dataDetailRequirement.packageNo != null) {
        sourcingResultsPackageSearchSelect.selectItem({
            value: dataDetailRequirement.packageNo,
            label: dataDetailRequirement.package
        });

        $("#add-edit-sourcing-results-hubrfq-form #supplier-warranty").val(0);
    }

    $("#add-edit-sourcing-results-hubrfq-form #rohs-dropdown").dropdown("select", dataDetailRequirement.rohs);

    let elapsed = 0;
    const interval = 100;
    const timeout = 5000;
    const waitForOptions = setInterval(() => {
        elapsed += interval;
        const matchedValue = $select.find("option").filter(function () {
            return $(this).text().trim() === dataDetailRequirement.msl;
        }).val();

        if (matchedValue !== undefined) {
            clearInterval(waitForOptions);
            $select.val(matchedValue).trigger("change");
        }

        if (elapsed >= timeout) {
            clearInterval(waitForOptions);
        }
    }, 100);

    $("#add-edit-sourcing-results-hubrfq-form #DateCode").val(dataDetailRequirement.dc);
    $("#add-edit-sourcing-results-hubrfq-form #Price").trigger("focusout");

    if (dataDetailRequirement.quantity) {
        $("#add-edit-sourcing-results-hubrfq-form #Quantity").val(dataDetailRequirement.quantity);
        $("#add-edit-sourcing-results-hubrfq-form #sourcing-results-quantity").text(`[ Qty Req: ${dataDetailRequirement.quantity} ]`);
    }


    const currentDialog = $("#add-edit-sourcing-results-hubrfq-dialog").dialog("instance");
    currentDialog.setLoading(false);
}

function focusFirstErrorInput() {
    const $form = $("#add-edit-sourcing-results-hubrfq-form");
    let $firstErrorField = $form.find(".is-invalid").first();

    if ($firstErrorField.is(":hidden")) {
        const fieldId = $firstErrorField.attr("id");
        const $visibleField = $form.find(`[data-input-value-id='${fieldId}']`);
        if ($visibleField.length) {
            $firstErrorField = $visibleField;
        }
    }

    if ($firstErrorField.length > 0) {
        $firstErrorField.trigger("focus");
    }
}

async function selectSourcingResultsPart(part) {
    if (!part) return;

    $("#add-edit-sourcing-results-hubrfq-form #SourcingResultsPartNo").val(part);
}

function checkPrices() {
    const buyPrice = parseFloat($('#buy-price').val());
    const upliftPrice = parseFloat($('#uplift-sell-price').val());

    if (!isNaN(buyPrice) && !isNaN(upliftPrice)) {
        if (upliftPrice < buyPrice) {
            $("#add-edit-sourcing-results-hubrfq-form #price-warning-message").removeClass('d-none');
            $("#add-edit-sourcing-results-hubrfq-form #reason").removeClass('d-none');
        }
        else {
            $("#add-edit-sourcing-results-hubrfq-form #price-warning-message").addClass('d-none');
            $("#add-edit-sourcing-results-hubrfq-form #reason").addClass('d-none');
        }
    }
}

function caculateUpliftSellPrice() {
    let newUpliftSellPrice = 0;
    const buyPrice = parseFloat($('#add-edit-sourcing-results-hubrfq-form #buy-price').val());
    if (!buyPrice) {
        $('#add-edit-sourcing-results-hubrfq-form #uplift-sell-price').val(newUpliftSellPrice.toFixed(5));
        return;
    }

    newUpliftSellPrice = buyPrice + (buyPrice * (upliftSellPrice / 100));
    $('#add-edit-sourcing-results-hubrfq-form #uplift-sell-price').val(newUpliftSellPrice.toFixed(5));
}