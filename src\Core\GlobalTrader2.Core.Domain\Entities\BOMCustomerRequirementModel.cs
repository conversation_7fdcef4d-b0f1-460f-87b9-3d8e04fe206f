namespace GlobalTrader2.Core.Domain.Entities;

public class BomCustomerRequirementModel
{
    // CustomerRequirement base fields
    public int CustomerRequirementId { get; set; }
    public int CustomerRequirementNumber { get; set; }
    public int ClientNo { get; set; }
    public string? FullPart { get; set; }
    public string? Part { get; set; }
    public int? ManufacturerNo { get; set; }
    public string? DateCode { get; set; }
    public int? PackageNo { get; set; }
    public int Quantity { get; set; }
    public double? Price { get; set; }
    public int? CurrencyNo { get; set; }
    public int Salesman { get; set; }
    public DateTime? DatePromised { get; set; }
    public string? Instructions { get; set; }
    public int CompanyNo { get; set; }
    public bool Alternate { get; set; }
    public string? CustomerPart { get; set; }
    public bool Closed { get; set; }
    public byte? ROHS { get; set; }
    public int? UpdatedBy { get; set; }
    public DateTime DLUP { get; set; }
    public bool? FactorySealed { get; set; }
    public string? MSL { get; set; }
    public bool? PartialQuantityAcceptable { get; set; }
    public bool? Obsolete { get; set; }
    public bool? LastTimeBuy { get; set; }
    public bool? RefirbsAcceptable { get; set; }
    public bool? TestingRequired { get; set; }
    public double? TargetSellPrice { get; set; }
    public double? CompetitorBestOffer { get; set; }
    public DateTime? CustomerDecisionDate { get; set; }
    public DateTime? RFQClosingDate { get; set; }
    public int? QuoteValidityRequired { get; set; }
    public int? ReqType { get; set; }
    public bool? OrderToPlace { get; set; }
    public int? ReqForTraceability { get; set; }

    // Joined fields from other tables
    public string? SalesmanName { get; set; }
    public string? CompanyName { get; set; }
    public string? CurrencyCode { get; set; }
    public string? ProductName { get; set; }
    public string? ManufacturerCode { get; set; }
    public string? PackageName { get; set; }
    public bool? IsTraceability { get; set; }

    // BOM related fields
    public string? BOMHeader { get; set; }
    public int? BOMNo { get; set; }
    public int? POHubReleaseBy { get; set; }
    public int? RequestToPOHubBy { get; set; }
    public string? BOMCode { get; set; }
    public string? BOMFullName { get; set; }
    public int? BOMCurrencyNo { get; set; }
    public DateTime? BOMDate { get; set; }
    public int? UpdateByPH { get; set; }
    public string? BOMStatus { get; set; }
    public string? ClientName { get; set; }
    public string? BOMCurrencyCode { get; set; }
    public double? ConvertedTargetValue { get; set; }
    public int AllSorcingHasDelDate { get; set; }
    public int AllSorcingHasProduct { get; set; }
    public int SourcingResult { get; set; }
    public bool? HasClientSourcingResult { get; set; }
    public bool? HasHubSourcingResult { get; set; }
    public bool? IsNoBid { get; set; }
    public DateTime? ExpediteDate { get; set; }
    public byte? AlternateStatus { get; set; }
    public int? SupportTeamMemberNo { get; set; }
    public string? SupportTeamMemberName { get; set; }
    public bool? PartWatchHUBIPO { get; set; }
    public int? PriceIssueBuyAndSell { get; set; }
    public string? IsAs6081Required { get; set; }
    public string? AssignedTo { get; set; }
    public string? AssigneeId { get; set; }
    public string? CompanyAdvisoryNotes { get; set; }
    public string? MfrAdvisoryNotes { get; set; }
    public int? ProductId { get; set; }
}