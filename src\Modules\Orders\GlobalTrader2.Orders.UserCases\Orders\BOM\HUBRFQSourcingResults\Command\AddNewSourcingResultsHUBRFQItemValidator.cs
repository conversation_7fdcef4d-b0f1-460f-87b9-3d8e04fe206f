using FluentValidation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GlobalTrader2.Orders.UserCases.Orders.BOM.HUBRFQSourcingResults.Command
{
    public class AddNewSourcingResultsHUBRFQItemValidator : AbstractValidator<AddNewSourcingResultsHUBRFQItemCommand>
    {
        public AddNewSourcingResultsHUBRFQItemValidator()
        {
            RuleFor(x => x.CustomerRequirementNo)
                .NotEmpty()
                .WithMessage("Customer Requirement Number is required.");

            RuleFor(x => x.Part)
                .NotEmpty()
                .WithMessage("Part is required.")
                .MaximumLength(30)
                .WithMessage("Part cannot exceed 30 characters.");

            RuleFor(x => x.Quantity)
                .GreaterThan(0)
                .WithMessage("Quantity must be greater than 0.")
                .When(x => x.Quantity.HasValue);

            RuleFor(x => x.Price)
                .GreaterThanOrEqualTo(0)
                .WithMessage("Price must be greater than or equal to 0.");

            RuleFor(x => x.EstimatedShippingCost)
                .GreaterThanOrEqualTo(0)
                .WithMessage("Estimated Shipping Cost must be greater than or equal to 0.");

            RuleFor(x => x.SuplierPrice)
                .GreaterThanOrEqualTo(0)
                .WithMessage("Supplier Price must be greater than or equal to 0.")
                .When(x => x.SuplierPrice.HasValue);
        }
    }
}
