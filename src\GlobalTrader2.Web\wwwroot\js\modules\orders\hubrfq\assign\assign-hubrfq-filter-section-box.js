import { FilterTableSectionBox } from "../../../../components/base/filter-table-section-box.component.js?v=#{BuildVersion}#"
import { DefaultPageInfo } from "./assign-hubrfq-constant.js?v=#{BuildVersion}#";
export class AssignHUBRFQFilterSectionBox extends FilterTableSectionBox {
    constructor(tableComp, filterComp, sectionBoxComp, options) {
        super(tableComp, filterComp, sectionBoxComp, options);
    }

    async initAsync() {
        this.state.sectionBoxComp.init();
        this.state.tableComp.init();
        this.state.filterComp.init();

        this._registerFilterEvents();

        this._registerTableEvents();

        const setObj = {};
        this.state.filterComp.setMetaData(setObj);

        this.state.tableComp.setDefaultOrder(DefaultPageInfo.SortIndex, DefaultPageInfo.SortNameDir);

        this.state.tableComp.setPageIndex(DefaultPageInfo.Index, DefaultPageInfo.Size);
        this.state.tableComp.setPageSize(DefaultPageInfo.Size);
    }

    async _saveFilterState(data) {
        data.size = DefaultPageInfo.Size;
        await super._saveFilterState(data);
    }
}