﻿using GlobalTrader2.Orders.UI.ViewModel.KubAssistance;
using GlobalTrader2.Orders.UserCases.Orders.KubAssistance.Commands.StartKubCacheForPage;
using GlobalTrader2.Orders.UserCases.Orders.KubAssistance.Commands.StartKubCachesForAddRequirement;
using GlobalTrader2.Orders.UserCases.Orders.KubAssistance.Queries.Dtos;
using GlobalTrader2.Orders.UserCases.Orders.KubAssistance.Queries.GetAllowedEnable;
using GlobalTrader2.Orders.UserCases.Orders.KubAssistance.Queries.GetAveragePriceDetails;
using GlobalTrader2.Orders.UserCases.Orders.KubAssistance.Queries.GetBomTopBuyPrices;
using GlobalTrader2.Orders.UserCases.Orders.KubAssistance.Queries.GetBomTopCustomerRequirements;
using GlobalTrader2.Orders.UserCases.Orders.KubAssistance.Queries.GetBomTopQuotes;
using GlobalTrader2.Orders.UserCases.Orders.KubAssistance.Queries.GetCountryWiseSaleDetails;
using GlobalTrader2.Orders.UserCases.Orders.KubAssistance.Queries.GetDetails;
using GlobalTrader2.Orders.UserCases.Orders.KubAssistance.Queries.GetGpCalculationDetails;
using GlobalTrader2.Orders.UserCases.Orders.KubAssistance.Queries.GetGpLastCalculationDetails;
using GlobalTrader2.Orders.UserCases.Orders.KubAssistance.Queries.GetLastRecentQuoteDetails;
using GlobalTrader2.Orders.UserCases.Orders.KubAssistance.Queries.GetMainProductGroupDetails;
using GlobalTrader2.Orders.UserCases.Orders.KubAssistance.Queries.GetTopBuyPriceDetails;
using GlobalTrader2.Orders.UserCases.Orders.KubAssistance.Queries.GetTotalLineInvoiceDetails;

namespace GlobalTrader2.Orders.UI.Areas.Orders.Controllers
{
    [Authorize]
    [ApiController]
    [Route("api/orders/kub-assistance")]
    public class KubAssistanceController : ApiBaseController
    {
        private readonly IMediator _mediator;
        private readonly SettingManager _settingManager;

        public KubAssistanceController(IMediator mediator, SettingManager settingManager)
        {
            _mediator = mediator;
            _settingManager = settingManager;
        }

        [HttpGet("setting")]
        public async Task<IActionResult> GetKubSetting()
        {
            var isKubEnabled = await _settingManager.IsKubEnabled();

            return Ok(new BaseResponse<KubSettingDto>
            {
                Success = true,
                Data = new KubSettingDto { Enabled = isKubEnabled }
            });
        }

        [HttpGet("allowed-status")]
        public async Task<IActionResult> GetKubIsAllowedEnable(string partNo)
        {
            var response = await _mediator.Send(new GetKubIsAllowedEnableQuery
            {
                ClientId = ClientId,
                PartNo = StringHelper.RemovePunctuationRetainingPercentSigns(partNo),
            });

            return new JsonResult(response);
        }

        [HttpPost("start-kub-cache")]
        public async Task<IActionResult> StartKubCache(KubStartCacheProcessRequest request)
        {
            var response = await _mediator.Send(new StartKubCacheForAddRequirementCommand
            {
                ClientId = ClientId,
                PartNo = StringHelper.RemovePunctuationRetainingPercentSigns(request.PartNo)
            });

            return new JsonResult(response);
        }
        [HttpPost("start-cache-for-page")]
        public async Task<IActionResult> StartCacheForPage(KubStartCacheForPageRequest request)
        {
            var response = await _mediator.Send(new StartKubCacheForPageCommand
            {
                PartNo = StringHelper.RemovePunctuationRetainingPercentSigns(request.PartNo),
                CustomerRequirementId = request.CustomerReqId,
                ClientId = ClientId,
            });

            return Ok(response);
        }

        [HttpGet("avg-price-details")]
        public async Task<IActionResult> GetAvgPriceDetails(string partNo)
        {
            var response = await _mediator.Send(new GetAveragePriceDetailsQuery
            {
                ClientId = ClientId,
                PartNo = StringHelper.RemovePunctuationRetainingPercentSigns(partNo)
            });

            return Ok(response);
        }

        [HttpGet("total-line-invoice-details")]
        public async Task<IActionResult> GetTotalLineInvoiceDetails(string partNo, int customerReqId)
        {
            var response = await _mediator.Send(new GetTotalLineInvoiceDetailsQuery
            {
                ClientId = ClientId,
                PartNo = StringHelper.RemovePunctuationRetainingPercentSigns(partNo),
                CustomerRequirementId = customerReqId
            });

            return Ok(response);
        }

        [HttpGet("last-top-recent-quote-details")]
        public async Task<IActionResult> GetKubLastTopRecentQuoteDetails(string partNo, int customerReqId)
        {
            var response = await _mediator.Send(new GetLastRecentQuoteDetailsQuery
            {
                ClientId = ClientId,
                PartNo = StringHelper.RemovePunctuationRetainingPercentSigns(partNo),
                CustomerRequirementId = customerReqId
            });

            return Ok(response);
        }

        [HttpGet("main-product-group-details")]
        public async Task<IActionResult> GetKubMainProductGroupDetails(string partNo)
        {
            var response = await _mediator.Send(new GetMainProductGroupDetailsQuery
            {
                ClientId = ClientId,
                PartNo = StringHelper.RemovePunctuationRetainingPercentSigns(partNo)
            });

            return Ok(response);
        }

        [HttpGet("details")]
        public async Task<IActionResult> GetKubAssistanceDetails(string partNo)
        {
            var response = await _mediator.Send(new GetKubAssistanceDetailsQuery
            {
                ClientId = ClientId,
                PartNo = StringHelper.RemovePunctuationRetainingPercentSigns(partNo)
            });

            return Ok(response);
        }

        [HttpGet("country-wise-sale-details")]
        public async Task<IActionResult> GetCountryWiseSaleDetails(string partNo)
        {
            var response = await _mediator.Send(new GetCountryWiseSaleDetailsQuery
            {
                ClientId = ClientId,
                PartNo = StringHelper.RemovePunctuationRetainingPercentSigns(partNo)
            });

            return Ok(response);
        }

        [HttpGet("top-buy-price-details")]
        public async Task<IActionResult> GetTopBuyPriceDetails(string partNo)
        {
            var response = await _mediator.Send(new GetTopBuyPriceDetailsQuery
            {
                ClientId = ClientId,
                PartNo = StringHelper.RemovePunctuationRetainingPercentSigns(partNo)
            });

            return Ok(response);
        }

        [HttpGet("gp-calculation-Details")]
        public async Task<IActionResult> GetGpCalculationDetails(string partNo)
        {
            var response = await _mediator.Send(new GetGpCalculationDetailsQuery
            {
                ClientId = ClientId,
                PartNo = StringHelper.RemovePunctuationRetainingPercentSigns(partNo)
            });

            return Ok(response);
        }

        [HttpGet("gp-last-sale-calculation-details")]
        public async Task<IActionResult> GetGpLastSaleCalculationDetails(string partNo)
        {
            var response = await _mediator.Send(new GetGpLastCalculationDetailsQuery
            {
                ClientId = ClientId,
                PartNo = StringHelper.RemovePunctuationRetainingPercentSigns(partNo)
            });
            return Ok(response);
        }

        [HttpGet("Bom/top-quotes")]
        public async Task<IActionResult> GetBomTopQuotes(string fullPartNo)
        {
            var response = await _mediator.Send(new GetBomTopQuotesQuery
            {
                ClientId = ClientId,
                PartNo = StringHelper.RemovePunctuationRetainingPercentSigns(fullPartNo)
            });

            return Ok(response);
        }

        [HttpGet("Bom/top-customer-requirements")]
        public async Task<IActionResult> GetBomTopCustomerRequirements(string fullPartNo)
        {
            var response = await _mediator.Send(new GetBomTopCustomerRequirementsQuery
            {
                ClientId = ClientId,
                PartNo = StringHelper.RemovePunctuationRetainingPercentSigns(fullPartNo)
            });

            return Ok(response);
        }

        [HttpGet("Bom/top-buy-prices")]
        public async Task<IActionResult> GetBomTopBuyPrices(string fullPartNo)
        {
            var response = await _mediator.Send(new GetBomTopBuyPricesQuery
            {
                ClientId = ClientId,
                PartNo = StringHelper.RemovePunctuationRetainingPercentSigns(fullPartNo)
            });

            return Ok(response);
        }

        [HttpGet("Bom/stock-details")]
        public async Task<IActionResult> GetBomStockDetails(int BomId, string fullPartNo)
        {
            var response = await _mediator.Send(new GetGetBomStockDetailsQuery
            {
                ClientId = ClientId,
                PartNo = StringHelper.RemovePunctuationRetainingPercentSigns(fullPartNo),
                BomId = BomId,
                IsHUBRFQ = true
            });

            return Ok(response);
        }

        
    }
}
