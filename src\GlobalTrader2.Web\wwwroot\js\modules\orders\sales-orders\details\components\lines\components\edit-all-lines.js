import { LinesTabService } from '../lines.service.js?v=#{BuildVersion}#';
import { FormBase } from './base/form-base.js';
export class EditAllSoLinesComponent extends FormBase {
    constructor() {
        super();
        this.$dialog = $('#edit-all-sales-order-lines-dialog');
        this.$form = $('#edit-all-sales-order-lines-form');

        this._$promiseDate = $('#edit-all-sales-order-lines-date-promised');
        this._$promiseReason = $('#edit-all-sales-order-lines-promise-reason');
        
        this._saveChangedMessage = window.localizedStrings.saveChangedMessage;
        this._saveText = window.localizedStrings.save;
        this._cancelText = window.localizedStrings.cancel;

        this._allowEditDatePromisedBetweenCurrentMonthAndEnd = editAllLinesModel.allowEditDatePromisedBetweenCurrentMonthAndEnd;
        this._isAutoAuthorizeSo = editAllLinesModel.isAutoAuthorizeSo;
        this._isAuthorizeSo = editAllLinesModel.isAuthorizeSo;
    }

    open() {
        if (!this._isDialogInit) {
            this._initDialog();
            this._initFields();
            this._setupBehaviors();
            this._setupFormValidation();
            this._isDialogInit = true;
        }
        
        this.$dialog.dialog("open");
    }

    _initDialog() {
        this.$dialog.dialog({
            autoOpen: false,
            height: "auto",
            width: "45vw",
            maxHeight: $(window).height(),
            modal: true,
            buttons: [
                {
                    id: "save-btn",
                    text: this._saveText,
                    class: 'btn btn-primary',
                    html: `<img src="/img/icons/save.svg" alt="${this._save}"/>${this._save}`,
                    click: async () => {
                        if (this.$form.valid()) {
                            this.$dialog.parent().find(`.ui-dialog-buttonset button:has(img[alt="${window.localizedStrings.save}"])`).prop('disabled', true);
                            this.$dialog.find(".form-error-summary").hide();

                            const response = await this._submitForm();
                            this.$dialog.dialog("setLoading", false);
                            this.$dialog.parent().find(`.ui-dialog-buttonset button:has(img[alt="${window.localizedStrings.save}"])`).prop('disabled', false);

                            if (response?.success) {
                                showToast('success', window.localizedStrings.saveChangedMessage);
                                this.$dialog.dialog("close");
                            } else {
                                showToast('error', response?.title);
                            }
                        } else {
                            $(".form-error-summary").show();
                        }
                    }
                },
                {
                    text: this._cancelText,
                    class: 'btn btn-danger',
                    html: `<img src="/img/icons/slash.svg" id="cancel-btn" alt="${this._cancelText}" />${this._cancelText}`,
                    click: () => {
                        this.$dialog.dialog("close");
                    }
                }
            ],
            open: () => {
                this.$dialog.find('.ui-dialog-titlebar-close').remove();
            },
            close: () => {
                this.$dialog.dialog("close");
                this.$form[0].reset();
                this.$form.validate().resetForm();
                this.$dialog.find(".form-error-summary").hide();
                this.$form.find('.is-invalid').removeClass("is-invalid");
            }
        });
    }

    _initFields() {

        this._$promiseDate.datepicker2();

        this._$promiseReason.dropdown({
            serverside: false,
            endpoint: '/lists/promise-reasons',
            params: {
                section: 'PS'
            },
            placeholderValue: "",
        });
    }

    async _submitForm() {
        const data = GlobalTrader.FormHelper.convertFormDataToOject(this.$form);
        data['isReasonChanged'] = this._$promiseReason.val() != null;
        this.$dialog.dialog("setLoading", true);
        const header = { "RequestVerificationToken": this.$form.find(':input[name="__RequestVerificationToken"]').val() }
        return await LinesTabService.updateAllSalesOrderLinesAsync(data, header);
    }

    _setupBehaviors() {
        this._$promiseDate.on('change', (event) => {
            this._$promiseReason.dropdown('reset');
        });
    }

    _setupFormValidation() {
        const promiseDateMessage = editAllLinesLocalizer.promiseNotInRangeOfPromiseMonth;
        const $dateField = this._$promiseDate;

        $.validator.addMethod("promiseDateRange", function (value, element, param) {
            if (this.optional(element)) {
                return true;
            }
            
            const currentDate = new Date();
            currentDate.setHours(0, 0, 0, 0);
            const dateStr = $dateField.val();

            const [day, month, year] = dateStr.split('/').map(num => parseInt(num, 10));
            let promiseDate = new Date(year, month - 1, day);
            promiseDate.setHours(0, 0, 0, 0);


            if (isNaN(promiseDate.getTime())) {
                return false;
            }

            return promiseDate >= currentDate;
        }, promiseDateMessage);

        this.$form.validate({
            ignore: [],
            rules: {
                datePromised: {
                    required: true,
                    promiseDateRange: true
                },
                promiseReasonNo: {
                    required: {
                        depends: (element) => {
                            return this._$promiseDate.val() != null;
                        }
                    },
                },
            },
            highlight: (element) => {
                $(element).addClass("is-invalid");
            },
            unhighlight: (element) => {
                $(element).removeClass("is-invalid");
            },
            errorPlacement: function (error, element) {
                const $element = $(element);
                if ($element.hasClass('datepicker') || $element.hasClass('dropdown')) {
                    error.insertAfter($element.parent());
                }
                else {
                    error.insertAfter(element); // Default placement
                }
            }
        });
    }
}
