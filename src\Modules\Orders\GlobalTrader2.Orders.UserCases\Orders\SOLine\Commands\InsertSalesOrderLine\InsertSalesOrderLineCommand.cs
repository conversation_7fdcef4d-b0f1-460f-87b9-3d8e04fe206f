namespace GlobalTrader2.Orders.UserCases.Orders.SOLine.Commands.InsertSalesOrderLine
{
    public class InsertSalesOrderLineCommand : IRequest<BaseResponse<int>>
    {
        public int SalesOrderNo { get; set; }
        public string? Part { get; set; }
        public int? ManufacturerNo { get; set; }
        public string? DateCode { get; set; }
        public int? PackageNo { get; set; }
        public int Quantity { get; set; }
        public double Price { get; set; }
        public DateTime DatePromised { get; set; }
        public DateTime RequiredDate { get; set; }
        public string? Instructions { get; set; }
        public int? ProductNo { get; set; }
        public string? Taxable { get; set; }
        public string? CustomerPart { get; set; }
        public bool Posted { get; set; }
        public bool ShipASAP { get; set; }
        public int? ServiceNo { get; set; }
        public int? StockNo { get; set; }
        public byte? ROHS { get; set; }
        public string? Notes { get; set; }
        public int? UpdatedBy { get; set; }
        public int? QuoteLineNo { get; set; }
        public byte? ProductSource { get; set; }
        public int? SourcingResultNo { get; set; }
        public bool IsCreateSOClone { get; set; }
        public DateTime? PODeliveryDate { get; set; }
        public bool PrintHazardous { get; set; }
        public string? MSLLevel { get; set; }
        public string? ContractNo { get; set; }
        public int? DocId { get; set; }
        public string? DocType { get; set; }
        public int EIRequired { get; set; } = 0;
        public string? EvidenceNotes { get; set; }
        public int? TestingType { get; set; }
        public string? ECCNCode { get; set; }
        public int? ECCNNo { get; set; }
        public bool AS6081 { get; set; }
        public double? ServiceCostRef { get; set; }
    }
}