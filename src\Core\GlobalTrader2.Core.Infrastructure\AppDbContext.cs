﻿using GlobalTrader2.Core.Domain.Entities;
using Microsoft.EntityFrameworkCore;
namespace GlobalTrader2.Core.Infrastructure.Persistence
{
    public class AppDbContext : DbContext
    {
        public AppDbContext(DbContextOptions<AppDbContext> options)
            : base(options)
        {
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<Sequencer>()
            .<PERSON><PERSON><PERSON>(o => new { o.SystemDocumentNo, o.ClientNo });

            modelBuilder.Entity<Country>()
                .HasOne(c => c.Tax)
                .WithMany(t => t.Countries)
                .HasForeignKey(c => c.TaxNo)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<GlobalProduct>()
                 .HasMany(p => p.GlobalDutyRate)
                 .WithOne()
                 .HasForeignKey(gdr => gdr.ProductNo);

            modelBuilder.Entity<Product>()
                .HasMany(p => p.DutyRate)
                .WithOne()
                .HasForeignKey(dr => dr.ProductNo);

            modelBuilder.Entity<Product>()
                .HasOne(p => p.Client)
                .WithMany()
                .HasForeignKey(p => p.ClientNo);

            modelBuilder.Entity<StarRating>()
               .HasOne(p => p.Login)
               .WithMany()
               .HasForeignKey(p => p.CreatedBy);

            modelBuilder.Entity<CountryHeaderMapping>()
                .HasOne(c => c.Countries)
                .WithMany(t => t.CountryHeaderMapping)
                .HasForeignKey(c => c.CountryNo)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<MailGroupMember>()
                .HasOne(mgm => mgm.MailGroup)
                .WithMany(mg => mg.MailGroupMembers)
                .HasForeignKey(mgm => mgm.MailGroupNo)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<MailGroupMember>()
                .HasOne(mgm => mgm.Login)
                .WithMany(lg => lg.MailGroupMembers)
                .HasForeignKey(mgm => mgm.LoginNo)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<Tax>()
                .HasMany(t => t.TaxRates)
                .WithOne(tr => tr.Tax)
                .HasForeignKey(tr => tr.TaxNo);

            modelBuilder.Entity<Country>()
                .HasMany(country => country.Addresses)
                .WithOne(address => address.Country)
                .HasForeignKey(address => address.CountryNo);

            modelBuilder.Entity<Login>()
                .HasOne(l => l.Client)
                .WithMany()
                .HasForeignKey(l => l.ClientNo);

            modelBuilder.Entity<ManufacturerPdf>()
                .HasOne(m => m.Login)
                .WithMany()
                .HasForeignKey(m => m.UpdatedBy);

            modelBuilder.Entity<Login>()
                .HasMany(l => l.CreditLimitLogs)
                .WithOne(cll => cll.Login)
                .HasForeignKey(cll => cll.UpdatedBy);

            modelBuilder.Entity<CreditLimitLog>()
                .HasOne(cll => cll.CreditLimit)
                .WithMany(cl => cl.CreditLimitLogs)
                .HasForeignKey(cll => cll.CreditLimitNo);

            modelBuilder.Entity<CompanyPdf>()
               .HasOne(m => m.Login)
               .WithMany()
               .HasForeignKey(m => m.UpdatedBy);

            modelBuilder.Entity<LoginPreference>()
                .HasOne(lp => lp.Login)
                .WithOne(l => l.LoginPreference)
                .HasForeignKey<LoginPreference>(lp => lp.LoginNo);

            modelBuilder.Entity<CompanyCIPPDF>()
               .HasOne(m => m.Login)
               .WithMany()
               .HasForeignKey(m => m.UpdatedBy);

            modelBuilder.Entity<InsuranceHistory>()
                .HasNoKey();

            modelBuilder.Entity<Manufacturer>()
            .HasMany(mfr => mfr.ManufacturerLinks)
            .WithOne(ml => ml.Manufacturer)
            .HasForeignKey(ml => ml.ManufacturerNo);

            modelBuilder.Entity<SalesOrderPDF>()
               .HasOne(m => m.Login)
               .WithMany()
               .HasForeignKey(m => m.UpdatedBy);

            modelBuilder.Entity<SOPaymentInfo>()
               .HasOne(m => m.Login)
               .WithMany()
               .HasForeignKey(m => m.CreatedBy);

            modelBuilder.Entity<SalesOrderExcel>()
               .HasOne(m => m.Login)
               .WithMany()
               .HasForeignKey(m => m.UpdatedBy);

            modelBuilder.Entity<SoLineEuuPdf>()
                .HasOne(m => m.Login)
                .WithMany()
                .HasForeignKey(m => m.UpdatedBy);

            modelBuilder.Entity<StockImage>()
               .HasOne(m => m.Login)
               .WithMany()
               .HasForeignKey(m => m.UpdatedBy);

            base.OnModelCreating(modelBuilder);
        }

        public DbSet<AS6081TypeOfSupplier> AS6081TypeOfSuppliers { get; set; }
        public DbSet<AS6081RiskOfSupplier> AS6081RiskOfSuppliers { get; set; }
        public DbSet<AS6081ReasonForChosenSupplier> AS6081ReasonForChosenSuppliers { get; set; }
        public DbSet<CompanyDemo> CompaniesDemo { get; set; }
        public DbSet<Login> Logins { get; set; }
        public DbSet<Client> Clients { get; set; }
        public DbSet<TermsSelect> TermsSelect { get; set; }
        public DbSet<Terms> Terms { get; set; }
        public DbSet<Printer> Printers { get; set; }
        public virtual DbSet<RecentlyViewedDetail> RecentlyViewed { get; set; }
        public DbSet<SettingItemDetail> SettingItemDetails { get; set; }
        public DbSet<GlobalCurrencyList> GlobalCurrencyLists { get; set; }
        public DbSet<LocalCurrency> LocalCurrencies { get; set; }
        public DbSet<SecurityGroup> SecurityGroups { get; set; }
        public DbSet<CompanyType> CompanyTypes { get; set; }
        public DbSet<Address> Addresses { get; set; }
        public DbSet<Warehouse> Warehouses { get; set; }
        public DbSet<CommunicationLogType> CommunicationLogTypes { get; set; }
        public DbSet<MasterCountry> MasterCountries { get; set; }
        public DbSet<MasterAppSetting> MasterAppSettings { get; set; }
        public DbSet<MasterAppSettingItem> MasterAppSettingItems { get; set; }
        public DbSet<SecurityGroupLogin> SecurityGroupLogins { get; set; }
        public DbSet<Company> Companies { get; set; }
        public DbSet<HubrfqPvvQuestion> HUBRFQPVVQuestions { get; set; }
        public DbSet<SecurityLoginPermissionReadModel> SecurityLoginPermissions { get; set; }
        public DbSet<GlobalSecurityLoginPermissionReadModel> GlobalSecurityLoginPermissions { get; set; }
        public DbSet<IndustryType> IndustryTypes { get; set; }
        public DbSet<TabSecurityGroupSecurityFunctionPermission> TabSecurityGroupSecurityFunctionPermissions { get; set; }
        public DbSet<SecurityGroupSecurityFunctionPermission> SecurityGroupSecurityFunctionPermission { get; set; }
        public DbSet<EmailComposerDetail> EmailComposerDetails { get; set; }
        public DbSet<CountingMethod> CountingMethods { get; set; }
        public DbSet<WarningMessage> WarningMessages { get; set; }
        public DbSet<SystemWarningMessage> SystemWarningMessages { get; set; }
        public DbSet<Incoterm> Incoterms { get; set; }
        public DbSet<Package> Packages { get; set; }
        public DbSet<MasterStatus> MasterStatuses { get; set; }
        public DbSet<StatusReason> StatusReasons { get; set; }
        public DbSet<Sequencer> Sequencers { get; set; }
        public DbSet<Currency> Currencies { get; set; }
        public DbSet<CurrencyRate> CurrencyRates { get; set; }
        public DbSet<MailGroup> MailGroups { get; set; }
        public DbSet<MailGroupMember> MailGroupMembers { get; set; }
        public DbSet<OGELLicense> OGELLicenses { get; set; }
        public DbSet<ApplicationSetting> ApplicationSettings { get; set; }
        public DbSet<Reason> Reasons { get; set; }
        public DbSet<ClientInvoiceHeader> ClientInvoiceHeaderImages { get; set; }
        public DbSet<DocumentSize> DocumentSizes { get; set; }
        public DbSet<Session> Sessions { get; set; }
        public DbSet<CertificateCategory> CertificateCategories { get; set; }
        public DbSet<Certificate> Certificates { get; set; }
        public DbSet<ClientInvoiceHeader> ClientInvoiceHeaders { get; set; }
        public DbSet<Tax> Taxes { get; set; }
        public DbSet<TaxRate> TaxRates { get; set; }
        public DbSet<Country> Countries { get; set; }
        public DbSet<CountryHeaderMapping> CountryHeaderMappings { get; set; }
        public DbSet<ToDoListType> ToDoListTypes { get; set; }
        public DbSet<MasterLogin> MasterLogins { get; set; }
        public DbSet<Division> Divisions { get; set; }
        public DbSet<RootCauseCategory> RootCauseCategories { get; set; }
        public DbSet<RootCauseSubCategory> RootCauseSubCategories { get; set; }
        public DbSet<AuditSetting> AuditSetting { get; set; }
        public DbSet<DataListNuggetState> DataListNuggetStates { get; set; }
        public DbSet<Todo> Todos { get; set; }
        public DbSet<LoginPreference> LoginPreference { get; set; }
        public DbSet<GlobalTax> GlobalTaxes { get; set; }
        public DbSet<GlobalTaxRate> GlobalTaxRates { get; set; }
        public DbSet<MailMessageFolder> MailMessageFolders { get; set; }
        public DbSet<StockLogReason> StockLogReasons { get; set; }
        public DbSet<DutyRate> DutyRates { get; set; }
        public DbSet<MailMessage> MailMessages { get; set; }
        public DbSet<SourcingLink> SourcingLinks { get; set; }
        public DbSet<GlobalProduct> GlobalProducts { get; set; }
        public DbSet<GlobalProductCategory> GlobalProductCategories { get; set; }
        public DbSet<GlobalProductGroup> GlobalProductGroups { get; set; }
        public DbSet<GlobalDutyRate> GlobalDutyRates { get; set; }
        public DbSet<Product> Products { get; set; }
        public DbSet<ShipVia> ShipVias { get; set; }
        public DbSet<EntertainmentType> EntertainmentTypes { get; set; }
        public DbSet<Eccn> Eccns { get; set; }
        public DbSet<TbTeam> TbTeams { get; set; }
        public DbSet<ToDoCategory> ToDoCategories { get; set; }
        public DbSet<StarRating> StarRatings { get; set; }
        public DbSet<SystemDocumentFooter> SystemDocumentFooters { get; set; }
        public DbSet<Quote> Quotes { get; set; }
        public DbSet<Contact> Contacts { get; set; }
        public DbSet<CompanyAddress> CompanyAddresses { get; set; }
        public DbSet<RequirementDropDownData> RequirementDropDownData { get; set; }
        public DbSet<Usage> Usages { get; set; }
        public DbSet<StockDetails> StockDetails { get; set; }
        public DbSet<StockInfoDetails> StockInfoDetails { get; set; }
        public DbSet<ManufacturerDataListReadModel> ManufacturerDataListReadModels { get; set; }
        public DbSet<CommunicationLog> CommunicationLogs { get; set; }
        public DbSet<SecurityFunctionEntity> SecurityFunctions { get; set; }
        public DbSet<TabSecurityFunction> TabSecurityFunctions { get; set; }
        public DbSet<ManufacturerLink> ManufacturerLinks { get; set; }
        public DbSet<RohsStatus> ROHSStatuses { get; set; }
        public DbSet<Manufacturer> Manufacturers { get; set; }
        public DbSet<MSLLevel> MSLLevels { get; set; }
        public DbSet<BOM> BOMs { get; set; }
        public DbSet<REQStatus> REQStatuses { get; set; }
        public DbSet<StockInfo> StockInfos { get; set; }
        public DbSet<ManufacturerPdf> ManufacturerPDFs { get; set; }
        public DbSet<SalesOrder> SalesOrders { get; set; }
        public DbSet<ExcessDetails> Excesses { get; set; }
        public DbSet<ManuFacturerExcel> ManuFacturerExcels { get; set; }
        public DbSet<ContactGroup> ContactGroups { get; set; }
        public DbSet<ExcessDetailReadModel> ExcessDetailReadModels { get; set; }
        public DbSet<OfferStatus> OfferStatuses { get; set; }
        public DbSet<ApiUrlKey> ApiUrlKeys { get; set; }
        public DbSet<SupplierApiInsertData> SupplierApiInsertData { get; set; }
        public DbSet<RestrictedManufacturer> RestrictedManufacturers { get; set; }
        public DbSet<CustomerRequirementMainInfo> CustomerRequirementMainInfos { get; set; }
        public DbSet<OfferDetailReadModel> OfferDetailReadModels { get; set; }
        public DbSet<OrderCustomerRequirement> CustomerRequirements { get; set; }

        public DbSet<CompanyAddressReadModel> CompanyAddressReadModels { get; set; }
        public DbSet<Region> Regions { get; set; }
        public DbSet<CompanyIndustryType> CompanyIndustryTypes { get; set; }
        public DbSet<GlobalSalesPerson> GlobalSalesPersons { get; set; }
        public DbSet<CreditLimitLog> CreditLimitLogs { get; set; }
        public DbSet<PrintDocumentLog> PrintDocumentLogs { get; set; }
        public DbSet<CreditLimit> CreditLimits { get; set; }
        public DbSet<CompanyPdf> CompanyPdfs { get; set; }
        public DbSet<LabelType> LabelTypes { get; set; }
        public DbSet<PowerAppUrl> PowerAppUrls { get; set; }
        public DbSet<EpoReadModel> EpoReadModel { get; set; }
        public DbSet<CommonDropdownReadModel> CommonDropdownReadModel { get; set; }
        public DbSet<GlobalSalesPersonName> GlobalSalesPersonNames { get; set; }
        public DbSet<ReverseLogisticsReadModel> ReverseLogisticsReadModels { get; set; }
        public DbSet<CompanyCertificate> CompanyCertificates { get; set; }
        public DbSet<SalesOrderReadModel> SalesOrderReadModel { get; set; }
        public DbSet<CompanyCIPPDF> CompanyCIPPDFs { get; set; }
        public DbSet<Audit> Audits { get; set; }
        public DbSet<InsuranceHistory> InsuranceHistories { get; set; }
        public DbSet<IpoReadModel> IpoReadModels { get; set; }
        public DbSet<CustomerRequirementReadModel> CustomerRequirementReadModels { get; set; }
        public DbSet<QuoteLineReadModel> QuoteLineReadModels { get; set; }
        public DbSet<Lytica> lyticaData { get; set; }
        public DbSet<PurchaseRequestLineReadModel> PurchaseRequestLineReadModels { get; set; }
        public DbSet<HubRfqPvvAnswerTemp> HUBRFQPVVAnswerTemps { get; set; }
        public DbSet<CustomerRequirementForCompanyReadModel> CustomerRequirementForCompanyReadModel { get; set; }
        public DbSet<InvoiceReadModel> InvoiceReadModel { get; set; }
        public DbSet<PurchaseOrderReadModel> PurchaseOrderReadModel { get; set; }
        public DbSet<CustomerRMAReadModel> CustomerRMAReadModel { get; set; }
        public DbSet<SupplierRMAReadModel> SupplierRMAReadModel { get; set; }
        public DbSet<CreditReadModel> CreditReadModel { get; set; }
        public DbSet<DebitReadModel> DebitReadModel { get; set; }
        public DbSet<QuoteVw> QuoteVws { get; set; }
        public DbSet<PurchaseOrder> PurchaseOrders { get; set; }
        public DbSet<Invoice> Invoices { get; set; }
        public DbSet<CustomerRma> CustomerRmas { get; set; }
        public DbSet<SupplierRma> SupplierRmas { get; set; }
        public DbSet<Credit> Credits { get; set; }
        public DbSet<Debit> Debits { get; set; }
        public DbSet<InternalPurchaseOrder> InternalPurchaseOrders { get; set; }
        public DbSet<PurchaseOrderLineReadModel> PurchaseOrderLineReadModels { get; set; }
        public DbSet<Epr> EPR { get; set; }
        public DbSet<KubAssistanceDetailsCache> KubAssistanceDetailsCache { get; set; }
        public DbSet<KubPoDetailsCache> KubPoDetailsCache { get; set; }
        public DbSet<KubGpCalculationDetail> KubGpCalculationDetails { get; set; }
        public DbSet<AutoSourcingReadModel> AutoSourcingReadModels { get; set; }
        public DbSet<SourcingResult> SourcingResult { get; set; }
        public DbSet<HubRfqPvvAnswer> HUBRFQPVVAnswers { get; set; }
        public DbSet<HubRfqPvvAnswerTemp> HubrfqpvvAnswerTemps { get; set; }
        public DbSet<SalesOrderStatus> SalesOrderStatus { get; set; }
        public DbSet<ReportColumn> ReportColumns { get; set; }
        public DbSet<SalesOrderPDF> SalesOrderPDF { get; set; }
        public DbSet<As6081AlertMessages> As6081AlertMessages { get; set; }
        public DbSet<ExportApprovalStatusOGEL> ExportApprovalStatusOGEL { get; set; }
        public DbSet<SOPaymentInfo> SOPaymentInfo { get; set; }
        public DbSet<SalesOrderExcel> SalesOrderExcel { get; set; }
        public DbSet<SalesOrderLine> SalesOrderLine { get; set; }
        public DbSet<GetSupplierTypeForSearchReadModel> GetSupplierTypeForSearchReadModels { get; set; }
        public DbSet<CompanyIndustryForSearchReadModel> CompanyIndustryForSearchReadModels { get; set; }
        public DbSet<GetSourcingDataSearchReadModel> GetSourcingDataSearchReadModels { get; set; }
        public DbSet<ProductSource> ProductSources { get; set; }
        public DbSet<SoLineEuuPdf> SoLineEuuPdf { get; set; }
        public DbSet<SourcingImage> SourcingImages { get; set; }
        public DbSet<StockImage> StockImages { get; set; }
    }
}
