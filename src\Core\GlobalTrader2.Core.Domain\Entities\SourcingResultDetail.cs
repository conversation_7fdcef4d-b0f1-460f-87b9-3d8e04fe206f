﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GlobalTrader2.Core.Domain.Entities
{
    public class SourcingResultDetail
    {
        public int SourcingResultId { get; set; }
        public int CustomerRequirementNo { get; set; }
        public string? SourcingTable { get; set; }
        public string? TypeName { get; set; }
        public string? FullPart { get; set; }
        public string? Part { get; set; }
        public int? ManufacturerNo { get; set; }
        public string? DateCode { get; set; }
        public int? ProductNo { get; set; }
        public int? PackageNo { get; set; }
        public int? Quantity { get; set; }
        public double? Price { get; set; }
        public int? CurrencyNo { get; set; }
        public int? SupplierNo { get; set; }
        public byte? ROHS { get; set; }
        public int? OfferStatusNo { get; set; }
        public string? Notes { get; set; }
        public string? SupplierName { get; set; }
        public string? ManufacturerName { get; set; }
        public string? ManufacturerCode { get; set; }
        public string? ProductDescription { get; set; }
        public bool? ProductInactive { get; set; }
        public int? MSLLevelNo { get; set; }
        public string? PackageDescription { get; set; }
        public string? SPQ { get; set; }
        public string? LeadTime { get; set; }
        public string? ROHSStatus { get; set; }
        public string? FactorySealed { get; set; }
        public string? MSL { get; set; }
        public string? SupplierMOQ { get; set; }
        public int? SupplierTotalQSA { get; set; }
        public string? SupplierLTB { get; set; }
        public string? SupplierNotes { get; set; }
        public int? regionNo { get; set; }
        public string? CurrencyCode { get; set; }
        public bool? partWatchMatch { get; set; }
        public double ? UPLiftPrice { get; set; }
        public double ? EstimatedShippingCost { get; set; }
        public DateTime ? DeliveryDate { get; set; }
        public int? SupplierWarranty { get; set; }
        public bool? NonPreferredCompany {  get; set; }
        public string? MSLLevel { get; set; }
        public int? CountryOfOriginNo { get; set; }
        public int? IHSCountryOfOriginNo { get; set; }
        public string? IHSCountryOfOriginName { get; set; }
        public int? TypeOfSupplierNo { get; set; }
        public int? ReasonForSupplierNo { get; set; }
        public int? RiskOfSupplierNo { get; set; }
        public int? CountryNo { get; set; }
        public string? CountryName { get; set; }
        public string? SellPriceLessReason { get; set; }
        public bool? IsTestingRecommended { get; set; }
        public double? SupplierPrice { get; set; }
    }
}
