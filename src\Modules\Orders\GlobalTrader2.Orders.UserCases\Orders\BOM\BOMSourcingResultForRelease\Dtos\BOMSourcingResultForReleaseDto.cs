using GlobalTrader2.Dto.Converters.DateTime;
using GlobalTrader2.Dto.Converters.Numeric;
using System.Text.Json.Serialization;

namespace GlobalTrader2.Orders.UserCases.Orders.BOM.BOMSourcingResultForRelease.Dtos
{
    public class BOMSourcingResultForReleaseDto
    {
        public int SourcingResultId { get; set; }
        public int CustomerRequirementNo { get; set; }
        public string FullPart { get; set; } = string.Empty;
        public string Part { get; set; } = string.Empty;
        [JsonConverter(typeof(F5NumericWithCultureFormatterConverter<double>))]
        public double? Price { get; set; }
        public string? CurrencyCode { get; set; } = string.Empty;
        [JsonConverter(typeof(ShortDateWithCultureFormatterConverter))]
        public DateTime? DeliveryDate { get; set; } = DateTime.MinValue;
        [JsonConverter(typeof(F5NumericWithCultureFormatterConverter<double>))]
        public double? BuyPrice { get; set; }

        public string? BuyCurrencyCode { get; set; } = string.Empty;
        public string? StrBuyPrice { get; set; } = string.Empty;
        public string? StrSellPrice { get; set; } = string.Empty;
    }
}