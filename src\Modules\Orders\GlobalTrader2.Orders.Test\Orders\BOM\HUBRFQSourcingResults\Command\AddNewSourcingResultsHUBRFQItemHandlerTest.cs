using AutoFixture;
using GlobalTrader2.Core;
using GlobalTrader2.Orders.UserCases.Orders.BOM.HUBRFQSourcingResults.Command;
using Microsoft.Data.SqlClient;
using Moq;
using System.Data;

namespace GlobalTrader2.Orders.Test.Orders.BOM.HUBRFQSourcingResults.Command
{
    public class AddNewSourcingResultsHUBRFQItemHandlerTest
    {
        private readonly Mock<IBaseRepository<object>> _mockRepository;
        private readonly AddNewSourcingResultsHUBRFQItemHandler _handler;
        private readonly IFixture _fixture;

        public AddNewSourcingResultsHUBRFQItemHandlerTest()
        {
            _mockRepository = new Mock<IBaseRepository<object>>();
            _handler = new AddNewSourcingResultsHUBRFQItemHandler(_mockRepository.Object);
            _fixture = new Fixture();
        }

        [Fact]
        public async Task Handle_WithValidCommand_ReturnsSuccessWithSourcingId()
        {
            // Arrange
            var command = _fixture.Create<AddNewSourcingResultsHUBRFQItemCommand>();
            var expectedSourcingId = 12345;
            var expectedMessage = "Success";

            _mockRepository.Setup(r => r.ExecuteSqlRawAsync(It.IsAny<string>(), It.IsAny<object[]>()))
                .ReturnsAsync((string query, SqlParameter[] parameters) =>
                {
                    parameters[parameters.Length - 2].Value = expectedSourcingId;
                    return 1;
                });            
            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.True(result.Success);
            Assert.Equal(expectedSourcingId, result.Data);
        }

        [Fact]
        public async Task Handle_WithNullOutputParameters_ReturnsMessage()
        {
            // Arrange
            var command = _fixture.Create<AddNewSourcingResultsHUBRFQItemCommand>();
            var expectedSourcingId = 12345;
            var expectedMessage = "Success";

            _mockRepository.Setup(r => r.ExecuteSqlRawAsync(It.IsAny<string>(), It.IsAny<object[]>()))
                .ReturnsAsync((string query, SqlParameter[] parameters) =>
                {
                    parameters.Last().Value = expectedMessage;
                    return 1;
                });
            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.True(result.Success);
            Assert.Equal(expectedMessage, result.Message);
        }
    }
}
