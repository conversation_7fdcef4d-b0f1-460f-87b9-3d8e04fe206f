@using GlobalTrader2.Core.Helpers
@using System.Globalization
@model GlobalTrader2.Dto.Templates.HubRrqPrintTemplate
<!DOCTYPE html>
<html lang="en">

    <head>
        <title>HUBRFQ Print Template</title>
        <style type="text/css">
            .Headerclass {
                font-family: Arial, Helvetica, sans-serif;
                font-size: 13px;
                font-style: normal;
                font-weight: bold;
                padding: 20px;
                text-align: center;
            }

            .Headerclass2 {
                font-family: Arial, Helvetica, sans-serif;
                font-size: 13px;
                font-style: normal;
                font-weight: bold;
            }

            .Textclass {
                font-family: Arial, Helvetica, sans-serif;
                font-size: 11px;
                font-style: normal;
            }

            .style1 {
                font-family: Arial, Helvetica, sans-serif;
                font-size: 11px;
                font-style: normal;
                font-weight: bold;
            }

            .style2 {
                font-family: Arial, Helvetica, sans-serif;
                font-weight: bold;
                font-size: 24px;
            }

            body {
                margin: 30px 10px;
            }

            table {
                border-collapse: separate !important;
                border-spacing: 1px !important;
                width: 100%;
                border: 1px solid #000000;
            }

            table th,
            table td {
                border: 1px solid #000 !important;
                text-align: left;
                font-size: 12px;
                padding: 1px;
            }

            table th {
                background-color: #f3f4f6;
                font-weight: bold;
            }
        </style>
    </head>

    <body class="email">
        <table>
            <tr>
                <th colspan="2" class="Headerclass" scope="colgroup">
                    <strong>HUBRFQ</strong>
                </th>
            </tr>
            <tr>
                <th class="Textclass" style="width:50%" scope="row">
                    <strong>Code </strong>: @Model.Code
                </th>
                <th class="Textclass" scope="row">
                    <strong>Contact</strong> : @Model.Contact
                </th>
            </tr>
            <tr>
                <th class="Textclass" scope="row">
                    <strong>Name</strong> : @Model.Name &nbsp;
                </th>
                <th class="Textclass" scope="row">
                    <strong>Currency</strong> : @Model.Currency
                </th>
            </tr>
            <tr>
                <th class="Textclass" scope="row">
                    <strong>Company</strong> : @Model.Company &nbsp;
                </th>
                <th class="Textclass" scope="row">
                    <strong>Quote Required </strong>: @Functions.FormatDate(Model.QuoteRequired)
                </th>
            </tr>
            <tr>
                <th colspan="2" scope="row">
                    <strong>Notes:&nbsp;</strong>@Html.Raw(Model.Notes)
                </th>
            </tr>
        </table>
        <table>
            <tr>
                <th style="text-align: left; vertical-align: top" class="Textclass" scope="col">Req <br /> Type</th>
                <th style="text-align: left; vertical-align: top" class="Textclass" scope="col"> Quantity <br /> Traceability</th>
                <th style="text-align: left; vertical-align: top" class="Textclass" scope="col">Part No <br /> Customer Part</th>
                <th style="text-align: left; vertical-align: top" class="Textclass" scope="col"> Mfr <br /> DC </th>
                <th style="text-align: left; vertical-align: top" class="Textclass" scope="col">Product <br /> Package </th>
                <th style="text-align: left; vertical-align: top" class="Textclass" scope="col">Customer <br /> Required </th>
                <th style="text-align: left; vertical-align: top" class="Textclass" scope="col"> Target Price <br /> Salesperson </th>
                <th style="text-align: left; vertical-align: top" class="Textclass" scope="col"> Notes</th>
                <th style="text-align: left; vertical-align: top" class="Textclass" scope="col"> MSL <br /> Factory Sealed</th>
                <th style="text-align: left; vertical-align: top" class="Textclass" scope="col"> Alternatives Accepted <br /> Repeat
                    business</th>
                <th style="text-align: left; vertical-align: top" class="Textclass" scope="col"> Lytica Manufacturer</th>
                <th style="text-align: left; vertical-align: top" class="Textclass" scope="col"> Lytica Avg. Price <br /> (50th
                    Percentile)</th>
                <th style="text-align: left; vertical-align: top" class="Textclass" scope="col"> Lytica Target Price <br /> (70th
                    Percentile)</th>
                <th style="text-align: left; vertical-align: top" class="Textclass" scope="col"> Lytica Market Leading</th>
            </tr>
            @foreach (var cReq in Model.CustomerRequirements)
            {
                <tr>
                    <td class="Textclass">@cReq.CustomerRequirementNumber<br />@cReq.ReqTypeText</td>
                    <td class="Textclass">@cReq.Quantity<br />@cReq.ReqForTraceabilityText</td>
                    <td class="Textclass">@cReq.Part<br />@cReq.CustomerPart</td>
                    <td class="Textclass">@cReq.ManufacturerCode<br />@cReq.DateCode</td>
                    <td class="Textclass">@cReq.ProductName<br />@cReq.PackageName</td>
                    <td class="Textclass">@(Model.IsPoHub ? cReq.ClientName : cReq.CompanyName)<br />@Functions.FormatDate(cReq.DatePromised)</td>
                    <td class="Textclass">@Functions.FormatCurrency(cReq.ConvertedTargetValue, cReq.BOMCurrencyCode)<br />@cReq.SalesmanName</td>
                    <td class="Textclass">@Html.Raw(@cReq.Instructions)</td>
                    <td class="Textclass">@cReq.MSL<br />@(cReq.FactorySealed == false ? "NO" : "YES")</td>
                    <td class="Textclass">@(cReq.AlternativesAccepted == false ? "NO" : "YES")<br />@(cReq.RepeatBusiness == false ? "NO" : "YES")</td>
                    <td class="Textclass">@cReq.LyticaManufacturerRef</td>
                    <td class="Textclass">@Functions.FormatCurrency(cReq.LyticaAveragePrice, CultureInfo.CurrentCulture, null, 2, false)</td>
                    <td class="Textclass">@Functions.FormatCurrency(cReq.LyticaTargetPrice, CultureInfo.CurrentCulture, null, 2, false)</td>
                    <td class="Textclass">@Functions.FormatCurrency(cReq.LyticaMarketLeading, CultureInfo.CurrentCulture, null, 2, false)</td>
                </tr>
            }
        </table>
    </body>

</html>
