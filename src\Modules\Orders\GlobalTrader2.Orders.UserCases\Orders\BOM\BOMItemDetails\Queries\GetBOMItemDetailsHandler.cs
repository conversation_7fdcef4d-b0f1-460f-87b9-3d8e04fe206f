using GlobalTrader2.Core.Helpers;
using GlobalTrader2.Orders.UserCases.Orders.BOM.BOMItemDetails.Queries.Dtos;
using System.Globalization;

namespace GlobalTrader2.Orders.UserCases.Orders.BOM.BOMItemDetails.Queries
{
    public class GetBomItemDetailsHandler : IRequestHandler<GetBomItemDetailsQuery, BaseResponse<BomItemDetailsDto>>
    {
        private readonly IBaseRepository<CustomerRequirementBomModel> _customerRequirementRepository;
        private readonly IBaseRepository<KubPermissionCheckModel> _kubPermissionRepository;
        private readonly IBaseRepository<KubAssistanceModel> _kubAssistanceRepository;
        private readonly IMapper _mapper;

        public GetBomItemDetailsHandler(
            IBaseRepository<CustomerRequirementBomModel> customerRequirementRepository,
            IBaseRepository<KubPermissionCheckModel> kubPermissionRepository,
            IBaseRepository<KubAssistanceModel> kubAssistanceRepository,
            IMapper mapper)
        {
            _customerRequirementRepository = customerRequirementRepository;
            _kubPermissionRepository = kubPermissionRepository;
            _kubAssistanceRepository = kubAssistanceRepository;
            _mapper = mapper;
        }

        public async Task<BaseResponse<BomItemDetailsDto>> Handle(GetBomItemDetailsQuery request, CancellationToken cancellationToken)
        {
            var parameters = new List<SqlParameter>
            {
                new SqlParameter("@CustomerRequirementId", SqlDbType.Int) { Value = request.Id },
                new SqlParameter("@ClientNo", SqlDbType.Int) { Value = request.ClientId }
            };

            var customerRequirements = await _customerRequirementRepository.SqlQueryRawAsync(
                $"{StoredProcedures.Select_CustomerRequirementBOM} @CustomerRequirementId, @ClientNo",
                parameters.ToArray());

            if (!customerRequirements.Any())
            {
                return new BaseResponse<BomItemDetailsDto>
                {
                    Success = false,
                    Message = "Customer requirement not found"
                };
            }

            var cReq = customerRequirements[0];

            var bomItemDetails = _mapper.Map<BomItemDetailsDto>(cReq);
            bomItemDetails.CompetitorBestOffer = Functions.FormatCurrency(cReq.CompetitorBestOffer, CultureInfo.CurrentCulture, cReq.CurrencyCode, 5, true);

            var partNo = Functions.GetFullPart(cReq.FullPart ?? string.Empty);
            bomItemDetails.IsAllowedEnableKub = await CheckKubPermissions(
                partNo,
                request.ClientId,
                request.Id,
                cReq.ManufacturerNo ?? 0,
                cReq.ManufacturerName ?? string.Empty);

            if (bomItemDetails.IsAllowedEnableKub)
            {
                bomItemDetails.KubAssistance = await GetKubAssistanceData(cReq, request.ClientId);
            }

            bomItemDetails.IsPOHub = request.IsPoHub;

            return new BaseResponse<BomItemDetailsDto>
            {
                Success = true,
                Data = bomItemDetails
            };
        }

        private async Task<bool> CheckKubPermissions(string partNo, int clientId, int bomId, int manufacturerNo, string manufacturerName)
        {
            var parameters = new List<SqlParameter>
            {
                new SqlParameter("@PartNo", SqlDbType.NVarChar, 100) { Value = partNo },
                new SqlParameter("@ClientID", SqlDbType.Int) { Value = clientId },
                new SqlParameter("@BOMID", SqlDbType.Int) { Value = bomId },
                new SqlParameter("@IsHubRFQ", SqlDbType.Bit) { Value = true },
                new SqlParameter("@ManufacturerID", SqlDbType.Int) { Value = manufacturerNo },
                new SqlParameter("@ManufacturerName", SqlDbType.NVarChar, 500) { Value = manufacturerName }
            };

            var results = await _kubPermissionRepository.SqlQueryRawAsync(
                $"{StoredProcedures.Check_KubEnableForBOMPart} @PartNo, @ClientID, @BOMID, @IsHubRFQ, @ManufacturerID, @ManufacturerName",
                parameters.ToArray()
            );

            if (results.Any())
            {
                var result = results[0];
                return result.IsAllowedEnable;
            }

            return false;
        }

        private async Task<KubAssistanceDto> GetKubAssistanceData(CustomerRequirementBomModel cReq, int clientId)
        {
            var fullPart = Functions.GetFullPart(cReq.FullPart ?? string.Empty);
            var kubParameters = new List<SqlParameter>
            {
                new SqlParameter("@PartNumber", SqlDbType.NVarChar, 100) { Value = fullPart },
                new SqlParameter("@ClientID", SqlDbType.Int) { Value = clientId },
                new SqlParameter("@BOMManagerID", SqlDbType.Int) { Value = cReq.BOMNo ?? 0 },
                new SqlParameter("@IsHubRFQ", SqlDbType.Bit) { Value = true },
                new SqlParameter("@ManufacturerID", SqlDbType.Int) { Value = cReq.ManufacturerNo ?? 0 },
                new SqlParameter("@ManufacturerName", SqlDbType.NVarChar, 500) { Value = cReq.ManufacturerName ?? string.Empty }
            };

            var kubResults = await _kubAssistanceRepository.SqlQueryRawAsync(
                $"{StoredProcedures.Select_KubAssistanceForBOMManager} @PartNumber, @ClientID, @BOMManagerID, @IsHubRFQ, @ManufacturerID, @ManufacturerName",
                kubParameters.ToArray()
            );

            if (kubResults.Any())
            {
                var kubData = kubResults[0];
                return _mapper.Map<KubAssistanceDto>(kubData);
            }

            return new KubAssistanceDto();
        }
    }
}
