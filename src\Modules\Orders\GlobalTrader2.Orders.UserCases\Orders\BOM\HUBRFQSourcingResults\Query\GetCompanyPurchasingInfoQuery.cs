﻿using GlobalTrader2.Dto.Base;
using GlobalTrader2.Dto.Company;
using GlobalTrader2.Dto.Currency;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GlobalTrader2.Orders.UserCases.Orders.BOM.HUBRFQSourcingResults.Query
{
    public class GetCompanyPurchasingInfoQuery : IRequest<BaseResponse<CompanyPurchasingInfoDto>>
    {
        public int? CompanyId { get; set; }
        public int? ClientId { get; set; }
    }
}
