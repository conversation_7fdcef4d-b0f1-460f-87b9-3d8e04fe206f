@page
@using GlobalTrader2.Orders.UI.Areas.Orders.Pages.CustomerRequirement.Components.AddNewHUBRFQComponent
@using GlobalTrader2.Orders.UI.Areas.Orders.Pages.HUBRFQ.AddNewHUBRFQ
@using GlobalTrader2.SharedUI.Areas.Common.Pages.Shared.Components.OrderQuickJumpMenu
@using GlobalTrader2.SharedUI.Areas.Common.Pages.Shared.Components.OrderSelectionMenu
@using GlobalTrader2.SharedUI.Areas.Common.Pages.Shared.Components.SourcingLinks
@using GlobalTrader2.SharedUI.Services
@model IndexModel
@inject IStringLocalizer<GlobalTrader2.SharedUI.CommonResources> _commonLocalizer
@inject IViewLocalizer _localizer
@inject SettingManager _settingManager

@{
    var colLabelSize = 3;
    var colInputSize = 12 - colLabelSize;
}

@{
    ViewData["Title"] = "Add New HUBRFQ";
}

@section LeftSidebar {
    @await Component.InvokeAsync(nameof(OrderQuickJumpMenu))
    @await Component.InvokeAsync(nameof(OrderSelectionMenu))
    @await Component.InvokeAsync(nameof(SourcingLinksMenu))
}

<div id="add-new-hubrfq-container" class="page-content-container fs-12">
    <div class="d-flex justify-content-between align-items-end border-bottom mt-2">
        <h2 class="page-primary-title mb-0">@_localizer["Add New HUBRFQ"]</h2>
    </div>

    <div>
        <div class="mb-2 mt-2">
            <span id="add-edit-form-message">@_localizer["Enter the details of the new HUBRFQ and press "]</span>
            <strong>@_commonLocalizer["Save"]</strong>
        </div>

        <form method="post" id="add-hubrfq-form">
            <div class="form-error-summary" style="display: none;">
                <img src="~/img/icons/x-octagon.svg" alt="X-icon" />
                <div>
                    <p>@_commonLocalizer["There were some problems with your form."]</p>
                    <p>@_commonLocalizer["Please check below and try again."]</p>
                    <p id="hubrfq-validation-message" class='d-none'></p>
                </div>
            </div>
            @await Component.InvokeAsync(nameof(AddNewHubrfqForm))
        </form>
        <div class="text-loader m-auto" id="addnewhubrfq-step-2-loading-content" style="display: none"></div>

        <div id="add-new-hubrfq-footer" class="my-2 d-flex gap-2">
            <button type="button" id="add-new-hubrfq-save" class="btn btn-save btn-primary fw-normal">
                <img src="/img/icons/save.svg" data-action-btn="save" alt="save">
                @_commonLocalizer["Save"]
            </button>

            <button type="button" id="add-new-hubrfq-cancel" class="btn btn-cancel btn-danger fw-normal">
                <img src="/img/icons/slash.svg" data-action-btn="cancel" alt="cancel">
                @_commonLocalizer["Cancel"]
            </button>
        </div>
    </div>
</div>
@await Html.PartialAsync("Partials/_HUBRFQDetail")

@section Scripts {
    @await Html.PartialAsync("Partials/_DataTablesScriptsPartial")
    <script src="@_settingManager.GetCdnUrl("/js/widgets/custom-date-picker.js")" asp-append-version="true"></script>

    <environment include="Development">
        <script src="/js/modules/orders/hubrfq/containers/add-new-hubrfq.js" type="module" asp-append-version="true"></script>
    </environment>
    <environment exclude="Development">
        <script src="/dist/js/orders-add-new-hubrfq.bundle.js" type="module" asp-append-version="true"></script>
    </environment>

    <script src="@_settingManager.GetCdnUrl("/js/directives/input-directive.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/widgets/common-table.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/widgets/drop-down.js")" asp-append-version="true"></script>
} 