
using GlobalTrader2.Orders.UserCases.Orders.SOLine.Commands.EditSalesOrderLine;

namespace GlobalTrader2.Orders.UserCases.Test.Orders.SOLine.Commands.EdtiAllSalesOrderLines;

public class EditAllSalesOrderLinesValidatorTests
{
    private readonly EditAllSalesOrderLinesValidator _validator = new();

    [Fact]
    public void Should_Have_Error_When_Fields_Are_Default_Or_Null()
    {
        var model = new EditAllSalesOrderLinesCommand
        {
            PromiseReasonNo = 0,
            OldEarliestDatePromised = default,
            AllowEditDatePromisedBetweenCurrentMonthAndEnd = null,
            IsSOAuthorized = null,
            IsSOAutoAuthorized = null,
            DatePromised = DateTime.Today.AddDays(-1)
        };

        var result = _validator.TestValidate(model);
        result.ShouldHaveValidationErrorFor(x => x.PromiseReasonNo);
        result.ShouldHaveValidationErrorFor(x => x.OldEarliestDatePromised);
        result.ShouldHaveValidationErrorFor(x => x.AllowEditDatePromisedBetweenCurrentMonthAndEnd);
        result.ShouldHaveValidationErrorFor(x => x.IsSOAuthorized);
        result.ShouldHaveValidationErrorFor(x => x.IsSOAutoAuthorized);
        result.ShouldHaveValidationErrorFor(x => x.DatePromised)
              .WithErrorMessage("Promised date should be between current date and end of promised date month");
    }

    [Fact]
    public void Should_Not_Have_Error_When_Valid()
    {
        var model = new EditAllSalesOrderLinesCommand
        {
            PromiseReasonNo = 1,
            OldEarliestDatePromised = DateTime.Today,
            AllowEditDatePromisedBetweenCurrentMonthAndEnd = true,
            IsSOAuthorized = true,
            IsSOAutoAuthorized = false,
            DatePromised = DateTime.Today
        };

        var result = _validator.TestValidate(model);
        result.ShouldNotHaveAnyValidationErrors();
    }
}