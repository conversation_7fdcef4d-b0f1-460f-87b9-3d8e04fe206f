﻿using GlobalTrader2.Orders.UserCases.Orders.PurchaseOrder.Queries.GetEprList;
using GlobalTrader2.Orders.UserCases.Orders.PurchaseOrder.Queries.GetEprRejectedLog;

namespace GlobalTrader2.Orders.UserCases.Test.Orders.PurchaseOrder.Queries.GetEprRejectedLog;

public class GetEprRejectedLogHandlerTests
{
    private readonly Mock<IBaseRepository<EprRejectedLogReadModel>> _mockRepository;
    private readonly GetEprRejectedLogHandler _handler;
    private readonly Fixture _fixture;

    public GetEprRejectedLogHandlerTests()
    {
        _mockRepository = new Mock<IBaseRepository<EprRejectedLogReadModel>>();
        _handler = new GetEprRejectedLogHandler(_mockRepository.Object);
        _fixture = new Fixture();
    }

    [Fact]
    public async Task Handle_GivenRepositoryReturnsNull_ShouldThrowArgumentException()
    {
        // Arrange
        var query = new GetEprRejectedLogQuery(EprNo: 1);

        _mockRepository
            .Setup(r => r.SqlQueryRawAsync(It.IsAny<string>(), It.IsAny<object[]>()))
            .ReturnsAsync((List<EprRejectedLogReadModel>)null!);

        // Act & Assert
        var ex = await Assert.ThrowsAsync<ArgumentException>(() => _handler.Handle(query, CancellationToken.None));

        Assert.Equal($"Cannot find EPR Rejected Log with id {query.EprNo}", ex.Message);
    }

    [Fact]
    public async Task Handle_WhenEprFound_ShouldMapAndReturnCorrectly()
    {
        // Arrange
        var query = new GetEprRejectedLogQuery(EprNo: 1);

        var readModel = _fixture.Create<EprRejectedLogReadModel>();
        var mockResult = new List<EprRejectedLogReadModel> { readModel };

        _mockRepository
            .Setup(r => r.SqlQueryRawAsync(It.IsAny<string>(), It.IsAny<object[]>()))
            .ReturnsAsync(mockResult);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.Success);
        Assert.NotNull(result.Data);
        _mockRepository.Verify(r => r.SqlQueryRawAsync(
            It.IsAny<string>(),
            It.Is<object[]>(p => p.Length == 1)),
            Times.Once);
    }
}
