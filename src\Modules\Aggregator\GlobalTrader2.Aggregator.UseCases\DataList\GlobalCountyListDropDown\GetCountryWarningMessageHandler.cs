﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GlobalTrader2.Aggregator.UseCases.DataList.GlobalCountyListDropDown
{
    public class GetCountryWarningMessageHandler : IRequestHandler<GetCountryWarningMessageQuery, BaseResponse<string>>
    {
        private readonly IBaseRepository<Country> _baseRepository;

        public GetCountryWarningMessageHandler(IBaseRepository<Country> baseRepository)
        {
            _baseRepository = baseRepository;
        }

        public async Task<BaseResponse<string>> Handle(GetCountryWarningMessageQuery request, CancellationToken cancellationToken)
        {
            var country = await _baseRepository.GetAsync(s => s.ClientNo == request.ClientId && s.GlobalCountryNo == request.CountryId);
            return new BaseResponse<string>
            {
                Success = true,
                Data = country?.WarningMessage,
            };
        }
    }
}
