namespace GlobalTrader2.Dto.SalesOrderLine
{
    public class GetCustomerRequirementDto
    {
        public int? Quantity { get; set; }
        public string? Part { get; set; }
        public int? SourcingResultNo { get; set; }
        public string? Price { get; set; }
        public string? DateCode { get; set; }
        public string? ManufacturerName { get; set; }
        public int? ManufacturerNo { get; set; }
        public int? ProductNo { get; set; }
        public int? PackageNo { get; set; }
        public int? Rohs { get; set; }
        public string? RequirementNotes { get; set; }
        public string? ProductDescription { get; set; }
        public string? Msl { get; set; }
        public string? PackageDescription { get; set; }
        public string? ECCNCode { get; set; }
        public bool? AS6081 { get; set; }

    }
}