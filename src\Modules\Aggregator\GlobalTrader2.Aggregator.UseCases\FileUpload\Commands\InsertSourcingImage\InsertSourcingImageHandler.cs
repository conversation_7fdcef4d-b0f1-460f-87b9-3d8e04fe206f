﻿namespace GlobalTrader2.Aggregator.UseCases.FileUpload.Commands.InsertSourcingImage
{
    public class InsertSourcingImageHandler : IRequestHandler<InsertSourcingImageCommand, BaseResponse<bool>>
    {
        private readonly IBaseRepository<SourcingImage> _repository;
        public InsertSourcingImageHandler(IBaseRepository<SourcingImage> repository)
        {
            _repository = repository;
        }

        public async Task<BaseResponse<bool>> Handle(InsertSourcingImageCommand request, CancellationToken cancellationToken)
        {
            var response = new BaseResponse<bool>();

            var sourcingImage = new SourcingImage
            {
                SourcingResultNo = request.SourcingNo,
                Caption = request.Caption,
                ImageName = request.ImageName,
                UpdatedBy = request.UpdateBy,
                DLUP = DateTime.UtcNow  
            };  

            await _repository.AddAsync(sourcingImage);

            response.Success = true;
            response.Data = true;

            return response;
        }
    }
}
