﻿using FluentAssertions;
using GlobalTrader2.Aggregator.UseCases.FileUpload.Commands.InsertSourcingImage;
using GlobalTrader2.Aggregator.UseCases.FileUpload.Commands.PostMultipleMediaDocument;
using GlobalTrader2.Core.Bases;
using GlobalTrader2.Core.Constants;
using GlobalTrader2.Core.Interfaces;
using GlobalTrader2.Dto.File;
using MediatR;
using Moq;

namespace GlobalTrader2.Aggregator.Test.FileUpload.Commands
{
    public class PostMultipleMediaDocumentHandlerTests
    {
        [Fact]
        public async Task Handle_QCSection_WithValidFiles_ShouldSendInsertSourcingImageAndUploadToBlob()
        {
            // Arrange
            var mediatorMock = new Mock<IMediator>();
            var blobStorageMock = new Mock<IBlobStorageService>();

            var handler = new PostMultipleMediaDocumentHandler(blobStorageMock.Object, mediatorMock.Object);

            var mockStream = new MemoryStream(new byte[] { 0x1, 0x2, 0x3 });
            var fileUploadItems = new List<DragDropFileUploadItem>
            {
                new DragDropFileUploadItem
                {
                    Caption = "Test Caption",
                    FileName = "image.jpg",
                    File = mockStream
                },
                new DragDropFileUploadItem
                {
                    Caption = "Skipped File",
                    FileName = null, // should be skipped
                    File = null
                }
            };

            var command = new PostMultipleMediaDocumentCommand
            {
                SectionName = ImageSourceFromConstant.QC,
                FileUploadItems = fileUploadItems,
                RefId = 123,
                UpdateBy = 456
            };

            // Setup Mediator.Send to return successful response
            mediatorMock.Setup(m => m.Send(It.IsAny<InsertSourcingImageCommand>(), It.IsAny<CancellationToken>()))
                        .ReturnsAsync(new BaseResponse<bool> { Success = true });

            // Act
            var result = await handler.Handle(command, CancellationToken.None);

            // Assert
            mediatorMock.Verify(m => m.Send(It.Is<InsertSourcingImageCommand>(q =>
                q.SourcingNo == 123 &&
                q.Caption == "Test Caption" &&
                q.ImageName == "image.jpg" &&
                q.UpdateBy == 456
            ), It.IsAny<CancellationToken>()), Times.Once);

            blobStorageMock.Verify(b => b.UploadBlobAsync(
                BlobStorage.MediaContainerName,
                "image.jpg",
                "image/jpeg",
                mockStream
            ), Times.Once);

            result.Should().NotBeNull();
            result.Success.Should().BeTrue();
            result.Data.Should().BeTrue();
        }
    }
}
