﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace GlobalTrader2.Orders.UserCases.Orders.BOM.HUBRFQSourcingResults.Command
{
    public class AddNewSourcingResultsHUBRFQItemHandler : IRequestHandler<AddNewSourcingResultsHUBRFQItemCommand, BaseResponse<int>>
    {
        private readonly IBaseRepository<object> _repository;

        public AddNewSourcingResultsHUBRFQItemHandler(IBaseRepository<object> repository)
        {
            _repository = repository;
        }

        public async Task<BaseResponse<int>> Handle(AddNewSourcingResultsHUBRFQItemCommand request, CancellationToken cancellationToken)
        {
            var messageOutput = new SqlParameter("@LinkCurrencyMsg", SqlDbType.VarChar, 150) { Direction = ParameterDirection.Output };
            var sourcingIdOutput = new SqlParameter("@SourcingResultId", SqlDbType.Int) { Direction = ParameterDirection.Output };

            SqlParameter[] param =
            [
                new SqlParameter("@CustomerRequirementNo", SqlDbType.Int) { Value = request.CustomerRequirementNo },
                new SqlParameter("@TypeName", SqlDbType.NVarChar, 40) { Value = (object?)request.TypeName ?? DBNull.Value },
                new SqlParameter("@Notes", SqlDbType.NVarChar, 128) { Value = (object?)request.Notes ?? DBNull.Value },
                new SqlParameter("@Part", SqlDbType.NVarChar, 30) { Value = request.Part },
                new SqlParameter("@ManufacturerNo", SqlDbType.Int) { Value = (object?)request.ManufacturerNo ?? DBNull.Value },
                new SqlParameter("@DateCode", SqlDbType.NVarChar, 5) { Value = (object?)request.DateCode ?? DBNull.Value },
                new SqlParameter("@ProductNo", SqlDbType.Int) { Value = (object?)request.ProductNo ?? DBNull.Value },
                new SqlParameter("@PackageNo", SqlDbType.Int) { Value = (object?)request.PackageNo ?? DBNull.Value },
                new SqlParameter("@Quantity", SqlDbType.Int) { Value = request.Quantity },
                new SqlParameter("@Price", SqlDbType.Float) { Value = request.Price },
                new SqlParameter("@CurrencyNo", SqlDbType.Int) { Value = (object?)request.CurrencyNo ?? DBNull.Value },
                new SqlParameter("@OriginalEntryDate", SqlDbType.DateTime) { Value = (object?)request.OriginalEntryDate ?? DBNull.Value },
                new SqlParameter("@Salesman", SqlDbType.Int) { Value = (object?)request.Salesman ?? DBNull.Value },
                new SqlParameter("@OfferStatusNo", SqlDbType.Int) { Value = (object?)request.OfferStatusNo ?? DBNull.Value },
                new SqlParameter("@SupplierNo", SqlDbType.Int) { Value = (object?)request.SupplierNo ?? DBNull.Value },
                new SqlParameter("@ROHS", SqlDbType.TinyInt) { Value = (object?)request.ROHS ?? DBNull.Value },
                new SqlParameter("@ClientNo", SqlDbType.Int) { Value = request.ClientNo },
                new SqlParameter("@UpdatedBy", SqlDbType.Int) { Value = (object?)request.UpdatedBy ?? DBNull.Value },
                new SqlParameter("@SuplierPrice", SqlDbType.Float) { Value = (object?)request.SuplierPrice ?? DBNull.Value },
                new SqlParameter("@EstimatedShippingCost", SqlDbType.Float) { Value = request.EstimatedShippingCost },
                new SqlParameter("@DeliveryDate", SqlDbType.DateTime) { Value = (object?)request.DeliveryDate ?? DBNull.Value },
                new SqlParameter("@PUHUB", SqlDbType.Bit) { Value = request.PUHUB },
                new SqlParameter("@SPQ", SqlDbType.NVarChar, 50) { Value = (object?)request.SPQ ?? DBNull.Value },
                new SqlParameter("@LeadTime", SqlDbType.NVarChar, 50) { Value = (object?)request.LeadTime ?? DBNull.Value },
                new SqlParameter("@ROHSStatus", SqlDbType.NVarChar, 50) { Value = (object?)request.ROHSStatus ?? DBNull.Value },
                new SqlParameter("@FactorySealed", SqlDbType.NVarChar, 50) { Value = (object?)request.FactorySealed ?? DBNull.Value },
                new SqlParameter("@MSL", SqlDbType.NVarChar, 50) { Value = (object?)request.MSL ?? DBNull.Value },
                new SqlParameter("@SupplierTotalQSA", SqlDbType.NVarChar, 30) { Value = (object?)request.SupplierTotalQSA ?? DBNull.Value },
                new SqlParameter("@SupplierLTB", SqlDbType.NVarChar, 30) { Value = (object?)request.SupplierLTB ?? DBNull.Value },
                new SqlParameter("@SupplierMOQ", SqlDbType.NVarChar, 30) { Value = (object?)request.SupplierMOQ ?? DBNull.Value },
                new SqlParameter("@RegionNo", SqlDbType.Int) { Value = (object?)request.RegionNo ?? DBNull.Value },
                new SqlParameter("@MSLLevelNo", SqlDbType.Int) { Value = (object?)request.MSLLevelNo ?? DBNull.Value },
                new SqlParameter("@SupplierWarranty", SqlDbType.Int) { Value = (object?)request.SupplierWarranty ?? DBNull.Value },
                new SqlParameter("@isTestingRecommended", SqlDbType.Bit) { Value = request.IsTestingRecommended },
                new SqlParameter("@IHSCountryOfOriginNo", SqlDbType.Int) { Value = (object?)request.IHSCountryOfOriginNo ?? DBNull.Value },
                new SqlParameter("@TypeOfSupplier", SqlDbType.Int) { Value = (object?)request.TypeOfSupplier ?? DBNull.Value },
                new SqlParameter("@ReasonForSupplier", SqlDbType.Int) { Value = (object?)request.ReasonForSupplier ?? DBNull.Value },
                new SqlParameter("@RiskOfSupplier", SqlDbType.Int) { Value = (object?)request.RiskOfSupplier ?? DBNull.Value },
                new SqlParameter("@CountryNo", SqlDbType.Int) { Value = (object?)request.CountryNo ?? DBNull.Value },
                new SqlParameter("@SellPriceLessReason", SqlDbType.NVarChar, 128) { Value = (object?)request.SellPriceLessReason ?? DBNull.Value },
                sourcingIdOutput,
                messageOutput
            ];


            string procedureName = StoredProcedures.Insert_SourcingResultWithOffer_V2;
            var queryStr = $"{procedureName} @CustomerRequirementNo, @TypeName, @Notes, @Part, @ManufacturerNo, @DateCode, @ProductNo, @PackageNo, @Quantity, @Price, @CurrencyNo, @OriginalEntryDate, @Salesman," +
                $"@OfferStatusNo, @SupplierNo, @ROHS, @ClientNo, @UpdatedBy, @SuplierPrice, @EstimatedShippingCost, @DeliveryDate, @PUHUB, @SPQ, @LeadTime, @ROHSStatus, @FactorySealed, @MSL," +
                $"@SupplierTotalQSA, @SupplierLTB, @SupplierMOQ, @RegionNo, @MSLLevelNo, @SupplierWarranty, @isTestingRecommended, @IHSCountryOfOriginNo, @TypeOfSupplier, @ReasonForSupplier, @RiskOfSupplier," +
                $"@CountryNo, @SellPriceLessReason, @SourcingResultId OUTPUT, @LinkCurrencyMsg OUTPUT";

            await _repository.ExecuteSqlRawAsync(queryStr, param);

            string message = messageOutput.Value?.ToString();
            int sourcingId = sourcingIdOutput.Value != DBNull.Value && sourcingIdOutput.Value != null
                ? Convert.ToInt32(sourcingIdOutput.Value.ToString())
                : 0;

            return new BaseResponse<int>
            {
                Message = message,
                Success = true,
                Data = sourcingId
            };
        }
    }
}
