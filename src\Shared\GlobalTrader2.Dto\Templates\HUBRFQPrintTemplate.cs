using GlobalTrader2.Core.Domain.Entities;

namespace GlobalTrader2.Dto.Templates
{
    public class HubRrqPrintTemplate
    {
        public required string Code { get; set; }
        public required string Name { get; set; }
        public required string Company { get; set; }
        public required string Contact { get; set; }
        public required string Currency { get; set; }
        public required DateTime QuoteRequired { get; set; }
        public required string Notes { get; set; }
        public required bool IsPoHub { get; set; }
        public required IList<HubRrqForMailTemplate> CustomerRequirements { get; set; } = [];
    }

    public class HubRrqForMailTemplate
    {
        public int CustomerRequirementNumber { get; set; }
        public int CustomerRequirementId { get; set; }
        public int Quantity { get; set; }
        public string? CustomerPart { get; set; }
        public string? Part { get; set; }
        public string? DateCode { get; set; }
        public string? ManufacturerCode { get; set; }
        public string? PackageName { get; set; }
        public string? ProductName { get; set; }
        public string? CompanyName { get; set; }
        public string? ClientName { get; set; }
        public DateTime? DatePromised { get; set; }
        public double? ConvertedTargetValue { get; set; }
        public int? CurrencyNo { get; set; }
        public string? BOMCurrencyCode { get; set; }
        public string? SalesmanName { get; set; }
        public string? Instructions { get; set; }
        public string? MSL { get; set; }
        public bool? FactorySealed { get; set; }
        public string? ReqTypeText { get; set; }
        public string? ReqForTraceabilityText { get; set; }
        public string? ReqForTraceability { get; set; }
        public bool? AlternativesAccepted { get; set; }
        public bool? RepeatBusiness { get; set; }
        public byte? AlternateStatus { get; set; }
        public bool? Alternate { get; set; }
        public double? LyticaAveragePrice { get; set; }
        public double? LyticaTargetPrice { get; set; }
        public double? LyticaMarketLeading { get; set; }
        public string? LyticaManufacturerRef { get; set; }
    }
}
