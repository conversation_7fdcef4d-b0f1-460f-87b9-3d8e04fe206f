﻿using GlobalTrader2.Dto.Datatables;
using GlobalTrader2.Dto.Service;
using GlobalTrader2.Orders.UserCases.Orders.ItemSearch.GetItemSearchService;
using System.Globalization;

namespace GlobalTrader2.Orders.UI.Areas.Orders.Controllers
{
    [ApiController]
    [Authorize]
    [Route("api/services")]
    public class ServiceController : ApiBaseController
    {
        private readonly IMediator _mediator;

        public ServiceController(IMediator mediator)
        {
            _mediator = mediator;
        }

        [HttpPost("item-search")]
        public async Task<IActionResult> GetServiceItemSearchAsync([FromBody] GetItemSearchServiceRequest request, CancellationToken cancellation)
        {
            var query = new GetItemSearchServiceQuery()
            {
                ClientId = request.GlobalLoginClientNo.HasValue && request.GlobalLoginClientNo.Value > 0 ? request.GlobalLoginClientNo.Value : ClientId,
                OrderBy = request.OrderBy,
                SortDir = request.SortDir,
                PageIndex = request.Index / request.Size,
                PageSize = request.Size,
                ServiceName = request.ServiceName
            };

            var result = await _mediator.Send(query, cancellation);

            var totalItems = result.Data?.FirstOrDefault()?.RowCnt ?? 0;

            var cultureInfo = new CultureInfo(Culture);

            foreach (var item in result.Data ?? Enumerable.Empty<GetItemSearchServiceDto>())
            {
                item.FormatedCost = Functions.FormatCurrency(item.Cost, cultureInfo, item.ClientBaseCurrencyCode, 5, false);
                item.FormatedPrice = Functions.FormatCurrency(item.Price, cultureInfo, item.ClientBaseCurrencyCode, 5, false);
            }

            var response = new DatatableResponse<IEnumerable<GetItemSearchServiceDto>>()
            {
                Success = result.Success,
                Data = result.Data,
                Draw = request.Draw,
                RecordsTotal = totalItems,
                RecordsFiltered = totalItems,
            };

            return Ok(response);
        }
    }
}
