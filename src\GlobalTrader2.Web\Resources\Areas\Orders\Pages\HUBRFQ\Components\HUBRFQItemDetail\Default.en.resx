﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <!-- COLUMN 1 -->
  <data name="Quantity" xml:space="preserve">
    <value>Quantity</value>
  </data>
  <data name="PartNo" xml:space="preserve">
    <value>Part No</value>
  </data>
  <data name="StockAlert" xml:space="preserve">
    <value>Stock Alert!</value>
  </data>
  <data name="StockCrossClient" xml:space="preserve">
    <value>Stock across all Clients</value>
  </data>
  <data name="InStock" xml:space="preserve">
    <value>In Stock</value>
  </data>
  <data name="OnOrder" xml:space="preserve">
    <value>On Order</value>
  </data>
  <data name="Allocated" xml:space="preserve">
    <value>Allocated</value>
  </data>
  <data name="Available" xml:space="preserve">
    <value>Available</value>
  </data>
  <data name="AS6081" xml:space="preserve">
    <value>Inhouse AS6081 testing required?</value>
  </data>
  <data name="ROHS" xml:space="preserve">
    <value>RoHS</value>
  </data>
  <data name="CusPartNo" xml:space="preserve">
    <value>Cust Part No</value>
  </data>
  <data name="Manufacturer" xml:space="preserve">
    <value>Manufacturer</value>
  </data>
  <data name="DateCode" xml:space="preserve">
    <value>Date Code</value>
  </data>
  <data name="Product" xml:space="preserve">
    <value>Product</value>
  </data>
  <data name="DutyCodeRate" xml:space="preserve">
    <value>Duty Code (Rate %)</value>
  </data>
  <data name="Package" xml:space="preserve">
    <value>Package</value>
  </data>
  <data name="PartWatch" xml:space="preserve">
    <value>PartWatch</value>
  </data>
  <data name="FactorySealed" xml:space="preserve">
    <value>Factory Sealed</value>
  </data>
  <data name="MSL" xml:space="preserve">
    <value>MSL</value>
  </data>
  <data name="NoBidNote" xml:space="preserve">
    <value>No-Bid Notes</value>
  </data>
  <data name="CountryOfOrigin" xml:space="preserve">
    <value>Country Of Origin</value>
  </data>
  <data name="PartStatus" xml:space="preserve">
    <value>Part Status</value>
  </data>
  <data name="IHSProduct" xml:space="preserve">
    <value>IHS Product</value>
  </data>
  <data name="HTSCode" xml:space="preserve">
    <value>HTS Code</value>
  </data>
  <data name="ECCNCode" xml:space="preserve">
    <value>ECCN Code</value>
  </data>
  <data name="PackagingSize" xml:space="preserve">
    <value>Packaging Size</value>
  </data>
  <data name="Descriptions" xml:space="preserve">
    <value>Description</value>
  </data>
  <!-- COLUMN 2 -->
  <data name="CusTargetPrice" xml:space="preserve">
    <value>Customer Target Price</value>
  </data>
  <data name="Currency" xml:space="preserve">
    <value>Currency</value>
  </data>
  <data name="CusDateRequired" xml:space="preserve">
    <value>Customer Date Required</value>
  </data>
  <data name="Closed" xml:space="preserve">
    <value>Closed</value>
  </data>
  <data name="Reason1" xml:space="preserve">
    <value>Reason</value>
  </data>
  <data name="Usage" xml:space="preserve">
    <value>Usage</value>
  </data>
  <data name="BOM" xml:space="preserve">
    <value>BOM</value>
  </data>
  <data name="BOMName" xml:space="preserve">
    <value>BOM Name</value>
  </data>
  <data name="NoteToCus" xml:space="preserve">
    <value>Notes to customer</value>
  </data>
  <data name="InternalNote" xml:space="preserve">
    <value>Internal notes</value>
  </data>
  <data name="PartialQuantityAcceptable" xml:space="preserve">
    <value>Partial Quantity Acceptable</value>
  </data>
  <data name="PriceRequest" xml:space="preserve">
    <value>Price Request</value>
  </data>
  <data name="LyticaManufacturerRef" xml:space="preserve">
    <value>Lytica Manufacturer Reference</value>
  </data>
  <data name="IHSManufacturer" xml:space="preserve">
    <value>IHS Manufacturer</value>
  </data>
  <data name="AVGPrice" xml:space="preserve">
    <value>Avg. Price (50th Percentile)</value>
  </data>
  <data name="TargetPrice" xml:space="preserve">
    <value>Target Price (70th Percentile)</value>
  </data>
  <data name="MarketLeading" xml:space="preserve">
    <value>Market Leading</value>
  </data>
  <!-- COLUMN 3 -->
  <data name="RefurbsAcceptable" xml:space="preserve">
    <value>Refurbs Acceptable</value>
  </data>
  <data name="TestingRequired" xml:space="preserve">
    <value>Testing Required</value>
  </data>
  <data name="AltAccepted" xml:space="preserve">
    <value>Alternatives Accepted</value>
  </data>
  <data name="Bussiness" xml:space="preserve">
    <value>Regular/Repeat business</value>
  </data>
  <data name="CompBestOffer" xml:space="preserve">
    <value>Competitor Best offer</value>
  </data>
  <data name="CusDecisionDate" xml:space="preserve">
    <value>Customer Decision Date</value>
  </data>
  <data name="RFQClosingDate" xml:space="preserve">
    <value>RFQ Closing Date</value>
  </data>
  <data name="QuoteValidityRequired" xml:space="preserve">
    <value>Quote Validity Required</value>
  </data>
  <data name="Type" xml:space="preserve">
    <value>Type</value>
  </data>
  <data name="OrdToPlace" xml:space="preserve">
    <value>Order to Place</value>
  </data>
  <data name="ReqForTrace" xml:space="preserve">
    <value>Requirement for Traceability</value>
  </data>
  <data name="EstAnnualUsage" xml:space="preserve">
    <value>Estimated Annual Usage</value>
  </data>
  <data name="CustomerRefNo" xml:space="preserve">
    <value>Customer Ref No.</value>
  </data>
  <data name="NoBid" xml:space="preserve">
    <value>No-Bid</value>
  </data>
</root>